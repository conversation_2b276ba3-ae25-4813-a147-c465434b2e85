import{a as te}from"./chunk-EXGJLNXY.js";import{a as I}from"./chunk-WPPT3EJF.js";import{b as x}from"./chunk-2LL5MXLB.js";import{B as d,C as j,Db as Y,F as k,G as h,H as O,Hb as Q,Ib as X,K as a,L as r,M as m,P as F,Q as S,R as y,Xb as Z,Y as c,Yb as W,Z as u,_ as $,d as N,da as U,dc as b,fc as ee,i as z,jb as J,k as v,m as q,na as B,pa as G,r as P,ra as H,s as T,vb as V,wb as K}from"./chunk-E442IOFQ.js";import{a as L,b as R,g as l}from"./chunk-2R6CW7ES.js";var g=x("FirebaseMessaging",{web:()=>import("./chunk-CQTDALRQ.js").then(n=>new n.FirebaseMessagingWeb)});var C=x("LocalNotifications",{web:()=>import("./chunk-47AQ3RT6.js").then(n=>new n.LocalNotificationsWeb)});function re(n,E){if(n&1&&(a(0,"div",12)(1,"h3",13),m(2,"ion-icon",23),c(3," Location "),r(),a(4,"p",24),c(5),r()()),n&2){let e=y();d(5),u(e.notification.barangay)}}function ae(n,E){if(n&1){let e=F();a(0,"div",12)(1,"h3",13),m(2,"ion-icon",25),c(3," Affected Areas "),r(),a(4,"div",26)(5,"p",27),c(6),r(),a(7,"ion-button",28),S("click",function(){P(e);let i=y();return T(i.viewOnMap())}),m(8,"ion-icon",29),c(9," View on Map "),r()()()}if(n&2){let e=y();d(6),u(e.getAffectedAreasSummary())}}function ce(n,E){if(n&1&&(a(0,"div",12)(1,"h3",13),c(2,"Reference ID"),r(),a(3,"p",30),c(4),r()()),n&2){let e=y();d(4),u(e.notification.notification_id)}}function se(n,E){if(n&1){let e=F();a(0,"ion-button",31),S("click",function(){P(e);let i=y();return T(i.viewOnMap())}),m(1,"ion-icon",32),c(2," Navigate to Area "),r()}}var ie=(()=>{class n{constructor(e){this.modalController=e}ngOnInit(){console.log("Notification detail opened:",this.notification)}closeModal(){return l(this,null,function*(){yield this.modalController.dismiss()})}getCategoryDisplayName(e){switch(e.toLowerCase()){case"earthquake":return"Earthquake Alert";case"typhoon":return"Typhoon Warning";case"flood":return"Flood Alert";case"fire":return"Fire Emergency";case"emergency":return"Emergency Alert";case"evacuation":return"Evacuation Notice";case"general":return"General Announcement";case"announcement":return"Public Announcement";default:return"Alert"}}getCategoryIcon(e){switch(e.toLowerCase()){case"earthquake":return"earth-outline";case"typhoon":return"thunderstorm-outline";case"flood":return"water-outline";case"fire":return"flame-outline";case"emergency":return"warning-outline";case"evacuation":return"walk-outline";case"general":return"megaphone-outline";case"announcement":return"chatbubble-outline";default:return"alert-circle-outline"}}getCategoryColor(e){switch(e.toLowerCase()){case"earthquake":return"#FFA500";case"typhoon":return"#008000";case"flood":return"#0066CC";case"fire":return"#FF0000";case"emergency":return"#FF0000";case"evacuation":return"#FF6600";case"general":return"#666666";case"announcement":return"#0066CC";default:return"#666666"}}getSeverityDisplayName(e){switch(e.toLowerCase()){case"high":return"High Priority";case"medium":return"Medium Priority";case"low":return"Low Priority";default:return"Normal Priority"}}getSeverityColor(e){switch(e.toLowerCase()){case"high":return"#FF0000";case"medium":return"#FFA500";case"low":return"#008000";default:return"#666666"}}getFormattedTimestamp(e){try{return new Date(e).toLocaleString()}catch{return e}}getAffectedAreasSummary(){if(!this.notification.affected_areas||!Array.isArray(this.notification.affected_areas))return"No specific areas defined";let e=this.notification.affected_areas.length;return e===1?"1 area affected":`${e} areas affected`}hasMapData(){return!!(this.notification.affected_areas&&Array.isArray(this.notification.affected_areas)&&this.notification.affected_areas.length>0)}viewOnMap(){console.log("View on map clicked for areas:",this.notification.affected_areas)}static{this.\u0275fac=function(t){return new(t||n)(j(b))}}static{this.\u0275cmp=q({type:n,selectors:[["app-notification-detail"]],inputs:{notification:"notification"},standalone:!0,features:[U],decls:42,vars:14,consts:[["slot","end"],[3,"click"],["name","close"],[1,"notification-detail-content"],[1,"notification-detail-container"],[1,"category-header"],[1,"category-info"],[1,"category-icon",3,"name"],[1,"category-text"],[1,"category-title"],[1,"severity-badge"],[1,"notification-content"],[1,"content-section"],[1,"section-title"],[1,"notification-title"],[1,"notification-message"],["class","content-section",4,"ngIf"],["name","time-outline",1,"section-icon"],[1,"timestamp"],[1,"action-buttons"],["expand","block","fill","solid","color","primary",3,"click"],["name","checkmark","slot","start"],["expand","block","fill","outline","color","medium",3,"click",4,"ngIf"],["name","location-outline",1,"section-icon"],[1,"location-info"],["name","map-outline",1,"section-icon"],[1,"affected-areas-info"],[1,"areas-summary"],["fill","outline","size","small",1,"view-map-button",3,"click"],["name","map","slot","start"],[1,"notification-id"],["expand","block","fill","outline","color","medium",3,"click"],["name","navigate","slot","start"]],template:function(t,i){t&1&&(a(0,"ion-header")(1,"ion-toolbar")(2,"ion-title"),c(3,"Notification Details"),r(),a(4,"ion-buttons",0)(5,"ion-button",1),S("click",function(){return i.closeModal()}),m(6,"ion-icon",2),r()()()(),a(7,"ion-content",3)(8,"div",4)(9,"div",5)(10,"div",6),m(11,"ion-icon",7),a(12,"div",8)(13,"h2",9),c(14),r(),a(15,"p",10),c(16),r()()()(),a(17,"div",11)(18,"div",12)(19,"h3",13),c(20,"Alert Title"),r(),a(21,"p",14),c(22),r()(),a(23,"div",12)(24,"h3",13),c(25,"Message"),r(),a(26,"p",15),c(27),r()(),k(28,re,6,1,"div",16)(29,ae,10,1,"div",16),a(30,"div",12)(31,"h3",13),m(32,"ion-icon",17),c(33," Received "),r(),a(34,"p",18),c(35),r()(),k(36,ce,5,1,"div",16),r(),a(37,"div",19)(38,"ion-button",20),S("click",function(){return i.closeModal()}),m(39,"ion-icon",21),c(40," Mark as Read "),r(),k(41,se,3,0,"ion-button",22),r()()()),t&2&&(d(9),O("background-color",i.getCategoryColor(i.notification.category)),d(2),h("name",i.getCategoryIcon(i.notification.category)),d(3),u(i.getCategoryDisplayName(i.notification.category)),d(),O("background-color",i.getSeverityColor(i.notification.severity)),d(),$(" ",i.getSeverityDisplayName(i.notification.severity)," "),d(6),u(i.notification.title),d(5),u(i.notification.body),d(),h("ngIf",i.notification.barangay),d(),h("ngIf",i.hasMapData()),d(6),u(i.getFormattedTimestamp(i.notification.timestamp)),d(),h("ngIf",i.notification.notification_id),d(5),h("ngIf",i.hasMapData()))},dependencies:[ee,V,K,Y,Q,X,Z,W,G,B],styles:['@charset "UTF-8";.notification-detail-content[_ngcontent-%COMP%]{--background: #f8f9fa}.notification-detail-container[_ngcontent-%COMP%]{padding:0;min-height:100%}.category-header[_ngcontent-%COMP%]{padding:20px;color:#fff;position:relative;background:linear-gradient(135deg,var(--ion-color-primary),var(--ion-color-primary-shade))}.category-header[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-10px;left:0;right:0;height:20px;background:#f8f9fa;border-radius:20px 20px 0 0}.category-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:15px}.category-icon[_ngcontent-%COMP%]{font-size:2.5rem;color:#fff}.category-text[_ngcontent-%COMP%]{flex:1}.category-title[_ngcontent-%COMP%]{margin:0 0 8px;font-size:1.4rem;font-weight:600;color:#fff}.severity-badge[_ngcontent-%COMP%]{display:inline-block;padding:4px 12px;border-radius:12px;font-size:.8rem;font-weight:600;color:#fff;margin:0}.notification-content[_ngcontent-%COMP%]{padding:20px;background:#fff;margin:0 16px 16px;border-radius:12px;box-shadow:0 2px 8px #0000001a}.content-section[_ngcontent-%COMP%]{margin-bottom:24px}.content-section[_ngcontent-%COMP%]:last-child{margin-bottom:0}.section-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin:0 0 8px;font-size:1rem;font-weight:600;color:var(--ion-color-dark)}.section-icon[_ngcontent-%COMP%]{font-size:1.1rem;color:var(--ion-color-medium)}.notification-title[_ngcontent-%COMP%]{margin:0;font-size:1.2rem;font-weight:600;color:var(--ion-color-dark);line-height:1.4}.notification-message[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:var(--ion-color-dark);line-height:1.5;white-space:pre-wrap}.location-info[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:var(--ion-color-dark);display:flex;align-items:center;gap:8px}.location-info[_ngcontent-%COMP%]:before{content:"\\1f4cd";font-size:1.1rem}.affected-areas-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.areas-summary[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:var(--ion-color-dark);display:flex;align-items:center;gap:8px}.areas-summary[_ngcontent-%COMP%]:before{content:"\\1f5fa\\fe0f";font-size:1.1rem}.view-map-button[_ngcontent-%COMP%]{align-self:flex-start;--border-radius: 8px}.timestamp[_ngcontent-%COMP%]{margin:0;font-size:.95rem;color:var(--ion-color-medium)}.notification-id[_ngcontent-%COMP%]{margin:0;font-size:.85rem;color:var(--ion-color-medium);font-family:monospace;background:var(--ion-color-light);padding:8px;border-radius:6px}.action-buttons[_ngcontent-%COMP%]{padding:16px;display:flex;flex-direction:column;gap:12px}.action-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 12px;font-weight:600}@media (max-width: 768px){.category-header[_ngcontent-%COMP%]{padding:16px}.category-title[_ngcontent-%COMP%]{font-size:1.2rem}.category-icon[_ngcontent-%COMP%]{font-size:2rem}.notification-content[_ngcontent-%COMP%]{margin:0 12px 12px;padding:16px}}@media (prefers-color-scheme: dark){.notification-detail-content[_ngcontent-%COMP%]{--background: #1a1a1a}.notification-content[_ngcontent-%COMP%]{background:#2a2a2a;color:#fff}.section-title[_ngcontent-%COMP%], .notification-title[_ngcontent-%COMP%], .notification-message[_ngcontent-%COMP%], .location-info[_ngcontent-%COMP%], .areas-summary[_ngcontent-%COMP%]{color:#fff}.notification-id[_ngcontent-%COMP%]{background:#3a3a3a;color:#ccc}}']})}}return n})();var Ne=(()=>{class n{constructor(e,t,i,o){this.platform=e,this.http=t,this.modalController=i,this.emergencyOverlay=o,this.fcmToken="",this.isInitialized=!1}initializeFCM(){return l(this,null,function*(){try{this.platform.is("capacitor")?(yield this.requestPermissions(),yield this.getFCMToken(),this.listenForTokenRefresh(),this.listenForMessages(),this.listenForLocalNotificationActions(),this.isInitialized=!0,console.log("FCM initialized successfully")):console.log("FCM not available on this platform")}catch(e){console.error("Error initializing FCM:",e),this.isInitialized=!1}})}requestPermissions(){return l(this,null,function*(){try{let e=yield g.requestPermissions();console.log("FCM permissions result:",e),e.receive==="granted"?console.log("FCM permissions granted"):console.warn("FCM permissions denied")}catch(e){console.error("Error requesting FCM permissions:",e)}})}getFCMToken(){return l(this,null,function*(){try{let e=yield g.getToken();return this.fcmToken=e.token,console.log("FCM Token:",this.fcmToken),yield this.registerTokenWithBackend(this.fcmToken),this.fcmToken}catch(e){return console.error("Error getting FCM token:",e),""}})}listenForTokenRefresh(){g.addListener("tokenReceived",e=>l(this,null,function*(){console.log("FCM token refreshed:",e.token),this.fcmToken=e.token,yield this.registerTokenWithBackend(e.token)}))}listenForMessages(){g.addListener("notificationReceived",e=>{console.log("\u{1F4F1} FCM notification received:",{notification:e.notification,timestamp:new Date().toISOString()}),this.isEmergencyNotification(e.notification)?this.handleEmergencyNotification(e.notification):this.showLocalNotification(e.notification)}),g.addListener("notificationActionPerformed",e=>{console.log("\u{1F4F1} FCM notification action performed:",{action:e,timestamp:new Date().toISOString()}),this.handleNotificationAction(e)})}listenForLocalNotificationActions(){C.addListener("localNotificationActionPerformed",e=>{console.log("Local notification action performed:",{action:e,timestamp:new Date().toISOString()}),this.handleNotificationAction(e)})}isEmergencyNotification(e){let t=e.data||{},i=t.category?.toLowerCase()||"",o=t.severity?.toLowerCase()||"";return["earthquake","flood","typhoon","fire","landslide"].includes(i)||o==="high"||o==="critical"||t.emergency==="true"||t.emergency===!0}handleEmergencyNotification(e){return l(this,null,function*(){try{console.log("\u{1F6A8} EMERGENCY NOTIFICATION RECEIVED:",e);let t=e.data||{},i={id:t.notification_id||`emergency_${Date.now()}`,title:e.title||"Emergency Alert",message:e.body||"Emergency notification received",category:this.mapToEmergencyCategory(t.category||"General"),severity:this.mapToEmergencySeverity(t.severity||"medium"),timestamp:new Date().toISOString(),data:t};yield this.emergencyOverlay.showEmergencyNotification(i)}catch(t){console.error("Error handling emergency notification:",t),this.showLocalNotification(e)}})}mapToEmergencyCategory(e){return{earthquake:"Earthquake",flood:"Flood",typhoon:"Typhoon",fire:"Fire",landslide:"Landslide",general:"General",emergency:"General"}[e.toLowerCase()]||"General"}mapToEmergencySeverity(e){return{low:"low",medium:"medium",high:"high",critical:"critical",urgent:"critical"}[e.toLowerCase()]||"medium"}showLocalNotification(e){return l(this,null,function*(){try{console.log("\uFFFD [FOREGROUND] Attempting to show notification:",JSON.stringify(e,null,2));let t=yield C.checkPermissions();if(console.log("\u{1F4CB} [PERMISSIONS] Current status:",JSON.stringify(t,null,2)),t.display!=="granted"){console.log("\u{1F512} [PERMISSIONS] Requesting notification permissions...");let D=yield C.requestPermissions();if(console.log("\u{1F4DD} [PERMISSIONS] Request result:",JSON.stringify(D,null,2)),D.display!=="granted"){console.error("\u274C [PERMISSIONS] Notification permissions denied - cannot show foreground notifications"),console.error("\u274C [PERMISSIONS] User needs to enable notifications in Android settings");return}}let i=this.generateUniqueNotificationId(),o=e.data||{},s=o.category||"general",f=o.severity||"medium",p=o.affected_areas?this.parseAffectedAreas(o.affected_areas):null,M=o.barangay||"",_=e.title||"New Alert",w=e.body||"You have received a new notification";this.isRichTitle(_)||(_=this.createRichTitle(_,s,f)),this.isRichBody(w)||(w=this.createRichBody(w,p,M,f)),console.log("\u{1F4E4} [SCHEDULING] Preparing local notification:",{id:i,title:_,body:w,category:s,severity:f,originalNotification:e});let A={title:_,body:w,id:i,schedule:{at:new Date(Date.now()+500)},sound:this.getNotificationSound(f),attachments:[],actionTypeId:"",extra:R(L({},o),{original_title:e.title,original_body:e.body,notification_id:o.notification_id,category:s,severity:f,affected_areas:p,barangay:M,timestamp:new Date().toISOString()})};console.log("\u{1F4E4} [SCHEDULING] Full notification payload:",JSON.stringify(A,null,2)),yield C.schedule({notifications:[A]}),console.log("\u2705 [SUCCESS] Local notification scheduled successfully with ID:",i),console.log("\u2705 [SUCCESS] Notification should appear in Android notification panel")}catch(t){console.error("\u274C [ERROR] Failed to show local notification:",t),console.error("\u274C [ERROR] Error details:",JSON.stringify(t,null,2));try{console.log("\u{1F504} [FALLBACK] Attempting immediate notification..."),yield C.schedule({notifications:[{title:e.title||"New Alert",body:e.body||"You have a new notification",id:Date.now(),schedule:{at:new Date(Date.now()+100)},sound:"default"}]}),console.log("\u2705 [FALLBACK] Simple notification scheduled")}catch(i){console.error("\u274C [FALLBACK] Simple notification also failed:",i),console.error("\u274C [FALLBACK] This indicates a deeper issue with local notifications on this device")}}})}generateUniqueNotificationId(){let e=Date.now(),t=Math.floor(Math.random()*1e3);return parseInt(`${e}${t}`)}createRichTitle(e,t,i){let o=this.getCategoryEmoji(t),s=this.getSeverityText(i);return`${o} ${s}: ${e}`}createRichBody(e,t,i,o){let s=e;if(o){let p=this.getPriorityIcon(o),M=this.getPriorityDescription(o);s+=`
${p} Priority: ${M}`}let f=this.formatAffectedAreas(t);return f?s+=`
\u{1F5FA}\uFE0F Affected Area/s: ${f}`:i&&i!=="All Areas"&&(s+=`
\uFFFD Area: ${i}`),s}getCategoryEmoji(e){switch(e.toLowerCase()){case"earthquake":return"\u{1F30D}";case"typhoon":return"\u{1F32A}\uFE0F";case"flood":return"\u{1F30A}";case"fire":return"\u{1F525}";case"emergency":return"\u{1F6A8}";case"evacuation":return"\u{1F3C3}\u200D\u2642\uFE0F";case"general":return"\u{1F4E2}";case"announcement":return"\u{1F4E3}";default:return"\u26A0\uFE0F"}}getSeverityText(e){switch(e.toLowerCase()){case"high":return"URGENT";case"medium":return"ALERT";case"low":return"INFO";default:return"NOTICE"}}getPriorityIcon(e){switch(e.toLowerCase()){case"high":return"\u{1F534}";case"medium":return"\u{1F7E1}";case"low":return"\u{1F7E2}";default:return"\u{1F535}"}}getPriorityDescription(e){switch(e.toLowerCase()){case"high":return"Critical - Immediate Action Required";case"medium":return"High - Action Required Soon";case"low":return"Normal - For Your Information";default:return"Standard - General Notice"}}getNotificationSound(e){switch(e.toLowerCase()){case"high":return"emergency.wav";case"medium":return"alert.wav";case"low":return"beep.wav";default:return"default"}}parseAffectedAreas(e){try{return JSON.parse(e)}catch(t){return console.error("Error parsing affected areas:",t),null}}formatAffectedAreas(e){if(!e)return null;try{let t=[];if(typeof e=="string")t=JSON.parse(e);else if(Array.isArray(e))t=e;else return null;if(!Array.isArray(t)||t.length===0)return null;let i=[];for(let o of t)typeof o=="object"&&o.name?i.push(o.name):typeof o=="string"&&i.push(o);if(i.length===0)return null;if(i.length===1)return i[0];if(i.length===2)return i.join(" and ");{let o=i.pop();return i.join(", ")+", and "+o}}catch(t){return console.error("Error formatting affected areas:",t),null}}isRichTitle(e){return/[🌍🌪️🌊🔥🚨🏃‍♂️📢📣⚠️]/.test(e)}isRichBody(e){return e.includes("\uFFFD Priority:")||e.includes("\u{1F7E1} Priority:")||e.includes("\u{1F7E2} Priority:")||e.includes("\uFFFD Priority:")}handleNotificationAction(e){return l(this,null,function*(){console.log("Handling notification action:",e);let t=e.notification?.extra||e.notification?.data||{};t&&(yield this.showNotificationDetail(t))})}showNotificationDetail(e){return l(this,null,function*(){try{yield(yield this.modalController.create({component:ie,componentProps:{notification:{id:e.notification_id||Date.now(),title:e.title||"Notification",body:e.body||"No message content",category:e.category||"general",severity:e.severity||"medium",barangay:e.barangay||"",affected_areas:e.affected_areas||null,timestamp:e.timestamp||new Date().toISOString(),notification_id:e.notification_id||""}},cssClass:"notification-detail-modal"})).present()}catch(t){console.error("Error showing notification detail:",t)}})}registerTokenWithBackend(e){return l(this,null,function*(){try{let t=this.getCurrentUserId(),i={token:e,device_type:"android",project_id:I.firebase.projectId,user_id:t};console.log("Registering FCM token with backend:",{token:e.substring(0,20)+"...",device_type:i.device_type,project_id:i.project_id,user_id:i.user_id});let o=`${I.apiUrl}/device-token`;try{let s=yield N(this.http.post(o,i));console.log("FCM token registered successfully:",s),localStorage.setItem("fcm_token",e),localStorage.setItem("fcm_token_registered","true");return}catch(s){if(console.error("Error registering token with public endpoint:",s),t)try{let f=`${I.apiUrl}/device-token/register`,p=yield N(this.http.post(f,i));console.log("FCM token registered with protected endpoint:",p),localStorage.setItem("fcm_token",e),localStorage.setItem("fcm_token_registered","true");return}catch(f){console.error("Error registering token with protected endpoint:",f)}throw localStorage.setItem("fcm_token",e),localStorage.setItem("fcm_token_registered","false"),s}}catch(t){console.error("Error registering FCM token with backend:",t),localStorage.setItem("fcm_token",e),localStorage.setItem("fcm_token_registered","false")}})}getCurrentUserId(){let e=["user","currentUser","authUser","userData"];for(let t of e){let i=localStorage.getItem(t);if(i)try{let o=JSON.parse(i);if(o&&(o.id||o.user_id||o.userId))return o.id||o.user_id||o.userId}catch(o){console.error(`Error parsing ${t} data:`,o)}}return console.log("No authenticated user found, registering token anonymously"),null}retryTokenRegistration(){return l(this,null,function*(){let e=localStorage.getItem("fcm_token"),t=localStorage.getItem("fcm_token_registered");e&&t==="false"&&(console.log("Retrying FCM token registration..."),yield this.registerTokenWithBackend(e))})}getCurrentToken(){if(this.fcmToken)return this.fcmToken;let e=localStorage.getItem("fcm_token");return e?(this.fcmToken=e,e):""}subscribeToTopic(e){return l(this,null,function*(){try{yield g.subscribeToTopic({topic:e}),console.log(`Subscribed to topic: ${e}`)}catch(t){console.error(`Error subscribing to topic ${e}:`,t)}})}unsubscribeFromTopic(e){return l(this,null,function*(){try{yield g.unsubscribeFromTopic({topic:e}),console.log(`Unsubscribed from topic: ${e}`)}catch(t){console.error(`Error unsubscribing from topic ${e}:`,t)}})}deleteToken(){return l(this,null,function*(){try{yield g.deleteToken(),this.fcmToken="",localStorage.removeItem("fcm_token"),console.log("FCM token deleted")}catch(e){console.error("Error deleting FCM token:",e)}})}getServiceStatus(){return{isInitialized:this.isInitialized,hasToken:!!this.fcmToken,token:this.fcmToken?this.fcmToken.substring(0,20)+"...":"No token",timestamp:new Date().toISOString()}}static{this.\u0275fac=function(t){return new(t||n)(v(J),v(H),v(b),v(te))}}static{this.\u0275prov=z({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();export{Ne as a};
