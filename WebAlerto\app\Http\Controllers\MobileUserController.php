<?php

namespace App\Http\Controllers;

use App\Models\MobileUser;
use Illuminate\Http\Request;

class MobileUserController extends Controller
{
    // Get all users
    public function index()
    {
        return response()->json(MobileUser::all());
    }

    // Store a new user
    public function store(Request $request)
    {
        $validated = $request->validate([
            'full_name' => 'required|string',
            'mobile_number' => 'required|string|max:11',
            'age' => 'required|integer',
            'gender' => 'required|string',
            'address' => 'required|string',
        ]);

        // Automatically set the user_id from the authenticated user
        $validated['user_id'] = $request->user()->id;

        $user = MobileUser::create($validated);
        return response()->json($user, 201);
    }

    // Show a single user
    public function show($id)
    {
        $user = MobileUser::findOrFail($id);
        return response()->json($user);
    }

    // Update a user
    public function update(Request $request, $id)
    {
        $user = MobileUser::findOrFail($id);
        $validated = $request->validate([
            'full_name' => 'sometimes|required|string',
            'mobile_number' => 'sometimes|required|string|max:11',
            'age' => 'sometimes|required|integer',
            'gender' => 'sometimes|required|string',
            'address' => 'sometimes|required|string',
        ]);
        $user->update($validated);
        return response()->json($user);
    }

    // Delete a user
    public function destroy($id)
    {
        $user = MobileUser::findOrFail($id);
        $user->delete();
        return response()->json(['message' => 'User deleted']);
    }
}