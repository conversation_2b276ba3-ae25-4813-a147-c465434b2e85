import{C as l,Db as b,K as o,L as i,M as s,Q as r,Y as a,da as m,fc as h,ib as u,m as c,pa as p,vb as f,ya as d}from"./chunk-E442IOFQ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import"./chunk-2R6CW7ES.js";var v=(()=>{class n{constructor(t){this.router=t}ngOnInit(){this.checkAuthenticationStatus()}checkAuthenticationStatus(){let t=localStorage.getItem("token"),e=localStorage.getItem("onboardingComplete");console.log("\u{1F50D} Intro page - Auth status check:",{hasToken:!!t,onboardingComplete:e==="true"}),t&&e==="true"?(console.log("\u2705 User authenticated & onboarded \u2192 navigating to tabs/home"),this.router.navigate(["/tabs/home"])):t&&e!=="true"?(console.log("\u2705 User authenticated but not onboarded \u2192 navigating to welcome"),this.router.navigate(["/welcome"])):!t&&e==="true"&&(console.log("\u{1F511} User not authenticated but has onboarded before \u2192 navigating to login"),this.router.navigate(["/login"]))}goToLogin(){this.router.navigate(["/login"])}goToRegister(){this.router.navigate(["/register"])}static{this.\u0275fac=function(e){return new(e||n)(l(d))}}static{this.\u0275cmp=c({type:n,selectors:[["app-intro"]],standalone:!0,features:[m],decls:9,vars:0,consts:[[1,"intro-bg"],[1,"intro-wrapper"],["src","assets/ALERTO.png"],[1,"button-container"],["expand","block",1,"login-btn",3,"click"],["expand","block","fill","outline",1,"signup-btn",3,"click"]],template:function(e,g){e&1&&(o(0,"ion-content",0)(1,"div",1)(2,"div"),s(3,"img",2),i(),o(4,"div",3)(5,"ion-button",4),r("click",function(){return g.goToLogin()}),a(6," Log In "),i(),o(7,"ion-button",5),r("click",function(){return g.goToRegister()}),a(8," Sign Up "),i()()()())},dependencies:[h,f,b,p,u],styles:[".intro-bg[_ngcontent-%COMP%]{--background: white;display:flex;align-items:center;justify-content:center;min-height:100vh}.intro-wrapper[_ngcontent-%COMP%]{width:100%;max-width:350px;padding:2rem;margin:0 auto;display:flex;flex-direction:column;align-items:flex-start;text-align:left}.logo-container[_ngcontent-%COMP%]{margin-bottom:3rem;align-self:center}.main-icon[_ngcontent-%COMP%]{width:120px;height:120px;border-radius:50%;background:#fff3;padding:20px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);box-shadow:0 8px 32px #0000001a}.title-container[_ngcontent-%COMP%]{margin-bottom:3rem;align-self:center}.app-title[_ngcontent-%COMP%]{font-size:2.2rem;font-weight:700;color:#000;margin:0 0 .5rem;text-shadow:none;letter-spacing:-.5px}.app-subtitle[_ngcontent-%COMP%]{font-size:1.1rem;color:#ffffffe6;margin:0;font-weight:300}.button-container[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column;gap:1rem}.login-btn[_ngcontent-%COMP%]{--background: #007bff;--color: white;--border-radius: 25px;--box-shadow: 0 4px 12px rgba(0, 0, 0, .15);font-weight:600;font-size:1.1rem;height:50px;width:100%;margin-top:1rem}.signup-btn[_ngcontent-%COMP%]{--color: #007bff;--border-color: #007bff;--border-radius: 25px;--box-shadow: 0 4px 12px rgba(0, 0, 0, .15);font-weight:600;font-size:1.1rem;height:50px;width:100%}.signup-btn[_ngcontent-%COMP%]:hover{--background: rgba(255, 255, 255, .1)}@media (max-height: 600px){.intro-wrapper[_ngcontent-%COMP%]{padding:1rem}.logo-container[_ngcontent-%COMP%]{margin-bottom:2rem}.main-icon[_ngcontent-%COMP%]{width:80px;height:80px;padding:15px}.title-container[_ngcontent-%COMP%]{margin-bottom:2rem}.app-title[_ngcontent-%COMP%]{font-size:2rem}}"]})}}return n})();export{v as IntroPage};
