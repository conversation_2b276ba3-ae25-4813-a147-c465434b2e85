#flood-map {
  height: 100%;
  width: 100%;
  z-index: 1;
}

.map-controls {
  position: absolute;
  top: 80px;
  right: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-btn {
  --background: rgba(255, 255, 255, 0.95);
  --color: #3880ff;
  --border-radius: 12px;
  width: 50px;
  height: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(56, 128, 255, 0.2);

  &:hover {
    --background: rgba(56, 128, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
}

.control-icon {
  width: 28px;
  height: 28px;
  object-fit: contain;
}

// All Centers Panel
.all-centers-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 380px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
  z-index: 1500;
  transition: right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;

  &.show {
    right: 0;
  }

  .panel-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow: hidden;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid var(--ion-color-primary-tint);

    .header-info {
      flex: 1;

      h3 {
        margin: 0 0 4px 0;
        font-size: 20px;
        font-weight: 700;
        color: var(--ion-color-primary);
        line-height: 1.2;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: var(--ion-color-medium);
        font-weight: 500;
      }
    }

    ion-button {
      --color: var(--ion-color-medium);
      margin: 0;
    }
  }

  .centers-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 4px;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: var(--ion-color-light);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--ion-color-primary-tint);
      border-radius: 2px;
    }
  }

  .center-item {
    display: flex;
    align-items: center;
    padding: 16px;
    margin-bottom: 12px;
    background: white;
    border-radius: 12px;
    border: 1px solid var(--ion-color-light);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(56, 128, 255, 0.15);
      border-color: var(--ion-color-primary-tint);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .center-info {
      flex: 1;

      h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--ion-color-dark);
        line-height: 1.3;
      }

      .address {
        margin: 0 0 8px 0;
        font-size: 13px;
        color: var(--ion-color-medium);
        line-height: 1.4;
      }

      .center-details {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .distance, .capacity {
          font-size: 12px;
          font-weight: 500;
          padding: 2px 8px;
          border-radius: 8px;
          background: var(--ion-color-primary-tint);
          color: var(--ion-color-primary);
          width: fit-content;
        }
      }
    }

    .center-actions {
      margin-left: 12px;

      ion-icon {
        font-size: 18px;
        color: var(--ion-color-medium);
      }
    }
  }
}

.all-centers-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1400;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.show {
    opacity: 1;
    visibility: visible;
  }
}

// Header styling
ion-header {
  ion-toolbar {
    --background: #3880ff;
    --color: white;
    --border-width: 0;

    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      font-size: 18px;

      ion-icon {
        font-size: 20px;
      }
    }
  }
}

.floating-info {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 250px;

  ion-card {
    margin: 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  ion-card-content {
    padding: 12px;
  }

  .info-row {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--ion-color-primary);
    margin-bottom: 4px;

    ion-icon {
      font-size: 18px;
    }
  }

  .info-text {
    font-size: 12px;
    color: var(--ion-color-medium);
    line-height: 1.3;
  }
}

// Flood-specific styling
ion-toolbar {
  --background: var(--ion-color-primary);
  --color: white;
}

ion-title {
  font-weight: 600;
}

// Navigation Panel
.navigation-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 380px;
  height: 100vh;
  background: white;
  z-index: 2000;
  transition: right 0.3s ease-in-out;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);

  &.show {
    right: 0;
  }

  .panel-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    padding-top: 60px; // Account for status bar
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--ion-color-light);

    .center-info {
      flex: 1;

      h3 {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--ion-color-dark);
        line-height: 1.2;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: var(--ion-color-medium);
        line-height: 1.3;
      }
    }

    ion-button {
      --color: var(--ion-color-medium);
      margin: 0;
    }
  }

  .transport-options {
    flex: 1;

    .option-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      font-weight: 600;
      color: var(--ion-color-primary);

      ion-icon {
        font-size: 20px;
      }
    }

    .transport-buttons {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 24px;
    }

    .transport-btn {
      display: flex;
      align-items: center;
      padding: 16px;
      border: 2px solid var(--ion-color-light);
      border-radius: 12px;
      background: white;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        border-color: var(--ion-color-primary-tint);
        background: var(--ion-color-primary-tint);
      }

      &.active {
        border-color: var(--ion-color-primary);
        background: var(--ion-color-primary-tint);

        ion-icon {
          color: var(--ion-color-primary);
        }
      }

      ion-icon {
        font-size: 24px;
        margin-right: 12px;
        color: var(--ion-color-medium);
        transition: color 0.2s ease;
      }

      > span {
        font-size: 16px;
        font-weight: 500;
        color: var(--ion-color-dark);
        flex: 1;
      }

      .route-info {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 2px;

        .time {
          font-size: 16px;
          font-weight: 600;
          color: var(--ion-color-primary);
        }

        .distance {
          font-size: 12px;
          color: var(--ion-color-medium);
        }
      }
    }

    .start-navigation-btn {
      width: 100%;
      padding: 16px;
      background: var(--ion-color-primary);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      cursor: pointer;
      transition: background 0.2s ease;

      &:hover {
        background: var(--ion-color-primary-shade);
      }

      ion-icon {
        font-size: 20px;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .map-controls {
    top: 90px;
    left: 8px;
  }
}

// Pulsing markers for nearest centers (like all-maps)
:global(.pulsing-marker) {
  .pulse-container {
    position: relative;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .pulse {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    animation: pulse 2s infinite;
    opacity: 0.6;
  }

  .marker-icon {
    width: 30px;
    height: 30px;
    z-index: 2;
    position: relative;
  }

  .marker-label {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #0066CC;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    z-index: 3;
    border: 2px solid white;
  }

  @keyframes pulse {
    0% {
      transform: scale(0.8);
      opacity: 0.8;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.4;
    }
    100% {
      transform: scale(0.8);
      opacity: 0.8;
    }
  }
}

// Travel Mode Selector (like all-maps)
.travel-mode-selector {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  ion-segment {
    --background: transparent;
    min-height: 40px;
  }

  ion-segment-button {
    --color: var(--ion-color-medium);
    --color-checked: var(--ion-color-primary);
    --indicator-color: var(--ion-color-primary);
    min-height: 40px;

    ion-icon {
      font-size: 16px;
      margin-bottom: 2px;
    }

    ion-label {
      font-size: 12px;
      font-weight: 500;
    }
  }
}

// Route Information Display
.route-info {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
  max-width: 200px;

  ion-card {
    margin: 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  ion-card-content {
    padding: 12px;
  }

  .route-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--ion-color-primary);
    margin-bottom: 8px;

    ion-icon {
      font-size: 18px;
    }
  }

  .route-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .route-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--ion-color-dark);

    ion-icon {
      font-size: 16px;
      color: var(--ion-color-primary);
    }
  }
}

// Popup styling for flood centers
:global(.leaflet-popup-content) {
  .evacuation-popup {
    text-align: center;
    min-width: 200px;

    h3 {
      margin: 0 0 8px 0;
      color: var(--ion-color-primary);
      font-size: 16px;
      font-weight: 600;
    }

    p {
      margin: 4px 0;
      font-size: 14px;

      strong {
        color: var(--ion-color-dark);
      }
    }

    &.nearest-popup {
      h3 {
        color: #0066CC;
        font-size: 18px;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .map-controls {
    top: 70px;
    right: 8px;
  }

  .all-centers-panel {
    width: 100%;
    right: -100%;

    &.show {
      right: 0;
    }
  }

  .navigation-panel {
    width: 100%;
    right: -100%;

    &.show {
      right: 0;
    }
  }

  .route-footer {
    .footer-content {
      padding: 12px 16px;

      .route-summary {
        gap: 10px;

        .transport-icon {
          width: 36px;
          height: 36px;

          ion-icon {
            font-size: 18px;
          }
        }

        .route-details {
          .destination {
            font-size: 14px;
          }

          .route-info-footer {
            .time {
              font-size: 13px;
            }

            .distance {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}
