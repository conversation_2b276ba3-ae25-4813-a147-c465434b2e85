import{a as u}from"./chunk-2GT6F2KJ.js";import{b as L}from"./chunk-2LL5MXLB.js";import{A as w,B as s,C as b,F as h,G as l,Ib as R,K as a,L as r,M as d,Q as x,R as v,Y as g,Z as p,_ as M,bc as j,da as E,dc as C,ea as k,fa as S,fc as $,ga as I,i as O,jb as q,k as f,la as A,m as P,na as T,oa as D,pa as z,ya as F}from"./chunk-E442IOFQ.js";import{g as c}from"./chunk-2R6CW7ES.js";var _=L("Haptics",{web:()=>import("./chunk-JBEZ4JM7.js").then(o=>new o.HapticsWeb)});var V=(o,y)=>[o,y];function Y(o,y){if(o&1&&(a(0,"div",28)(1,"div",29)(2,"span",30),g(3),r()()()),o&2){let e=v();s(3),p(e.getFormattedTimeRemaining())}}function B(o,y){if(o&1&&(a(0,"div",31)(1,"div",32),d(2,"ion-icon",33),a(3,"span"),g(4,"IMMEDIATE ACTION REQUIRED"),r()(),a(5,"p",34),g(6),r()()),o&2){let e=v();s(6),p(e.getEmergencyInstructions())}}function U(o,y){o&1&&(a(0,"div",35),d(1,"div",36),a(2,"span",37),g(3,"CRITICAL EMERGENCY"),r()())}var G=(()=>{class o{constructor(e,n){this.modalController=e,this.animationController=n,this.timeRemaining=30}ngOnInit(){this.startCountdown(),this.startPulseAnimation()}ngOnDestroy(){this.clearCountdown(),this.stopPulseAnimation()}startCountdown(){this.countdownInterval=setInterval(()=>{this.timeRemaining--,this.timeRemaining<=0&&this.dismissModal("timeout")},1e3)}clearCountdown(){this.countdownInterval&&(clearInterval(this.countdownInterval),this.countdownInterval=null)}startPulseAnimation(){let e=document.querySelector(".emergency-alert-container");e&&(this.pulseAnimation=this.animationController.create().addElement(e).duration(1e3).iterations(1/0).keyframes([{offset:0,transform:"scale(1)",opacity:"1"},{offset:.5,transform:"scale(1.02)",opacity:"0.9"},{offset:1,transform:"scale(1)",opacity:"1"}]),this.pulseAnimation.play())}stopPulseAnimation(){this.pulseAnimation&&(this.pulseAnimation.stop(),this.pulseAnimation=null)}getDisasterIcon(){let e={Earthquake:"assets/earthquake.png",Flood:"assets/flood.png",Typhoon:"assets/icon/bagyo.png",Fire:"assets/icon/fire.jpg",Landslide:"assets/icon/lanslide.jpg",Others:"assets/otherdisasterIcon.png",General:"assets/emergency-icon.png"};return e[this.notification.category]||e.General}getSeverityClass(){return`severity-${this.notification.severity}`}getDisasterClass(){return`disaster-${this.notification.category.toLowerCase()}`}getFormattedTimeRemaining(){return`${this.timeRemaining}s`}viewMap(){return c(this,null,function*(){console.log("\u{1F6A8} Emergency Overlay: View Map button clicked for",this.notification.category),yield this.dismissModal("view_map")})}dismiss(){return c(this,null,function*(){console.log("\u{1F6A8} Emergency Overlay: Dismiss button clicked"),yield this.dismissModal("dismiss")})}dismissModal(e){return c(this,null,function*(){console.log(`\u{1F6A8} Emergency Overlay: Dismissing modal with action: ${e}`),this.clearCountdown(),this.stopPulseAnimation();let n={action:e,timestamp:new Date().toISOString()};console.log("\u{1F6A8} Emergency Overlay: Dismiss data:",n),yield this.modalController.dismiss(n)})}getFormattedMessage(){return this.notification.message}getSeverityText(){return{low:"Advisory",medium:"Warning",high:"Alert",critical:"CRITICAL EMERGENCY"}[this.notification.severity]||"Alert"}getActionButtonText(){return`View ${this.notification.category} Map & Routes`}isCritical(){return this.notification.severity==="critical"}getEmergencyInstructions(){let e={Earthquake:"Drop, Cover, and Hold On. Stay away from windows and heavy objects.",Flood:"Move to higher ground immediately. Avoid walking or driving through flood waters.",Typhoon:"Stay indoors. Secure loose objects and avoid windows.",Fire:"Evacuate immediately. Stay low to avoid smoke. Do not use elevators.",Landslide:"Move away from the slide area. Get to higher, stable ground.",General:"Follow emergency procedures and stay alert for further instructions."};return e[this.notification.category]||e.General}static{this.\u0275fac=function(n){return new(n||o)(b(C),b(j))}}static{this.\u0275cmp=P({type:o,selectors:[["app-emergency-overlay"]],inputs:{notification:"notification"},standalone:!0,features:[E],decls:39,vars:21,consts:[[1,"emergency-overlay-container"],[1,"emergency-alert-container",3,"ngClass"],[1,"emergency-header"],[1,"disaster-icon-container"],[1,"disaster-icon",3,"src","alt"],[1,"severity-badge",3,"ngClass"],[1,"emergency-title-container"],[1,"emergency-title"],["name","warning",1,"warning-icon"],[1,"disaster-type"],["class","countdown-container",4,"ngIf"],[1,"emergency-message-container"],[1,"emergency-message"],[1,"message-title"],[1,"message-content"],["class","emergency-instructions",4,"ngIf"],[1,"emergency-actions"],["type","button",1,"emergency-button","primary-action",3,"click","touchstart","ngClass"],["name","map",1,"button-icon"],[1,"button-text"],["name","chevron-forward",1,"button-arrow"],["type","button",1,"emergency-button","secondary-action",3,"click","touchstart"],["name","checkmark-circle",1,"button-icon"],[1,"emergency-footer"],[1,"timestamp"],["name","time",1,"time-icon"],["class","critical-indicator",4,"ngIf"],[1,"emergency-background-pattern",3,"ngClass"],[1,"countdown-container"],[1,"countdown-circle"],[1,"countdown-text"],[1,"emergency-instructions"],[1,"instructions-header"],["name","information-circle",1,"info-icon"],[1,"instructions-text"],[1,"critical-indicator"],[1,"critical-pulse"],[1,"critical-text"]],template:function(n,t){n&1&&(a(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3),d(4,"img",4),a(5,"div",5),g(6),r()(),a(7,"div",6)(8,"h1",7),d(9,"ion-icon",8),g(10," EMERGENCY ALERT "),r(),a(11,"h2",9),g(12),r()(),h(13,Y,4,1,"div",10),r(),a(14,"div",11)(15,"div",12)(16,"h3",13),g(17),r(),a(18,"p",14),g(19),r()(),h(20,B,7,1,"div",15),r(),a(21,"div",16)(22,"button",17),x("click",function(){return t.viewMap()})("touchstart",function(){return t.viewMap()}),d(23,"ion-icon",18),a(24,"span",19),g(25),r(),d(26,"ion-icon",20),r(),a(27,"button",21),x("click",function(){return t.dismiss()})("touchstart",function(){return t.dismiss()}),d(28,"ion-icon",22),a(29,"span",19),g(30,"I'm Safe - Dismiss"),r()()(),a(31,"div",23)(32,"div",24),d(33,"ion-icon",25),a(34,"span"),g(35),S(36,"date"),r()(),h(37,U,4,0,"div",26),r()(),d(38,"div",27),r()),n&2&&(s(),l("ngClass",k(18,V,t.getSeverityClass(),t.getDisasterClass())),s(3),l("src",t.getDisasterIcon(),w)("alt",t.notification.category+" icon"),s(),l("ngClass",t.getSeverityClass()),s(),M(" ",t.getSeverityText()," "),s(6),p(t.notification.category.toUpperCase()),s(),l("ngIf",t.timeRemaining>0),s(4),p(t.notification.title),s(2),p(t.getFormattedMessage()),s(),l("ngIf",t.isCritical()),s(2),l("ngClass",t.getDisasterClass()),s(3),p(t.getActionButtonText()),s(10),M("Alert issued: ",I(36,15,t.notification.timestamp,"short"),""),s(2),l("ngIf",t.isCritical()),s(),l("ngClass",t.getDisasterClass()))},dependencies:[z,A,T,D,$,R],styles:[".emergency-overlay-container[_ngcontent-%COMP%]{position:fixed;inset:0;z-index:99999;display:flex;align-items:center;justify-content:center;padding:20px;background:#000000f2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);animation:_ngcontent-%COMP%_emergencyFadeIn .3s ease-out}.emergency-alert-container[_ngcontent-%COMP%]{width:100%;max-width:400px;background:#fff;border-radius:20px;box-shadow:0 20px 60px #00000080;overflow:hidden;position:relative;animation:_ngcontent-%COMP%_emergencySlideIn .5s ease-out;border:4px solid}.emergency-header[_ngcontent-%COMP%]{padding:24px 20px 16px;text-align:center;position:relative;background:linear-gradient(135deg,#ffffff1a,#ffffff0d)}.disaster-icon-container[_ngcontent-%COMP%]{position:relative;display:inline-block;margin-bottom:16px}.disaster-icon[_ngcontent-%COMP%]{width:64px;height:64px;border-radius:50%;border:3px solid rgba(255,255,255,.3);object-fit:cover}.severity-badge[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;padding:4px 8px;border-radius:12px;font-size:10px;font-weight:700;text-transform:uppercase;letter-spacing:.5px;color:#fff;box-shadow:0 2px 8px #0000004d}.emergency-title-container[_ngcontent-%COMP%]{margin-bottom:12px}.emergency-title[_ngcontent-%COMP%]{font-size:18px;font-weight:800;color:#fff;margin:0 0 4px;text-transform:uppercase;letter-spacing:1px;display:flex;align-items:center;justify-content:center;gap:8px}.warning-icon[_ngcontent-%COMP%]{font-size:20px;animation:_ngcontent-%COMP%_warningPulse 1s infinite}.disaster-type[_ngcontent-%COMP%]{font-size:24px;font-weight:900;color:#fff;margin:0;text-shadow:0 2px 4px rgba(0,0,0,.3)}.countdown-container[_ngcontent-%COMP%]{position:absolute;top:16px;right:16px}.countdown-circle[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:#fff3;display:flex;align-items:center;justify-content:center;border:2px solid rgba(255,255,255,.4)}.countdown-text[_ngcontent-%COMP%]{font-size:12px;font-weight:700;color:#fff}.emergency-message-container[_ngcontent-%COMP%]{padding:20px;background:#fff}.emergency-message[_ngcontent-%COMP%]{margin-bottom:16px}.message-title[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#333;margin:0 0 8px}.message-content[_ngcontent-%COMP%]{font-size:16px;line-height:1.5;color:#555;margin:0}.emergency-instructions[_ngcontent-%COMP%]{background:#fff3cd;border:1px solid #ffeaa7;border-radius:12px;padding:16px;margin-top:16px}.instructions-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:700;color:#856404;margin-bottom:8px;font-size:14px}.info-icon[_ngcontent-%COMP%]{font-size:16px}.instructions-text[_ngcontent-%COMP%]{font-size:14px;color:#856404;margin:0;line-height:1.4}.emergency-actions[_ngcontent-%COMP%]{padding:0 20px 20px;background:#fff;display:flex;flex-direction:column;gap:12px;position:relative;z-index:999;pointer-events:auto}.emergency-button[_ngcontent-%COMP%]{width:100%;padding:16px 20px;border:none;border-radius:12px;font-size:16px;font-weight:600;display:flex;align-items:center;justify-content:space-between;cursor:pointer;transition:all .3s ease;position:relative;overflow:hidden;z-index:1000;pointer-events:auto;touch-action:manipulation;-webkit-tap-highlight-color:transparent}.primary-action[_ngcontent-%COMP%]{background:#28a745;color:#fff;box-shadow:0 4px 16px #28a7454d}.primary-action[_ngcontent-%COMP%]:hover{background:#218838;transform:translateY(-2px);box-shadow:0 6px 20px #28a74566}.secondary-action[_ngcontent-%COMP%]{background:#6c757d;color:#fff;box-shadow:0 4px 16px #6c757d4d}.secondary-action[_ngcontent-%COMP%]:hover{background:#5a6268;transform:translateY(-2px)}.button-icon[_ngcontent-%COMP%]{font-size:20px}.button-text[_ngcontent-%COMP%]{flex:1;text-align:center;margin:0 12px}.button-arrow[_ngcontent-%COMP%]{font-size:18px}.emergency-footer[_ngcontent-%COMP%]{padding:16px 20px;background:#0000000d;border-top:1px solid rgba(0,0,0,.1)}.timestamp[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;font-size:12px;color:#666;justify-content:center}.time-icon[_ngcontent-%COMP%]{font-size:14px}.critical-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px;margin-top:8px;padding:8px 16px;background:#dc3545;color:#fff;border-radius:20px;font-size:12px;font-weight:700;text-transform:uppercase;letter-spacing:.5px}.critical-pulse[_ngcontent-%COMP%]{width:8px;height:8px;background:#fff;border-radius:50%;animation:_ngcontent-%COMP%_criticalPulse 1s infinite}.emergency-background-pattern[_ngcontent-%COMP%]{position:absolute;inset:0;opacity:.1;background-size:30px 30px;background-image:linear-gradient(45deg,rgba(255,255,255,.1) 25%,transparent 25%),linear-gradient(-45deg,rgba(255,255,255,.1) 25%,transparent 25%),linear-gradient(45deg,transparent 75%,rgba(255,255,255,.1) 75%),linear-gradient(-45deg,transparent 75%,rgba(255,255,255,.1) 75%);animation:_ngcontent-%COMP%_patternMove 20s linear infinite}.disaster-earthquake[_ngcontent-%COMP%]{border-color:#ff8c00}.disaster-earthquake[_ngcontent-%COMP%]   .emergency-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff8c00,orange)}.disaster-earthquake[_ngcontent-%COMP%]   .severity-badge[_ngcontent-%COMP%]{background:#ff6b00}.disaster-earthquake[_ngcontent-%COMP%]   .primary-action[_ngcontent-%COMP%]{background:#ff8c00;box-shadow:0 4px 16px #ff8c004d}.disaster-earthquake[_ngcontent-%COMP%]   .primary-action[_ngcontent-%COMP%]:hover{background:#f70}.disaster-flood[_ngcontent-%COMP%]{border-color:#06c}.disaster-flood[_ngcontent-%COMP%]   .emergency-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#06c,#4a90e2)}.disaster-flood[_ngcontent-%COMP%]   .severity-badge[_ngcontent-%COMP%]{background:#0052a3}.disaster-flood[_ngcontent-%COMP%]   .primary-action[_ngcontent-%COMP%]{background:#06c;box-shadow:0 4px 16px #0066cc4d}.disaster-flood[_ngcontent-%COMP%]   .primary-action[_ngcontent-%COMP%]:hover{background:#0052a3}.disaster-typhoon[_ngcontent-%COMP%]{border-color:#228b22}.disaster-typhoon[_ngcontent-%COMP%]   .emergency-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#228b22,#32cd32)}.disaster-typhoon[_ngcontent-%COMP%]   .severity-badge[_ngcontent-%COMP%]{background:#1f7a1f}.disaster-typhoon[_ngcontent-%COMP%]   .primary-action[_ngcontent-%COMP%]{background:#228b22;box-shadow:0 4px 16px #228b224d}.disaster-typhoon[_ngcontent-%COMP%]   .primary-action[_ngcontent-%COMP%]:hover{background:#1f7a1f}.disaster-fire[_ngcontent-%COMP%]{border-color:#dc143c}.disaster-fire[_ngcontent-%COMP%]   .emergency-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#dc143c,#ff4500)}.disaster-fire[_ngcontent-%COMP%]   .severity-badge[_ngcontent-%COMP%]{background:#b91c3c}.disaster-fire[_ngcontent-%COMP%]   .primary-action[_ngcontent-%COMP%]{background:#dc143c;box-shadow:0 4px 16px #dc143c4d}.disaster-fire[_ngcontent-%COMP%]   .primary-action[_ngcontent-%COMP%]:hover{background:#b91c3c}.disaster-landslide[_ngcontent-%COMP%]{border-color:#8b4513}.disaster-landslide[_ngcontent-%COMP%]   .emergency-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#8b4513,sienna)}.disaster-landslide[_ngcontent-%COMP%]   .severity-badge[_ngcontent-%COMP%]{background:#654321}.disaster-landslide[_ngcontent-%COMP%]   .primary-action[_ngcontent-%COMP%]{background:#8b4513;box-shadow:0 4px 16px #8b45134d}.disaster-landslide[_ngcontent-%COMP%]   .primary-action[_ngcontent-%COMP%]:hover{background:#654321}.disaster-general[_ngcontent-%COMP%]{border-color:#666}.disaster-general[_ngcontent-%COMP%]   .emergency-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#666,#888)}.disaster-general[_ngcontent-%COMP%]   .severity-badge[_ngcontent-%COMP%]{background:#555}.disaster-general[_ngcontent-%COMP%]   .primary-action[_ngcontent-%COMP%]{background:#666;box-shadow:0 4px 16px #6666664d}.disaster-general[_ngcontent-%COMP%]   .primary-action[_ngcontent-%COMP%]:hover{background:#555}.severity-critical[_ngcontent-%COMP%]   .severity-badge[_ngcontent-%COMP%]{background:#dc3545!important;animation:_ngcontent-%COMP%_criticalBadgePulse 1s infinite}.severity-critical[_ngcontent-%COMP%]   .emergency-alert-container[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_criticalShake .5s infinite}.severity-high[_ngcontent-%COMP%]   .severity-badge[_ngcontent-%COMP%]{background:#fd7e14!important}.severity-medium[_ngcontent-%COMP%]   .severity-badge[_ngcontent-%COMP%]{background:#ffc107!important;color:#333!important}.severity-low[_ngcontent-%COMP%]   .severity-badge[_ngcontent-%COMP%]{background:#28a745!important}@keyframes _ngcontent-%COMP%_emergencyFadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_emergencySlideIn{0%{transform:translateY(-50px) scale(.9);opacity:0}to{transform:translateY(0) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_warningPulse{0%,to{opacity:1;transform:scale(1)}50%{opacity:.7;transform:scale(1.1)}}@keyframes _ngcontent-%COMP%_criticalPulse{0%,to{opacity:1;transform:scale(1)}50%{opacity:.5;transform:scale(1.2)}}@keyframes _ngcontent-%COMP%_criticalBadgePulse{0%,to{box-shadow:0 0 #dc3545b3}50%{box-shadow:0 0 0 8px #dc354500}}@keyframes _ngcontent-%COMP%_criticalShake{0%,to{transform:translate(0)}25%{transform:translate(-2px)}75%{transform:translate(2px)}}@keyframes _ngcontent-%COMP%_patternMove{0%{background-position:0 0}to{background-position:30px 30px}}@media (max-width: 480px){.emergency-overlay-container[_ngcontent-%COMP%]{padding:16px}.emergency-alert-container[_ngcontent-%COMP%]{max-width:100%}.disaster-icon[_ngcontent-%COMP%]{width:56px;height:56px}.emergency-title[_ngcontent-%COMP%]{font-size:16px}.disaster-type[_ngcontent-%COMP%]{font-size:20px}.message-title[_ngcontent-%COMP%]{font-size:16px}.message-content[_ngcontent-%COMP%]{font-size:14px}.emergency-button[_ngcontent-%COMP%]{padding:14px 16px;font-size:14px}}@media (prefers-contrast: high){.emergency-alert-container[_ngcontent-%COMP%]{border-width:6px}.emergency-button[_ngcontent-%COMP%]{border:2px solid rgba(255,255,255,.3)}}@media (prefers-reduced-motion: reduce){.emergency-alert-container[_ngcontent-%COMP%], .warning-icon[_ngcontent-%COMP%], .critical-pulse[_ngcontent-%COMP%], .severity-badge[_ngcontent-%COMP%], .emergency-background-pattern[_ngcontent-%COMP%]{animation:none!important}}"]})}}return o})();var ae=(()=>{class o{constructor(e,n,t){this.modalController=e,this.platform=n,this.router=t,this.currentModal=null,this.audioContext=null,this.emergencySounds={},this.isShowingEmergency=!1,this.initializeAudioContext(),this.preloadEmergencySounds()}initializeAudioContext(){try{this.audioContext=new(window.AudioContext||window.webkitAudioContext)}catch(e){console.warn("Audio context not supported:",e)}}preloadEmergencySounds(){let e={Earthquake:"assets/sounds/emergency-earthquake.mp3",Flood:"assets/sounds/emergency-flood.mp3",Typhoon:"assets/sounds/emergency-typhoon.mp3",Fire:"assets/sounds/emergency-fire.mp3",Landslide:"assets/sounds/emergency-landslide.mp3",General:"assets/sounds/emergency-general.mp3",critical:"assets/sounds/emergency-critical.mp3"};Object.keys(e).forEach(n=>{let t=new Audio(e[n]);t.preload="auto",t.loop=!0,t.volume=1,t.onerror=()=>{console.warn(`Emergency sound not found: ${e[n]}, using fallback`),t.src="assets/sounds/notification.mp3"},this.emergencySounds[n]=t})}showEmergencyNotification(e){return c(this,null,function*(){this.isShowingEmergency&&(yield this.dismissCurrentEmergency()),this.isShowingEmergency=!0;try{yield this.startEmergencyVibration(e.category,e.severity),yield this.playEmergencySound(e.category,e.severity),this.currentModal=yield this.modalController.create({component:G,componentProps:{notification:e},cssClass:`emergency-overlay ${e.category.toLowerCase()}-emergency ${e.severity}-severity`,backdropDismiss:!1,keyboardClose:!1,showBackdrop:!0,animated:!0,mode:"ios"}),this.currentModal.onDidDismiss().then(n=>{this.handleEmergencyDismissal(e,n.data)}),yield this.currentModal.present(),setTimeout(()=>{this.currentModal&&this.isShowingEmergency&&this.dismissCurrentEmergency()},3e4)}catch(n){console.error("Error showing emergency notification:",n),this.isShowingEmergency=!1}})}startEmergencyVibration(e,n){return c(this,null,function*(){if(!this.platform.is("capacitor")){if("vibrate"in navigator){let t=this.getVibrationPattern(e,n);navigator.vibrate(t);let i=setInterval(()=>{this.isShowingEmergency?navigator.vibrate(t):clearInterval(i)},3e3)}return}try{let t=this.getHapticPattern(n),i=setInterval(()=>c(this,null,function*(){this.isShowingEmergency?(yield _.impact({style:t}),n==="critical"&&setTimeout(()=>c(this,null,function*(){this.isShowingEmergency&&(yield _.impact({style:u.Heavy}))}),200)):clearInterval(i)}),1e3)}catch(t){if(console.warn("Haptics not available:",t),"vibrate"in navigator){let i=this.getVibrationPattern(e,n);navigator.vibrate(i)}}})}getVibrationPattern(e,n){let t={Earthquake:[300,100,300,100,300],Flood:[500,200,500],Typhoon:[200,100,200,100,200,100,200],Fire:[100,50,100,50,100,50,100,50,100],Landslide:[400,150,400,150,400],General:[250,100,250,100,250]},i=t[e]||t.General;return n==="critical"?i=i.map(m=>m*1.5):n==="high"&&(i=i.map(m=>m*1.2)),i}getHapticPattern(e){switch(e){case"critical":return u.Heavy;case"high":return u.Medium;case"medium":return u.Light;default:return u.Light}}playEmergencySound(e,n){return c(this,null,function*(){try{this.stopAllEmergencySounds();let t=n==="critical"?"critical":e,i=this.emergencySounds[t];if((!i||i.error)&&(i=this.emergencySounds.General),i){i.currentTime=0,i.volume=1;let m=i.play();m!==void 0&&(yield m,console.log(`Emergency sound playing: ${t}`))}}catch(t){console.warn("Error playing emergency sound:",t)}})}stopAllEmergencySounds(){Object.values(this.emergencySounds).forEach(e=>{e.paused||(e.pause(),e.currentTime=0)})}handleEmergencyDismissal(e,n){return c(this,null,function*(){this.isShowingEmergency=!1,this.stopAllEmergencySounds(),n?.action==="view_map"&&(yield this.navigateToDisasterMap(e.category))})}navigateToDisasterMap(e){return c(this,null,function*(){let t={Earthquake:"/tabs/earthquake-map",Flood:"/tabs/flood-map",Typhoon:"/tabs/typhoon-map",Fire:"/tabs/fire-map",Landslide:"/tabs/landslide-map",General:"/tabs/map"}[e]||"/tabs/map";try{console.log(`\u{1F6A8} Emergency navigation: Navigating to ${t} for ${e} disaster`),yield this.router.navigate([t],{queryParams:{emergency:!0,autoRoute:!0,timestamp:Date.now()}}),console.log(`\u2705 Successfully navigated to ${t}`)}catch(i){console.error("Error navigating to disaster map:",i);try{console.log("\u{1F504} Falling back to main map..."),yield this.router.navigate(["/tabs/map"],{queryParams:{emergency:!0,autoRoute:!0,timestamp:Date.now()}})}catch(m){console.error("Fallback navigation also failed:",m)}}})}dismissCurrentEmergency(){return c(this,null,function*(){if(this.currentModal){this.isShowingEmergency=!1,this.stopAllEmergencySounds();try{yield this.currentModal.dismiss(),this.currentModal=null}catch(e){console.warn("Error dismissing emergency modal:",e)}}})}isEmergencyShowing(){return this.isShowingEmergency}forceStopEmergency(){this.isShowingEmergency=!1,this.stopAllEmergencySounds(),this.currentModal&&(this.currentModal.dismiss(),this.currentModal=null)}testEarthquakeEmergency(){return c(this,null,function*(){let e={id:"test-earthquake-"+Date.now(),title:"EARTHQUAKE ALERT",message:"A magnitude 7.2 earthquake has been detected in your area. Take immediate safety precautions.",category:"Earthquake",severity:"high",timestamp:new Date().toISOString(),data:{magnitude:7.2,depth:"10km",location:"Test Location"}};console.log("\u{1F9EA} Testing earthquake emergency overlay..."),yield this.showEmergencyNotification(e)})}testEmergencyOverlay(e="Earthquake",n="high"){return c(this,null,function*(){let t={id:`test-${e.toLowerCase()}-${Date.now()}`,title:`${e.toUpperCase()} ALERT`,message:`A ${n} level ${e.toLowerCase()} emergency has been detected in your area. Take immediate safety precautions.`,category:e,severity:n,timestamp:new Date().toISOString(),data:{testMode:!0,location:"Test Location"}};console.log(`\u{1F9EA} Testing ${e} emergency overlay with ${n} severity...`),yield this.showEmergencyNotification(t)})}static{this.\u0275fac=function(n){return new(n||o)(f(C),f(q),f(F))}}static{this.\u0275prov=O({token:o,factory:o.\u0275fac,providedIn:"root"})}}return o})();export{ae as a};
