import{a as _e}from"./chunk-O4HFYYTJ.js";import{a as Pe,b as Ne,c as $e}from"./chunk-GX6AP5HP.js";import{b as x}from"./chunk-I4SN7ED3.js";import{a as Z}from"./chunk-IEIMMQCN.js";import"./chunk-GF36QAB3.js";import{a as T}from"./chunk-WPPT3EJF.js";import"./chunk-2LL5MXLB.js";import{$ as ie,Ab as ue,B as p,C as W,Cb as ge,Db as j,Eb as pe,F as k,Fb as he,G as v,Gb as me,Hb as fe,I as ne,Ib as z,K as d,Kb as K,L as h,Lb as U,M,Mb as ve,Nb as Ce,P as $,Q as _,R as y,Tb as we,Xb as be,Y as f,Yb as Me,Z as S,_ as O,ac as ye,d as B,da as R,dc as X,ec as V,fc as q,ib as re,l as E,m as D,ma as Y,na as A,pa as F,r as I,ra as J,s as N,v as te,vb as G,wa as ae,wb as se,xb as ce,yb as le,z as oe,zb as de}from"./chunk-E442IOFQ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-MJZLGLVY.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-7D2EH4XU.js";import"./chunk-MMIXVVWR.js";import"./chunk-SV7S5NYR.js";import{f as ee,g as b}from"./chunk-2R6CW7ES.js";var l=ee($e());function Te(u,P){u&1&&(d(0,"div",8),M(1,"ion-spinner",9),d(2,"p"),f(3,"Calculating travel times..."),h()())}function De(u,P){if(u&1&&(d(0,"ion-chip",13),M(1,"ion-icon",24),d(2,"ion-label"),f(3),h()()),u&2){let e=y(2);v("color",e.getStatusColor(e.center.status)),p(3),S(e.center.status)}}function Re(u,P){if(u&1&&(d(0,"div",16)(1,"ion-item",17),M(2,"ion-icon",25),d(3,"ion-label")(4,"h2"),f(5,"Capacity"),h(),d(6,"p"),f(7),h()()()()),u&2){let e=y(2);p(7),O("",e.center.capacity," people")}}function Ae(u,P){if(u&1){let e=$();d(0,"ion-card",26),_("click",function(){let o=I(e).$implicit,n=y(2);return N(n.selectTravelMode(o.mode))}),d(1,"ion-card-header"),M(2,"ion-icon",27),d(3,"ion-card-title"),f(4),h()(),d(5,"ion-card-content")(6,"div",28)(7,"div",29),M(8,"ion-icon",30),d(9,"span"),f(10),h()(),d(11,"div",31),M(12,"ion-icon",32),d(13,"span"),f(14),h()()(),d(15,"ion-button",33),_("click",function(){let o=I(e).$implicit,n=y(2);return N(n.selectTravelMode(o.mode))}),f(16," Select "),M(17,"ion-icon",34),h()()()}if(u&2){let e=P.$implicit,t=y(2);p(2),v("name",e.icon)("color",e.color),p(2),O(" ",e.mode==="foot-walking"?"Walking":e.mode==="cycling-regular"?"Cycling":"Driving"," "),p(6),S(t.formatTime(e.time)),p(4),S(t.formatDistance(e.distance)),p(),v("color",e.color)}}function Fe(u,P){if(u&1&&(d(0,"div",10)(1,"h1",11),f(2),h(),d(3,"div",12)(4,"ion-chip",13),M(5,"ion-icon",14),d(6,"ion-label"),f(7),h()(),k(8,De,4,2,"ion-chip",15),h(),d(9,"div",16)(10,"ion-item",17),M(11,"ion-icon",18),d(12,"ion-label")(13,"h2"),f(14,"Address"),h(),d(15,"p"),f(16),h()()()(),d(17,"div",16)(18,"ion-item",17),M(19,"ion-icon",19),d(20,"ion-label")(21,"h2"),f(22,"Contact"),h(),d(23,"p"),f(24),h()()()(),k(25,Re,8,1,"div",20),d(26,"div",21)(27,"h2"),f(28,"Travel Time Estimates"),h(),d(29,"div",22),k(30,Ae,18,6,"ion-card",23),h()()()),u&2){let e=y();p(2),S(e.center.name),p(2),v("color",e.getStatusColor(e.center.status)),p(),v("name",e.getDisasterTypeIcon(e.center.disaster_type)),p(2),S(e.center.disaster_type||"General"),p(),v("ngIf",e.center.status),p(8),S(e.center.address||"No address available"),p(8),S(e.center.contact||"No contact information available"),p(),v("ngIf",e.center.capacity),p(5),v("ngForOf",e.travelEstimates)}}var Oe=(()=>{class u{constructor(e,t,o,n){this.modalCtrl=e,this.http=t,this.toastCtrl=o,this.osmRouting=n,this.travelEstimates=[],this.selectedMode="foot-walking",this.isLoading=!0}ngOnInit(){return b(this,null,function*(){this.isLoading=!0,yield this.calculateTravelTimes(),this.isLoading=!1})}calculateTravelTimes(){return b(this,null,function*(){let e=[{id:"foot-walking",name:"Walking",icon:"walk-outline",color:"primary"},{id:"cycling-regular",name:"Cycling",icon:"bicycle-outline",color:"success"},{id:"driving-car",name:"Driving",icon:"car-outline",color:"danger"}];this.travelEstimates=[];let t=Number(this.center.latitude),o=Number(this.center.longitude);if(console.log("Calculating travel times with coordinates:",{userLat:this.userLat,userLng:this.userLng,centerLat:t,centerLng:o}),isNaN(t)||isNaN(o)||isNaN(this.userLat)||isNaN(this.userLng)){console.error("Invalid coordinates for travel time calculations:",{userLat:this.userLat,userLng:this.userLng,centerLat:t,centerLng:o}),this.toastCtrl.create({message:"Invalid coordinates. Using estimated travel times.",duration:3e3,color:"warning",position:"bottom"}).then(n=>n.present()),this.useFallbackCalculations(e);return}for(let n of e)try{let r=yield this.getTravelTimeEstimate(this.userLat,this.userLng,t,o,n.id);this.travelEstimates.push({mode:n.id,time:r.time,distance:r.distance,icon:n.icon,color:n.color})}catch(r){if(console.error(`Error calculating ${n.name} time:`,r),this.travelEstimates.length===0){let c="Using estimated travel times due to connection issues";r.message&&(r.message.includes("Invalid coordinates")?c="Invalid coordinates. Using estimated travel times.":r.message.includes("API Error")&&(c=`${r.message}. Using estimated travel times.`)),this.toastCtrl.create({message:c,duration:3e3,color:"warning",position:"bottom"}).then(m=>m.present())}let a=this.calculateStraightLineDistance(this.userLat,this.userLng,t,o),s;switch(n.id){case"foot-walking":s=5e3/3600;break;case"cycling-regular":s=15e3/3600;break;case"driving-car":s=4e4/3600;break;default:s=5e3/3600}let i=a/s;this.travelEstimates.push({mode:n.id,time:i,distance:a,icon:n.icon,color:n.color})}})}useFallbackCalculations(e){let t=Number(this.center.latitude),o=Number(this.center.longitude),n=isNaN(this.userLat)?10.3157:this.userLat,r=isNaN(this.userLng)?123.8854:this.userLng,a=isNaN(t)?10.3257:t,s=isNaN(o)?123.8954:o,i=this.calculateStraightLineDistance(n,r,a,s);console.log(`Using fallback calculation with distance: ${i} meters`);for(let c of e){let m;switch(c.id){case"foot-walking":m=5e3/3600;break;case"cycling-regular":m=15e3/3600;break;case"driving-car":m=4e4/3600;break;default:m=5e3/3600}let g=i/m;this.travelEstimates.push({mode:c.id,time:g,distance:i,icon:c.icon,color:c.color})}}getTravelTimeEstimate(e,t,o,n,r){return b(this,null,function*(){if([e,t,o,n].some(a=>typeof a!="number"||isNaN(a)))throw console.error("Invalid coordinates for travel time estimate:",{startLat:e,startLng:t,endLat:o,endLng:n}),new Error("Invalid coordinates");if(Math.abs(e)>90||Math.abs(o)>90||Math.abs(t)>180||Math.abs(n)>180)throw console.error("Coordinates out of range for travel time estimate:",{startLat:e,startLng:t,endLat:o,endLng:n}),new Error("Coordinates out of range");console.log(`Calculating OpenStreetMap route from [${e}, ${t}] to [${o}, ${n}] using mode: ${r}`);try{let a=this.osmRouting.convertTravelModeToProfile(r),s=yield this.osmRouting.getDirections(t,e,n,o,a,{geometries:"geojson",overview:"simplified",steps:!1});if(!s.routes||s.routes.length===0)throw new Error("No routes found");let i=s.routes[0];return console.log(`Received OpenStreetMap response for ${r} route:`,{duration:i.duration,distance:i.distance}),{time:i.duration,distance:i.distance}}catch(a){throw console.error(`Failed to fetch ${r} route from OpenStreetMap:`,a),a.message?a.message.includes("Invalid API key")?new Error("Invalid OpenRouteService API key. Please check your token configuration."):a.message.includes("Rate limit exceeded")?new Error("Too many requests to OpenRouteService. Please wait a moment and try again."):a.message.includes("Network error")?new Error("Network error. Please check your internet connection."):a.message.includes("No routes found")?new Error("No route could be calculated between these points."):new Error(`OpenStreetMap routing error: ${a.message}`):a}})}calculateStraightLineDistance(e,t,o,n){let a=e*Math.PI/180,s=o*Math.PI/180,i=(o-e)*Math.PI/180,c=(n-t)*Math.PI/180,m=Math.sin(i/2)*Math.sin(i/2)+Math.cos(a)*Math.cos(s)*Math.sin(c/2)*Math.sin(c/2);return 6371e3*(2*Math.atan2(Math.sqrt(m),Math.sqrt(1-m)))}formatTime(e){let t=Math.round(e/60);if(t<60)return`${t} min`;{let o=Math.floor(t/60),n=t%60;return`${o} hr ${n} min`}}formatDistance(e){return e<1e3?`${Math.round(e)} m`:`${(e/1e3).toFixed(2)} km`}selectTravelMode(e){this.selectedMode=e,this.dismiss(e)}dismiss(e){this.modalCtrl.dismiss({selectedMode:e||null})}getDisasterTypeIcon(e){if(!e)return"alert-circle-outline";let t=e.toLowerCase();return e.startsWith("Others:")?"help-circle-outline":t.includes("earthquake")||t.includes("quake")?"earth-outline":t.includes("flood")||t.includes("flash")?"water-outline":t.includes("typhoon")||t.includes("storm")?"thunderstorm-outline":t.includes("fire")?"flame-outline":t.includes("landslide")||t.includes("slide")?"triangle-outline":t.includes("others")?"help-circle-outline":"alert-circle-outline"}getStatusColor(e){if(!e)return"medium";let t=e.toLowerCase();return t.includes("active")||t.includes("open")?"success":t.includes("inactive")||t.includes("closed")?"warning":t.includes("full")?"danger":"medium"}static{this.\u0275fac=function(t){return new(t||u)(W(X),W(J),W(V),W(Z))}}static{this.\u0275cmp=D({type:u,selectors:[["app-evacuation-center-details"]],inputs:{center:"center",userLat:"userLat",userLng:"userLng"},standalone:!0,features:[R],decls:14,vars:2,consts:[[1,"ion-no-border"],["slot","start"],[3,"click"],["name","chevron-back-outline","slot","icon-only"],[1,"ion-padding"],["class","loading-container",4,"ngIf"],["class","details-container",4,"ngIf"],["expand","block","color","medium",3,"click"],[1,"loading-container"],["name","circles"],[1,"details-container"],[1,"center-name"],[1,"center-type"],[3,"color"],[3,"name"],[3,"color",4,"ngIf"],[1,"info-section"],["lines","none"],["name","location-outline","slot","start","color","primary"],["name","call-outline","slot","start","color","primary"],["class","info-section",4,"ngIf"],[1,"travel-section"],[1,"travel-cards"],["class","travel-card",3,"click",4,"ngFor","ngForOf"],["name","information-circle-outline"],["name","people-outline","slot","start","color","primary"],[1,"travel-card",3,"click"],[1,"travel-icon",3,"name","color"],[1,"travel-info"],[1,"travel-time"],["name","time-outline"],[1,"travel-distance"],["name","navigate-outline"],["expand","block","fill","clear",3,"click","color"],["name","arrow-forward-outline","slot","end"]],template:function(t,o){t&1&&(d(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-buttons",1)(3,"ion-button",2),_("click",function(){return o.dismiss()}),M(4,"ion-icon",3),h()(),d(5,"ion-title"),f(6,"Evacuation Center"),h()()(),d(7,"ion-content",4),k(8,Te,4,0,"div",5)(9,Fe,31,9,"div",6),h(),d(10,"ion-footer",0)(11,"ion-toolbar")(12,"ion-button",7),_("click",function(){return o.dismiss()}),f(13," Back to Map "),h()()()),t&2&&(p(8),v("ngIf",o.isLoading),p(),v("ngIf",!o.isLoading))},dependencies:[q,G,se,ce,le,de,ue,ge,j,me,fe,z,K,U,we,be,Me,F,Y,A],styles:["ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--border-color: transparent}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:200px}.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{width:48px;height:48px;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-medium)}.details-container[_ngcontent-%COMP%]{padding-bottom:20px}.center-name[_ngcontent-%COMP%]{font-size:24px;font-weight:700;margin:0 0 8px;color:var(--ion-color-dark)}.center-type[_ngcontent-%COMP%]{display:flex;gap:8px;margin-bottom:16px;flex-wrap:wrap}.info-section[_ngcontent-%COMP%]{margin-bottom:16px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;--inner-padding-end: 0;--background: transparent}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:16px;font-weight:600;margin-bottom:4px}.info-section[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:var(--ion-color-medium)}.travel-section[_ngcontent-%COMP%]{margin-top:24px}.travel-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:18px;font-weight:600;margin-bottom:16px;color:var(--ion-color-dark)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]{margin:0;border-radius:12px;box-shadow:0 4px 12px #00000014;transition:transform .2s ease,box-shadow .2s ease}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]:active{transform:scale(.98);box-shadow:0 2px 8px #0000001a}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 16px 0}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   .travel-icon[_ngcontent-%COMP%]{font-size:28px;margin-right:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px 16px 16px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:12px}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]{display:flex;align-items:center}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;margin-right:6px;color:var(--ion-color-medium)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-time[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .travel-info[_ngcontent-%COMP%]   .travel-distance[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:15px;font-weight:500;color:var(--ion-color-dark)}.travel-section[_ngcontent-%COMP%]   .travel-cards[_ngcontent-%COMP%]   .travel-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:8px;font-weight:500}ion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--border-color: transparent;padding:0 16px 16px}"]})}}return u})();function Ge(u,P){if(u&1&&(d(0,"p"),f(1),h()),u&2){let e=y();p(),ie(" ",e.formatDistance(e.totalDistance)," \u2022 ",e.formatTime(e.totalDuration)," ")}}function ze(u,P){if(u&1&&(d(0,"span"),f(1),h()),u&2){let e=y(2).$implicit,t=y();p(),O(" \u2022 ",t.formatTime(e.duration),"")}}function Ue(u,P){if(u&1&&(d(0,"p"),f(1),k(2,ze,2,1,"span",4),h()),u&2){let e=y().$implicit,t=y();p(),O(" ",t.formatDistance(e.distance)," "),p(),v("ngIf",e.duration>0)}}function qe(u,P){if(u&1&&(d(0,"ion-item",9),M(1,"ion-icon",3),d(2,"ion-label"),M(3,"h3",10),k(4,Ue,3,2,"p",4),h(),d(5,"ion-note",11),f(6),h()()),u&2){let e=P.$implicit,t=P.index,o=y();p(),v("name",o.getDirectionIcon(e.type))("color",o.getTravelModeColor()),p(2),v("innerHTML",e.instruction,oe),p(),v("ngIf",e.distance>0),p(2),S(t+1)}}var Le=(()=>{class u{constructor(){this.directions=[],this.travelMode="foot-walking",this.totalDistance=null,this.totalDuration=null,this.close=new te}getTravelModeName(){switch(this.travelMode){case"foot-walking":return"Walking";case"cycling-regular":return"Cycling";case"driving-car":return"Driving";default:return"Traveling"}}getTravelModeIcon(){switch(this.travelMode){case"foot-walking":return"walk-outline";case"cycling-regular":return"bicycle-outline";case"driving-car":return"car-outline";default:return"navigate-outline"}}getTravelModeColor(){switch(this.travelMode){case"foot-walking":return"primary";case"cycling-regular":return"success";case"driving-car":return"danger";default:return"medium"}}formatTime(e){let t=Math.round(e/60);if(t<60)return`${t} min`;{let o=Math.floor(t/60),n=t%60;return`${o} hr ${n} min`}}formatDistance(e){return e<1e3?`${Math.round(e)} m`:`${(e/1e3).toFixed(2)} km`}closePanel(){this.close.emit()}getDirectionIcon(e){switch(e){case 0:return"arrow-forward-outline";case 1:return"arrow-forward-outline";case 2:return"arrow-forward-outline";case 3:return"arrow-forward-outline";case 4:return"arrow-back-outline";case 5:return"arrow-back-outline";case 6:return"arrow-back-outline";case 7:return"arrow-back-outline";case 8:return"arrow-down-outline";case 9:return"flag-outline";case 10:return"arrow-up-outline";case 11:return"arrow-forward-outline";case 12:return"arrow-forward-outline";case 13:return"arrow-forward-outline";case 14:return"arrow-forward-outline";case 15:return"flag-outline";default:return"navigate-outline"}}static{this.\u0275fac=function(t){return new(t||u)}}static{this.\u0275cmp=D({type:u,selectors:[["app-directions-panel"]],inputs:{directions:"directions",travelMode:"travelMode",totalDistance:"totalDistance",totalDuration:"totalDuration"},outputs:{close:"close"},standalone:!0,features:[R],decls:13,vars:5,consts:[[1,"directions-panel"],[1,"directions-header"],["lines","none"],["slot","start",3,"name","color"],[4,"ngIf"],["fill","clear","slot","end",3,"click"],["name","close-outline","slot","icon-only"],[1,"directions-list"],["lines","full",4,"ngFor","ngForOf"],["lines","full"],[3,"innerHTML"],["slot","end"]],template:function(t,o){t&1&&(d(0,"div",0)(1,"div",1)(2,"ion-item",2),M(3,"ion-icon",3),d(4,"ion-label")(5,"h2"),f(6),h(),k(7,Ge,2,2,"p",4),h(),d(8,"ion-button",5),_("click",function(){return o.closePanel()}),M(9,"ion-icon",6),h()()(),d(10,"div",7)(11,"ion-list"),k(12,qe,7,5,"ion-item",8),h()()()),t&2&&(p(3),v("name",o.getTravelModeIcon())("color",o.getTravelModeColor()),p(3),O("",o.getTravelModeName()," Directions"),p(),v("ngIf",o.totalDistance&&o.totalDuration),p(5),v("ngForOf",o.directions))},dependencies:[q,G,z,K,U,ve,Ce,F,Y,A],styles:[".directions-panel[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;background-color:#fff;border-top-left-radius:15px;border-top-right-radius:15px;box-shadow:0 -2px 10px #0000001a;max-height:50vh;overflow-y:auto;z-index:1000}.directions-header[_ngcontent-%COMP%]{padding:10px 0;border-bottom:1px solid #eee;position:sticky;top:0;background-color:#fff;z-index:1001}.directions-list[_ngcontent-%COMP%]{max-height:calc(50vh - 60px);overflow-y:auto}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--inner-padding-end: 16px}ion-icon[_ngcontent-%COMP%]{font-size:24px}h2[_ngcontent-%COMP%]{font-weight:600;margin:0}h3[_ngcontent-%COMP%]{font-size:14px;font-weight:500;margin:0}p[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);margin:4px 0 0}ion-note[_ngcontent-%COMP%]{font-size:12px;padding:4px 8px;border-radius:50%;background-color:var(--ion-color-light);color:var(--ion-color-dark);display:flex;align-items:center;justify-content:center;min-width:24px;min-height:24px}"]})}}return u})();var Ee=ee(Ne());function We(u,P){if(u&1){let e=$();d(0,"div",11)(1,"ion-button",12),_("click",function(){I(e);let o=y();return N(o.requestLocationExplicitly())}),M(2,"ion-icon",13),f(3," Enable Location Access "),h(),d(4,"p",14),f(5,"Tap the button above to enable location access"),h()()}}function Ve(u,P){if(u&1){let e=$();d(0,"div",15),_("click",function(){I(e);let o=y();return N(o.showDirectionsPanel=!0)}),M(1,"ion-icon",16),d(2,"div",17)(3,"strong"),f(4),h(),f(5),d(6,"div",18),f(7),h()(),M(8,"ion-icon",19),h()}if(u&2){let e=y();p(),v("name",e.travelMode==="foot-walking"?"walk-outline":e.travelMode==="cycling-regular"?"bicycle-outline":"car-outline")("color",e.travelMode==="foot-walking"?"primary":e.travelMode==="cycling-regular"?"success":"danger"),p(3),O("",(e.routeTime/60).toFixed(0)," min"),p(),O(" \u2022 ",(e.routeDistance/1e3).toFixed(2)," km "),p(2),S(e.getTravelModeName())}}function He(u,P){if(u&1){let e=$();d(0,"app-directions-panel",20),_("close",function(){I(e);let o=y();return N(o.showDirectionsPanel=!1)}),h()}if(u&2){let e=y();v("directions",e.currentDirections)("travelMode",e.travelMode)("totalDistance",e.routeDistance)("totalDuration",e.routeTime)}}function Be(u,P){if(u&1&&(d(0,"div",21),M(1,"ion-icon",6),d(2,"span"),f(3),h()()),u&2){let e=y();p(),v("name",e.currentDisasterType.toLowerCase().includes("earthquake")?"earth-outline":e.currentDisasterType.toLowerCase().includes("typhoon")?"thunderstorm-outline":e.currentDisasterType.toLowerCase().includes("flood")?"water-outline":"alert-circle-outline"),p(2),O("",e.currentDisasterType," Evacuation Centers")}}function Ye(u,P){if(u&1){let e=$();d(0,"ion-fab",22)(1,"ion-fab-button",23),_("click",function(){I(e);let o=y();return N(o.routeToTwoNearestCenters())}),M(2,"ion-icon",24),h(),d(3,"ion-label",25),f(4,"Route to Nearest Centers"),h()()}}function Je(u,P){if(u&1){let e=$();d(0,"ion-fab",26)(1,"ion-fab-button",27),_("click",function(){I(e);let o=y();return N(o.showDirectionsPanel=!0)}),M(2,"ion-icon",28),h(),d(3,"ion-label",25),f(4,"Show Directions"),h()()}}var Pt=(()=>{class u{getTravelModeName(){switch(this.travelMode){case"foot-walking":return"Walking";case"cycling-regular":return"Cycling";case"driving-car":return"Driving";default:return"Traveling"}}findTwoNearestCenters(e,t,o){if(!o.length)return[];let n=o;if(this.currentDisasterType&&this.currentDisasterType!=="all"){console.log(`Filtering centers by disaster type: ${this.currentDisasterType}`);let a=this.currentDisasterType.toLowerCase();if(n=o.filter(s=>{if(!s.disaster_type)return!1;let i;return Array.isArray(s.disaster_type)?i=s.disaster_type.join(" ").toLowerCase():i=s.disaster_type.toLowerCase(),a==="earthquake"||a==="earthquakes"?i.includes("earthquake")||i.includes("quake"):a==="typhoon"||a==="typhoons"?i.includes("typhoon")||i.includes("storm")||i.includes("hurricane"):a==="flood"||a==="floods"?i.includes("flood")||i.includes("flash"):i===a}),console.log(`Filtered to ${n.length} centers for disaster type: ${this.currentDisasterType}`),n.length===0)return console.log(`No centers found for disaster type: ${this.currentDisasterType}`),[]}return[...n].sort((a,s)=>{let i=this.calculateDistance(e,t,Number(a.latitude),Number(a.longitude)),c=this.calculateDistance(e,t,Number(s.latitude),Number(s.longitude));return i-c}).slice(0,2)}updateRoute(){this.routeToTwoNearestCenters()}requestLocationExplicitly(){return b(this,null,function*(){console.log("User explicitly requested location access via button click"),this.showLocationRequestButton=!1,yield this.loadingService.showLoading("Getting your location...");try{try{let n=yield x.checkPermissions();if(console.log("Permission status:",n),n.location!=="granted"){console.log("Requesting permissions explicitly...");let r=yield x.requestPermissions();if(console.log("Permission request result:",r),r.location!=="granted")throw new Error("Location permission denied")}}catch(n){console.log("Permission check failed, might be in browser:",n)}let e=yield x.getCurrentPosition({enableHighAccuracy:!0,timeout:3e4,maximumAge:0});console.log("Successfully got position:",e);let t=e.coords.latitude,o=e.coords.longitude;yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Location access successful!",duration:2e3,color:"success"}).then(n=>n.present()),this.gpsEnabled=!0,this.map?(this.userMarker?(this.userMarker.setLatLng([t,o]),this.map.setView([t,o],15)):this.updateUserMarker(t,o),this.startWatchingPosition()):this.initializeMap(t,o)}catch(e){console.error("Error getting location:",e),yield this.loadingService.dismissLoading(),this.showLocationRequestButton=!0,yield(yield this.alertCtrl.create({header:"Location Access Failed",message:"We couldn't access your location. Would you like to see help on enabling location access?",buttons:[{text:"Show Help",handler:()=>{this.showLocationHelp()}},{text:"Try Again",handler:()=>{this.requestLocationExplicitly()}},{text:"Cancel",role:"cancel"}]})).present()}})}showLocationHelp(){return b(this,null,function*(){let e="To use location services:";navigator.userAgent.includes("Chrome")?e+='<br><br><b>Chrome:</b><br>1. Click the lock/info icon in the address bar<br>2. Select "Site settings"<br>3. Change Location permission to "Allow"<br>':navigator.userAgent.includes("Firefox")?e+='<br><br><b>Firefox:</b><br>1. Click the lock icon in the address bar<br>2. Select "Site Permissions"<br>3. Change "Access Your Location" to "Allow"<br>':navigator.userAgent.includes("Safari")?e+='<br><br><b>Safari:</b><br>1. Open Safari settings<br>2. Go to Websites > Location<br>3. Ensure this website is set to "Allow"<br>':e+="<br><br>Please enable location access for this website in your browser settings.",e+="<br><br>On mobile devices, also ensure that:<br>1. Your device location/GPS is turned on<br>2. The app has permission to access your location",yield(yield this.alertCtrl.create({header:"Location Services Help",message:e,buttons:[{text:"Try Again",handler:()=>{this.requestLocationExplicitly()}},{text:"OK",role:"cancel"}]})).present()})}routeToTwoNearestCenters(){return b(this,null,function*(){try{if(!this.gpsEnabled){console.log("GPS is disabled, not calculating routes"),(yield this.toastCtrl.create({message:"Please enable GPS to see evacuation routes",duration:3e3,color:"warning"})).present();return}console.log("Forcing fresh GPS position check for routing...");try{let e=yield this.getCurrentPositionWithFallback(),t=e.coords.latitude,o=e.coords.longitude;console.log(`Got fresh GPS position: [${t}, ${o}]`),this.userMarker?(this.userMarker.setLatLng([t,o]),this.map.setView([t,o],15)):this.userMarker=l.marker([t,o],{icon:l.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map);let n=t,r=o;this.toastCtrl.create({message:"Using your current real-time location",duration:2e3,color:"success"}).then(s=>s.present()),console.log(`Using FRESH GPS coordinates for routing: [${n}, ${r}]`),(!this.evacuationCenters||this.evacuationCenters.length===0)&&(yield this.loadEvacuationCenters(n,r));let a=this.findTwoNearestCenters(n,r,this.evacuationCenters);if(a.length===0){(yield this.toastCtrl.create({message:"No evacuation centers found.",duration:3e3,color:"danger"})).present();return}console.log("Aggressively clearing ALL existing routes"),this.map.eachLayer(s=>{s instanceof l.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(s))});for(let s of a){let i=Number(s.latitude),c=Number(s.longitude);if(console.log(`Calculating route from [${n}, ${r}] to center: ${s.name} with disaster type: ${s.disaster_type}`),console.log(`Center coordinates: [${i}, ${c}], types: [${typeof i}, ${typeof c}]`),isNaN(i)||isNaN(c)){console.error("Invalid center coordinates:",{centerLat:i,centerLng:c,center:s});continue}yield this.getRealRoute(n,r,i,c,this.travelMode,s.disaster_type)}if(this.userMarker){let s="You are here!.";a.forEach((i,c)=>{let m=this.calculateDistance(n,r,Number(i.latitude),Number(i.longitude));s+=`<br> \u2022 <strong>${i.name}</strong> <br> Distance: ${(m/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(s).openPopup()}}catch(e){console.error("Failed to get fresh GPS position:",e),(yield this.toastCtrl.create({message:"Could not get your current location. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}}catch(e){(yield this.toastCtrl.create({message:"Failed to get your location or route.",duration:3e3,color:"danger"})).present(),console.error("Failed to route to two nearest centers",e)}})}constructor(){this.travelMode="foot-walking",this.routeTime=null,this.routeDistance=null,this.userMarker=null,this.evacuationCenters=[],this.gpsEnabled=!0,this.loadingService=E(_e),this.osmRouting=E(Z),this.mapboxRouting=E(Pe),this.toastController=E(V),this.alertCtrl=E(ye),this.toastCtrl=E(V),this.modalCtrl=E(X),this.http=E(J),this.watchId=null,this.currentDisasterType="all",this.isFilterMode=!1,this.currentDirections=[],this.showDirectionsPanel=!1,this.showLocationRequestButton=!1,this.ORS_API_KEY=T.orsApiKey,this.isLoadingCenters=!1,this.lastErrorToast=0,this.ERROR_TOAST_DEBOUNCE=5e3,this.route=E(ae)}getPrimaryDisasterType(e){return e?Array.isArray(e)?e[0]||"":e:""}getDisasterIcon(e){if(!e||typeof e=="string"&&e.startsWith("Others:"))return"assets/forOthers.png";switch(e){case"Earthquake":return"assets/forEarthquake.png";case"Flood":return"assets/forFlood.png";case"Typhoon":return"assets/forTyphoon.png";case"Fire":return"assets/forFire.png";case"Landslide":return"assets/forLandslide.png";case"Others":return"assets/forOthers.png";default:return console.warn(`Unknown disaster type: ${e}, using Others icon`),"assets/forOthers.png"}}getDisasterColor(e){if(!e||typeof e=="string"&&e.startsWith("Others:"))return"#9333ea";switch(e){case"Earthquake":return"#ffa500";case"Flood":return"#0000ff";case"Typhoon":return"#008000";case"Fire":return"#ef4444";case"Landslide":return"#8b5a2b";case"Others":return"#9333ea";default:return console.warn(`Unknown disaster type: ${e}, using Others color`),"#9333ea"}}clearPulseCircles(){this.map.eachLayer(e=>{e instanceof l.Circle&&e.options.className==="marker-pulse"&&this.map.removeLayer(e)})}addPulsingAnimationToNearest(e){if(!e)return;this.clearPulseCircles();let t=Number(e.latitude),o=Number(e.longitude);if(isNaN(t)||isNaN(o)){console.error("Invalid coordinates for nearest center:",e);return}let n=this.getDisasterColor(e.disaster_type);l.circle([t,o],{radius:100,fillColor:n,color:n,weight:2,opacity:.8,fillOpacity:.3,className:"marker-pulse"}).addTo(this.map),console.log(`Added pulsing animation to nearest center: ${e.name} with color: ${n}`)}hasRecentErrorToast(){return Date.now()-this.lastErrorToast<this.ERROR_TOAST_DEBOUNCE}setLastErrorToast(){this.lastErrorToast=Date.now()}toggleGps(e){return b(this,null,function*(){if(console.log("GPS toggle:",e.detail.checked),this.gpsEnabled=e.detail.checked,this.gpsEnabled){console.log("Enabling GPS tracking...");try{let t=yield this.getCurrentPositionWithFallback();console.log("Position on toggle:",t);let o=t.coords.latitude,n=t.coords.longitude;this.userMarker?(this.userMarker.setLatLng([o,n]),this.userMarker.addTo(this.map)):this.updateUserMarker(o,n),this.map.setView([o,n],15),this.startWatchingPosition()}catch(t){console.error("Error enabling GPS:",t),this.gpsEnabled=!1,(yield this.toastCtrl.create({message:"Failed to enable GPS. Please check your location settings.",duration:3e3,color:"danger"})).present()}}else if(console.log("Disabling GPS tracking..."),this.userMarker&&this.userMarker.remove(),this.watchId){if(typeof this.watchId=="string")try{let t=this.watchId;x.clearWatch({id:t})}catch(t){console.log("Error clearing Capacitor watch:",t)}else if(typeof this.watchId=="number")try{navigator.geolocation.clearWatch(this.watchId)}catch(t){console.log("Error clearing browser watch:",t)}this.watchId=null}})}ngOnInit(){return b(this,null,function*(){console.log("\u{1F5FA}\uFE0F MAIN MAP: Initializing clean map (tabs/map)..."),this.route.queryParams.subscribe(e=>{if(e.centerId){let t=e.centerId;console.log(`\u{1F50D} SEARCH NAVIGATION: Loading specific center ID: ${t}`),this.loadSpecificCenter(t);return}if(e.lat&&e.lng){let t=parseFloat(e.lat),o=parseFloat(e.lng),n=e.name||"Search Result",r=e.directions==="true",a=e.viewOnly==="true";console.log(`\u{1F50D} SEARCH NAVIGATION: Loading location [${t}, ${o}] - ${n}, directions: ${r}, viewOnly: ${a}`),r?this.loadSearchLocationWithRouting(t,o,n):a&&this.loadSearchLocation(t,o,n);return}if(e.searchLat&&e.searchLng){let t=parseFloat(e.searchLat),o=parseFloat(e.searchLng),n=e.searchName||"Search Result";console.log(`\u{1F50D} SEARCH NAVIGATION: Loading location [${t}, ${o}] - ${n}`),this.loadSearchLocation(t,o,n);return}console.log("\u{1F5FA}\uFE0F MAIN MAP: Loading clean map with user location only"),this.loadCleanMap()})})}loadCleanMap(){return b(this,null,function*(){console.log("\u{1F5FA}\uFE0F CLEAN MAP: Loading map with user location only..."),yield this.loadingService.showLoading("Loading map...");try{let e=yield x.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),t=e.coords.latitude,o=e.coords.longitude;console.log(`\u{1F5FA}\uFE0F CLEAN MAP: User location [${t}, ${o}]`),this.initializeMap(t,o),this.evacuationCenters=[],this.isFilterMode=!1,this.currentDisasterType="all",this.map.eachLayer(r=>{r instanceof l.Marker&&r!==this.userMarker&&this.map.removeLayer(r),r instanceof l.GeoJSON&&this.map.removeLayer(r)}),yield this.loadingService.dismissLoading(),yield(yield this.toastCtrl.create({message:"\u{1F4CD} Map ready - Search for evacuation centers to view them here",duration:3e3,color:"primary",position:"top"})).present()}catch(e){yield this.loadingService.dismissLoading(),console.error("\u{1F5FA}\uFE0F CLEAN MAP: Error loading map",e),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadCleanMap()},{text:"Use Default Location",handler:()=>this.initializeMap(10.3157,123.8854)}]})).present()}})}loadSpecificCenter(e){return b(this,null,function*(){console.log(`\u{1F50D} SPECIFIC CENTER: Loading center ID ${e}...`),yield this.loadingService.showLoading("Loading evacuation center...");try{let o=(yield B(this.http.get(`${T.apiUrl}/evacuation-centers`))).find(w=>w.id.toString()===e);if(!o){yield this.loadingService.dismissLoading(),yield(yield this.alertCtrl.create({header:"Center Not Found",message:"The requested evacuation center could not be found.",buttons:["OK"]})).present(),this.loadCleanMap();return}let n=yield x.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),r=n.coords.latitude,a=n.coords.longitude,s=Number(o.latitude),i=Number(o.longitude);this.initializeMap(r,a);let c=this.getDisasterIcon(this.getPrimaryDisasterType(o.disaster_type)),m=l.marker([s,i],{icon:l.icon({iconUrl:c,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})});m.bindPopup(`
        <div class="evacuation-popup">
          <h3>${o.name}</h3>
          <p><strong>Type:</strong> ${o.disaster_type||"General"}</p>
          <p><strong>Address:</strong> ${o.address}</p>
          <p><strong>Capacity:</strong> ${o.capacity||"N/A"}</p>
        </div>
      `).openPopup(),m.addTo(this.map);let g=l.latLngBounds([[r,a],[s,i]]);this.map.fitBounds(g,{padding:[50,50]}),yield this.loadingService.dismissLoading(),yield(yield this.toastCtrl.create({message:`\u{1F4CD} Showing ${o.name}`,duration:3e3,color:"success"})).present()}catch(t){yield this.loadingService.dismissLoading(),console.error("\u{1F50D} SPECIFIC CENTER: Error loading center",t),yield(yield this.toastCtrl.create({message:"Error loading evacuation center. Please try again.",duration:3e3,color:"danger"})).present(),this.loadCleanMap()}})}loadSearchLocation(e,t,o){return b(this,null,function*(){console.log(`\u{1F50D} SEARCH LOCATION: Loading [${e}, ${t}] - ${o}...`),yield this.loadingService.showLoading("Loading location...");try{let n=yield x.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),r=n.coords.latitude,a=n.coords.longitude;this.initializeMap(r,a);let s=l.marker([e,t],{icon:l.icon({iconUrl:"assets/Location.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})});s.bindPopup(`
        <div class="search-popup">
          <h3>\u{1F4CD} ${o}</h3>
          <p>Search result location</p>
        </div>
      `).openPopup(),s.addTo(this.map);let i=l.latLngBounds([[r,a],[e,t]]);this.map.fitBounds(i,{padding:[50,50]}),yield this.loadingService.dismissLoading(),yield(yield this.toastCtrl.create({message:`\u{1F4CD} Showing ${o}`,duration:3e3,color:"primary"})).present()}catch(n){yield this.loadingService.dismissLoading(),console.error("\u{1F50D} SEARCH LOCATION: Error loading location",n),yield(yield this.toastCtrl.create({message:"Error loading search location. Please try again.",duration:3e3,color:"danger"})).present(),this.loadCleanMap()}})}loadMapWithSearchLocation(e,t,o,n=!1){return b(this,null,function*(){yield this.loadingService.showLoading("Loading selected location...");try{if(console.log(`Initializing map with search location: [${e}, ${t}], name: ${o}`),this.isFilterMode=!1,this.currentDisasterType="all",this.initializeMap(e,t),this.map.eachLayer(a=>{a instanceof l.Marker&&a!==this.userMarker&&this.map.removeLayer(a),a instanceof l.GeoJSON&&this.map.removeLayer(a)}),l.marker([e,t],{icon:l.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup(`<b>${o}</b><br>Selected evacuation center`).openPopup(),this.gpsEnabled)try{let a=yield this.getCurrentPositionWithFallback(),s=a.coords.latitude,i=a.coords.longitude;if(this.updateUserMarker(s,i),n){this.map.eachLayer(m=>{m instanceof l.GeoJSON&&this.map.removeLayer(m)}),yield this.getRealRoute(s,i,e,t,this.travelMode),this.toastCtrl.create({message:`Showing directions to ${o}`,duration:3e3,color:"success"}).then(m=>m.present());let c=l.latLngBounds([[s,i],[e,t]]);this.map.fitBounds(c,{padding:[50,50]})}else this.toastCtrl.create({message:`Showing ${o} on map`,duration:2e3,color:"primary"}).then(c=>c.present())}catch(a){console.error("Error getting user location for routing:",a),n&&this.toastCtrl.create({message:"Could not get your location to calculate directions. Please check your GPS settings.",duration:3e3,color:"warning"}).then(s=>s.present())}else n&&this.toastCtrl.create({message:"Please enable GPS to get directions",duration:3e3,color:"warning"}).then(a=>a.present());yield this.loadingService.dismissLoading()}catch(r){console.error("Error loading search location:",r),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load selected location. Please try again.",duration:3e3,color:"danger"}).then(a=>a.present()),this.loadMapWithUserLocation()}})}loadSearchLocationWithRouting(e,t,o){return b(this,null,function*(){console.log(`\u{1F50D} SEARCH ROUTING: Loading search location with routing [${e}, ${t}] - ${o}`);try{let n=yield x.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),r=n.coords.latitude,a=n.coords.longitude;console.log(`\u{1F50D} SEARCH ROUTING: User location [${r}, ${a}]`),console.log(`\u{1F50D} SEARCH ROUTING: Target location [${e}, ${t}] - ${o}`),this.isFilterMode=!1,this.currentDisasterType="all",this.initializeMap(r,a),this.map.eachLayer(i=>{i instanceof l.Marker&&i!==this.userMarker&&this.map.removeLayer(i),i instanceof l.GeoJSON&&this.map.removeLayer(i)}),this.userMarker=l.marker([r,a],{icon:l.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!"),l.marker([e,t],{icon:l.icon({iconUrl:"assets/forEarthquake.png",iconSize:[40,40],iconAnchor:[20,40]})}).addTo(this.map).bindPopup(`<b>${o}</b><br>Selected evacuation center`).openPopup(),yield this.showTransportationOptionsForSearch(e,t,o),console.log(`\u{1F50D} SEARCH ROUTING: Successfully loaded search location with routing: ${o}`)}catch(n){console.error("\u{1F50D} SEARCH ROUTING: Error loading search location with routing:",n),(yield this.toastCtrl.create({message:"Error getting your location for routing. Please enable GPS and try again.",duration:3e3,color:"danger"})).present()}})}showTransportationOptionsForSearch(e,t,o){return b(this,null,function*(){yield(yield this.alertCtrl.create({header:`Route to ${o}`,message:"Choose your transportation mode:",buttons:[{text:"\u{1F6B6}\u200D\u2642\uFE0F Walk",handler:()=>{this.routeToSearchLocation(e,t,o,"walking")}},{text:"\u{1F6B4}\u200D\u2642\uFE0F Cycle",handler:()=>{this.routeToSearchLocation(e,t,o,"cycling")}},{text:"\u{1F697} Drive",handler:()=>{this.routeToSearchLocation(e,t,o,"driving")}},{text:"Cancel",role:"cancel"}]})).present()})}routeToSearchLocation(e,t,o,n){return b(this,null,function*(){if(this.userMarker)try{let r=this.userMarker.getLatLng().lat,a=this.userMarker.getLatLng().lng,s=this.mapboxRouting.convertTravelModeToProfile(n),i=yield this.mapboxRouting.getDirections(a,r,t,e,s);if(i&&i.routes&&i.routes.length>0){let c=i.routes[0],m="#3880ff";this.map.eachLayer(w=>{w instanceof l.GeoJSON&&this.map.removeLayer(w)});let g=l.polyline(c.geometry.coordinates.map(w=>[w[1],w[0]]),{color:m,weight:5,opacity:.8});g.addTo(this.map),yield(yield this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Route: ${(c.distance/1e3).toFixed(2)}km, ${(c.duration/60).toFixed(0)}min via ${n}`,duration:4e3,color:"primary"})).present(),this.map.fitBounds(g.getBounds(),{padding:[50,50]})}}catch(r){console.error("\u{1F50D} Error routing to search location:",r),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}ngOnDestroy(){console.log("Map page destroyed, cleaning up resources"),this.stopWatchingPosition(),this.map&&this.map.remove()}getCurrentPositionWithFallback(){return b(this,null,function*(){try{console.log("Trying Capacitor Geolocation...");try{let e=yield x.checkPermissions();if(console.log("Permission status:",e),e.location!=="granted"){console.log("Requesting permissions explicitly...");let t=yield x.requestPermissions();if(console.log("Permission request result:",t),t.location!=="granted")throw new Error("Location permission denied")}}catch(e){console.log("Permission check failed, might be in browser:",e)}try{return console.log("Getting current position via Capacitor..."),yield x.getCurrentPosition({enableHighAccuracy:!0,timeout:1e4})}catch(e){throw console.log("Capacitor Geolocation failed, trying browser fallback:",e),e}}catch{if(console.log("Trying browser geolocation fallback..."),navigator.geolocation)return new Promise((t,o)=>{navigator.geolocation.getCurrentPosition(n=>{console.log("Browser geolocation succeeded:",n),t({coords:{latitude:n.coords.latitude,longitude:n.coords.longitude,accuracy:n.coords.accuracy,altitude:n.coords.altitude,altitudeAccuracy:n.coords.altitudeAccuracy,heading:n.coords.heading,speed:n.coords.speed},timestamp:n.timestamp})},n=>{if(console.error("Browser geolocation failed:",n),n.code===1&&n.message.includes("secure origins")){let r=new Error("Geolocation requires HTTPS. Please use a secure connection, run on a real device, or enable insecure origins in Chrome flags.");r.code=n.code,o(r)}else o(n)},{enableHighAccuracy:!0,timeout:1e4})});throw console.error("Geolocation not available in this browser"),new Error("Geolocation not available in this browser")}})}loadMapWithDisasterFilter(e,t=!1,o=!1){return b(this,null,function*(){yield this.loadingService.showLoading(`Loading ${e==="all"?"all evacuation centers":e+" evacuation centers"}...`);try{console.log("Getting user location for disaster map...");try{let n=yield this.getCurrentPositionWithFallback();console.log("Position received:",n);let r=n.coords.latitude,a=n.coords.longitude;console.log(`Initializing disaster map with real GPS coordinates: [${r}, ${a}]`),this.initializeMap(r,a),this.startWatchingPosition(),yield this.loadEvacuationCentersFiltered(r,a,e),t?this.toastCtrl.create({message:`\u{1F6A8} EMERGENCY: Showing nearest ${e} evacuation centers with routes`,duration:5e3,color:"danger",position:"top"}).then(s=>s.present()):this.toastCtrl.create({message:`Showing ${e==="all"?"all evacuation centers":e+" evacuation centers"} near you`,duration:3e3,color:"primary"}).then(s=>s.present()),yield this.loadingService.dismissLoading();return}catch(n){console.error("Failed to get GPS position for disaster map:",n),yield this.loadingService.dismissLoading(),yield(yield this.alertCtrl.create({header:"GPS Required",message:"We need your location to show nearby evacuation centers. Please enable GPS and try again.",buttons:[{text:"Enable GPS",handler:()=>{this.loadMapWithDisasterFilter(e)}},{text:"Cancel",role:"cancel"}]})).present()}}catch(n){console.error("Error loading disaster map:",n),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load evacuation centers. Please try again.",duration:3e3,color:"danger"}).then(r=>r.present())}})}loadMapWithOnlyUserLocation(){return b(this,null,function*(){yield this.loadingService.showLoading("Loading map...");try{console.log("Getting user location for map tab...");try{let e=yield this.getCurrentPositionWithFallback();console.log("Position received:",e);let t=e.coords.latitude,o=e.coords.longitude;console.log(`Initializing map with only user location: [${t}, ${o}]`),this.isFilterMode=!1,this.currentDisasterType="all",this.evacuationCenters=[],this.initializeMap(t,o),this.map.eachLayer(n=>{n instanceof l.Marker&&n!==this.userMarker&&this.map.removeLayer(n),n instanceof l.GeoJSON&&this.map.removeLayer(n)}),this.startWatchingPosition(),this.userMarker&&this.userMarker.bindPopup("You are here!").openPopup(),this.toastCtrl.create({message:"Showing your current location",duration:2e3,color:"success"}).then(n=>n.present()),yield this.loadingService.dismissLoading();return}catch(e){console.error("Failed to get GPS position for map tab:",e),yield this.loadingService.dismissLoading(),yield(yield this.alertCtrl.create({header:"Location Required",message:"We need your location to show the map. Please enable GPS and try again.",buttons:[{text:"Enable GPS",handler:()=>{this.loadMapWithOnlyUserLocation()}},{text:"Cancel",role:"cancel"}]})).present();return}}catch(e){console.error("Error loading map:",e),yield this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load map. Please try again.",duration:3e3,color:"danger"}).then(t=>t.present())}})}loadMapWithUserLocation(){return b(this,null,function*(){yield this.loadingService.showLoading("Loading map...");try{console.log("Getting user location...");try{let e=yield this.getCurrentPositionWithFallback();console.log("Position received:",e);let t=e.coords.latitude,o=e.coords.longitude;console.log(`Initializing map with real GPS coordinates: [${t}, ${o}]`),this.isFilterMode||(this.currentDisasterType="all"),this.initializeMap(t,o),this.startWatchingPosition(),this.toastCtrl.create({message:"Using your real-time location",duration:2e3,color:"success"}).then(n=>n.present()),yield this.loadingService.dismissLoading();return}catch(e){throw console.error("Failed to get GPS position, showing alert:",e),e}}catch(e){console.error("Error getting location",e);let t="Unable to access your location. ";e.code===1?navigator.userAgent.includes("Chrome")?t+='Location permission denied. Please click the lock icon in the address bar, select "Site settings", and change Location permission to "Allow".':navigator.userAgent.includes("Firefox")?t+='Location permission denied. Please click the lock icon in the address bar, select "Site Permissions", and change "Access Your Location" to "Allow".':navigator.userAgent.includes("Safari")?t+='Location permission denied. Please check Safari settings > Websites > Location and ensure this website is set to "Allow".':t+="Location permission denied. Please enable location access for this website in your browser settings.":e.code===2?t+="Position unavailable. Your GPS signal might be weak or unavailable.":e.code===3?t+="Location request timed out. Please try again.":t+="Please enable GPS or try again. "+(e.message||""),yield(yield this.alertCtrl.create({header:"Location Error",message:t,buttons:[{text:"Retry",handler:()=>{this.loadMapWithUserLocation()}},{text:"Load Default Map",role:"cancel",handler:()=>{this.initializeMap(10.3157,123.8854)}}]})).present()}yield this.loadingService.dismissLoading()})}stopWatchingPosition(){if(this.watchId){if(console.log("Stopping position watch..."),typeof this.watchId=="string")try{let e=this.watchId;x.clearWatch({id:e})}catch(e){console.log("Error clearing Capacitor watch:",e)}else if(typeof this.watchId=="number")try{navigator.geolocation.clearWatch(this.watchId)}catch(e){console.log("Error clearing browser watch:",e)}this.watchId=null}}startWatchingPosition(){this.stopWatchingPosition(),console.log("Starting position watch...");try{this.watchId=x.watchPosition({enableHighAccuracy:!0,timeout:1e4},(e,t)=>{e&&this.gpsEnabled&&(console.log("Capacitor watch position update:",e),this.updateUserMarker(e.coords.latitude,e.coords.longitude)),t&&(console.error("Error watching position:",t),this.toastCtrl.create({message:"GPS signal lost or weak. Please check your location settings.",duration:3e3,color:"warning"}).then(o=>o.present()))}),console.log("Capacitor watch started with ID:",this.watchId)}catch(e){console.log("Capacitor watch failed, trying browser fallback:",e),navigator.geolocation?(this.watchId=navigator.geolocation.watchPosition(t=>{this.gpsEnabled&&(console.log("Browser watch position update:",t),this.updateUserMarker(t.coords.latitude,t.coords.longitude))},t=>{console.error("Browser watch error:",t),this.toastCtrl.create({message:"GPS signal lost or weak. Please check your location settings.",duration:3e3,color:"warning"}).then(o=>o.present())},{enableHighAccuracy:!0,timeout:1e4}),console.log("Browser watch started with ID:",this.watchId)):console.error("Geolocation watching not available")}}initializeMap(e,t){console.log(`Initializing map with coordinates: [${e}, ${t}]`),(isNaN(e)||isNaN(t)||Math.abs(e)>90||Math.abs(t)>180)&&(console.error("Invalid coordinates for map initialization:",{lat:e,lng:t}),e=12.8797,t=121.774,console.log(`Using fallback coordinates for Philippines: [${e}, ${t}]`)),this.map&&(console.log("Removing existing map"),this.map.remove()),this.map=l.map("map").setView([e,t],15),console.log("Map initialized"),console.log("\u{1F310} Loading online map tiles..."),l.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors",maxZoom:19,minZoom:8}).addTo(this.map),this.gpsEnabled?(console.log("GPS is enabled, adding user marker"),this.userMarker?(this.userMarker.setLatLng([e,t]),this.userMarker.addTo(this.map),console.log("Updated existing user marker")):(this.userMarker=l.marker([e,t],{icon:l.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup("You are here!").openPopup(),console.log("Created new user marker")),this.toastCtrl.create({message:"Using your real-time GPS location",duration:2e3,color:"success"}).then(o=>o.present())):console.log("GPS is disabled, not adding user marker"),this.isFilterMode&&this.currentDisasterType!=="all"?(console.log("Loading evacuation centers for filter mode"),this.loadEvacuationCenters(e,t)):(console.log("\u{1F5FA}\uFE0F CLEAN MAP: Skipping evacuation centers - showing only user location"),this.evacuationCenters=[])}updateUserMarker(e,t){if(console.log(`Updating user marker to: [${e}, ${t}]`),isNaN(e)||isNaN(t)||Math.abs(e)>90||Math.abs(t)>180){console.error("Invalid coordinates for user marker update:",{lat:e,lng:t});return}if(this.userMarker){let o=this.userMarker.getLatLng();this.userMarker.setLatLng([e,t]),this.map.setView([e,t]),console.log("Updated existing user marker position");let n=this.calculateDistance(o.lat,o.lng,e,t);console.log(`User moved ${n.toFixed(2)} meters from previous position`),n>20&&(console.log(`Significant movement detected (${n.toFixed(2)}m), recalculating routes`),this.map.eachLayer(r=>{r instanceof l.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(r))}),this.evacuationCenters&&this.evacuationCenters.length>0&&(console.log("Recalculating routes to nearest evacuation centers"),this.routeToTwoNearestCenters()))}else this.userMarker=l.marker([e,t],{icon:l.icon({iconUrl:"assets/Location.png",iconSize:[32,32],iconAnchor:[16,32]})}).addTo(this.map).bindPopup("You are here!").openPopup(),console.log("Created new user marker"),this.evacuationCenters&&this.evacuationCenters.length>0&&(console.log("Calculating initial routes with real GPS data"),this.routeToTwoNearestCenters())}loadEvacuationCentersFiltered(e,t,o){return b(this,null,function*(){if(this.isLoadingCenters){console.log("Already loading evacuation centers, skipping duplicate request");return}this.isLoadingCenters=!0;try{if(console.log(`Loading evacuation centers for disaster type: ${o}`),console.log(`User coordinates: [${e}, ${t}]`),isNaN(e)||isNaN(t)||Math.abs(e)>90||Math.abs(t)>180){console.error("Invalid user coordinates for loading evacuation centers:",{userLat:e,userLng:t}),(yield this.toastCtrl.create({message:"Invalid location coordinates. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}this.currentDisasterType=o;let n=[];console.log("\u{1F310} Fetching evacuation centers from:",`${T.apiUrl}/evacuation-centers`);try{let i=yield B(this.http.get(`${T.apiUrl}/evacuation-centers`));console.log("\u{1F4E1} RAW API RESPONSE:",i),n=i.data||[],console.log("\u{1F4CA} TOTAL CENTERS RECEIVED:",n?.length||0)}catch(i){console.error("\u274C Failed to fetch evacuation centers:",i),this.toastCtrl.create({message:"Failed to load evacuation centers. Please check your internet connection.",duration:3e3,color:"danger"}).then(c=>c.present());return}if(n&&n.length>0){let i=[...new Set(n.map(c=>c.disaster_type))];console.log("\u{1F3F7}\uFE0F UNIQUE DISASTER TYPES IN DATABASE:",i),i.forEach(c=>{let m=n.filter(g=>g.disaster_type===c).length;console.log(`   \u{1F4C8} ${c}: ${m} centers`)}),console.log("\u{1F50D} SAMPLE CENTERS:"),n.slice(0,5).forEach((c,m)=>{console.log(`   ${m+1}. "${c.name}" - Type: "${c.disaster_type}" - Status: "${c.status}"`)})}this.map.eachLayer(i=>{i instanceof l.Marker&&i!==this.userMarker&&this.map.removeLayer(i)}),this.map.eachLayer(i=>{i instanceof l.GeoJSON&&this.map.removeLayer(i)});let r=n||[];if(o!=="all"){console.log(`\u{1F50D} FILTERING centers for disaster type: "${o}"`),console.log(`\u{1F4CA} Total centers before filtering: ${r.length}`),console.log("\u{1F4CB} All centers disaster types:",n.map(c=>`${c.name}: "${c.disaster_type}"`)),r=r.filter(c=>{if(!c.disaster_type)return console.log(`\u274C Center "${c.name}" has no disaster_type, excluding`),!1;let m=this.getPrimaryDisasterType(c.disaster_type).trim(),g=o.trim(),C=m===g;return console.log(`\u{1F3E2} Center "${c.name}"`),console.log(`   \u{1F4CD} Center Type: "${m}" (length: ${m.length})`),console.log(`   \u{1F3AF} Looking for: "${g}" (length: ${g.length})`),console.log(`   \u2705 Match: ${C}`),C}),console.log(`\u{1F3AF} FILTERED RESULT: ${r.length} centers for disaster type: "${o}"`),console.log("\u2705 INCLUDED CENTERS:"),r.forEach((c,m)=>{console.log(`   ${m+1}. ${c.name} (${c.disaster_type})`)});let i=n.filter(c=>c.disaster_type&&this.getPrimaryDisasterType(c.disaster_type).trim()!==o.trim());console.log("\u274C EXCLUDED CENTERS:"),i.forEach((c,m)=>{console.log(`   ${m+1}. ${c.name} (${c.disaster_type})`)}),r.length===0&&(console.error("\u{1F6A8} NO CENTERS FOUND FOR DISASTER TYPE!"),console.error("\u{1F50D} Debug Info:"),console.error(`   Target disaster type: "${o}"`),console.error("   Available disaster types:",[...new Set(n.map(c=>c.disaster_type))]),console.error(`   Total centers in database: ${n.length}`))}console.log("\u{1F9F9} AGGRESSIVE CLEARING: Removing ALL existing markers");let a=[];if(this.map.eachLayer(i=>{i instanceof l.Marker&&i!==this.userMarker&&(console.log("\u{1F5D1}\uFE0F Marking marker for removal:",i),a.push(i)),i instanceof l.GeoJSON&&(console.log("\u{1F5D1}\uFE0F Marking route for removal:",i),a.push(i))}),a.forEach(i=>{console.log("\u{1F5D1}\uFE0F Removing layer from map"),this.map.removeLayer(i)}),this.evacuationCenters=[],console.log(`\u{1F9F9} CLEARED: Removed ${a.length} layers from map`),this.evacuationCenters=r,this.evacuationCenters.length===0){console.log(`\u{1F6A8} NO EVACUATION CENTERS FOUND for disaster type: "${o}"`),this.alertCtrl.create({header:"No Evacuation Centers Found",message:`There are no evacuation centers stored for ${o==="all"?"any disaster type":o}. Please contact your administrator to add evacuation centers.`,buttons:["OK"]}).then(i=>i.present()),this.userMarker&&this.userMarker.bindPopup("You are here!").openPopup(),this.map.setView([e,t],15);return}console.log(`\u{1F3AF} ADDING ${this.evacuationCenters.length} FILTERED MARKERS to map`),console.log(`\u{1F4CD} Disaster type filter: "${o}"`),console.log("\u{1F3E2} Centers to display:",this.evacuationCenters.map(i=>`${i.name} (${i.disaster_type})`)),this.evacuationCenters.forEach((i,c)=>{let m=Number(i.latitude),g=Number(i.longitude);if(console.log(`\u{1F3E2} Processing center ${c+1}/${this.evacuationCenters.length}: ${i.name}`),console.log(`   \u{1F4CD} Coordinates: [${m}, ${g}]`),console.log(`   \u{1F3F7}\uFE0F Disaster Type: "${i.disaster_type}"`),console.log(`   \u{1F3AF} Filter Type: "${o}"`),!isNaN(m)&&!isNaN(g)){let C=this.getDisasterIcon(this.getPrimaryDisasterType(i.disaster_type));console.log(`   \u{1F3A8} Icon URL: ${C}`);let w=l.marker([m,g],{icon:l.icon({iconUrl:C,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),H=`
            <div class="evacuation-popup">
              <h3>${i.name||"Evacuation Center"}</h3>
              <p><strong>Type:</strong> ${i.disaster_type||"General"}</p>
              <p><strong>Distance:</strong> ${(this.calculateDistance(e,t,m,g)/1e3).toFixed(2)} km</p>
              <p><button class="popup-button">View Details</button></p>
            </div>
          `;w.bindPopup(H),w.on("click",()=>{setTimeout(()=>{w.closePopup(),this.showEvacuationCenterDetails(i,e,t)},300)}),w.addTo(this.map),console.log(`   \u2705 MARKER ADDED to map for: ${i.name} (${i.disaster_type})`)}else console.error(`   \u274C Invalid coordinates for center: ${i.name}`)}),console.log(`\u{1F389} COMPLETED: Added ${this.evacuationCenters.length} markers for disaster type "${o}"`);let s=0;if(this.map.eachLayer(i=>{i instanceof l.Marker&&i!==this.userMarker&&s++}),console.log(`\u{1F50D} VERIFICATION: ${s} evacuation center markers currently on map`),this.gpsEnabled&&this.userMarker&&this.evacuationCenters.length>0){console.log("GPS enabled and user marker exists, finding nearest centers");let i=this.findTwoNearestCenters(e,t,this.evacuationCenters);if(i.length>0){this.addPulsingAnimationToNearest(i[0]);for(let g of i){let C=Number(g.latitude),w=Number(g.longitude);if(console.log(`Calculating route to center: ${g.name}`),console.log(`Center coordinates: [${C}, ${w}], types: [${typeof C}, ${typeof w}]`),isNaN(C)||isNaN(w)){console.error("Invalid center coordinates:",{centerLat:C,centerLng:w,center:g});continue}yield this.getRealRoute(e,t,C,w,this.travelMode,g.disaster_type)}let c="You are here!.";i.forEach((g,C)=>{let w=this.calculateDistance(e,t,Number(g.latitude),Number(g.longitude));c+=`<br> \u2022 <strong>${C+1}: ${g.name} </strong> <br> Distance: ${(w/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(c).openPopup();let m=l.latLngBounds([]);m.extend([e,t]),i.forEach(g=>{m.extend([Number(g.latitude),Number(g.longitude)])}),this.map.fitBounds(m,{padding:[50,50]})}else console.log("No nearest centers found"),this.map.setView([e,t],15)}else console.log("GPS disabled, no user marker, or no centers found, skipping route calculation"),this.map.setView([e,t],15)}catch(n){console.error("Error loading filtered evacuation centers:",n),console.log("Network error loading evacuation centers - offline mode available")}finally{this.isLoadingCenters=!1}})}loadEvacuationCenters(e,t){return b(this,null,function*(){if(this.isLoadingCenters){console.log("Already loading evacuation centers, skipping duplicate request");return}this.isLoadingCenters=!0;try{if(console.log(`Loading evacuation centers with user coordinates: [${e}, ${t}]`),isNaN(e)||isNaN(t)||Math.abs(e)>90||Math.abs(t)>180){console.error("Invalid user coordinates for loading evacuation centers:",{userLat:e,userLng:t}),(yield this.toastCtrl.create({message:"Invalid location coordinates. Please check your GPS settings.",duration:3e3,color:"danger"})).present();return}let o=[];console.log("\u{1F310} Fetching evacuation centers from:",`${T.apiUrl}/evacuation-centers`);try{o=(yield B(this.http.get(`${T.apiUrl}/evacuation-centers`))).data||[],console.log("\u{1F4E1} Received centers from API:",o)}catch(n){console.error("\u274C Failed to fetch evacuation centers:",n),this.toastCtrl.create({message:"Failed to load evacuation centers. Please check your internet connection.",duration:3e3,color:"danger"}).then(r=>r.present());return}if(this.evacuationCenters=o||[],this.map.eachLayer(n=>{n instanceof l.Marker&&n!==this.userMarker&&this.map.removeLayer(n)}),this.evacuationCenters.forEach(n=>{let r=Number(n.latitude),a=Number(n.longitude);if(console.log(`Processing center: ${n.name}, coordinates: [${r}, ${a}]`),!isNaN(r)&&!isNaN(a)){let s=this.getDisasterIcon(this.getPrimaryDisasterType(n.disaster_type)),i=l.marker([r,a],{icon:l.icon({iconUrl:s,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),c=`
            <div class="evacuation-popup">
              <h3>${n.name||"Evacuation Center"}</h3>
              <p><strong>Distance:</strong> ${(this.calculateDistance(e,t,r,a)/1e3).toFixed(2)} km</p>
              <p><button class="popup-button">View Details</button></p>
            </div>
          `;i.bindPopup(c),i.on("click",()=>{setTimeout(()=>{i.closePopup(),this.showEvacuationCenterDetails(n,e,t)},300)}),i.addTo(this.map),console.log(`Added marker for center: ${n.name}`)}else console.error(`Invalid coordinates for center: ${n.name}`)}),this.gpsEnabled&&this.userMarker){console.log("GPS enabled and user marker exists, finding nearest centers");let n=this.findTwoNearestCenters(e,t,this.evacuationCenters);if(n.length>0){this.addPulsingAnimationToNearest(n[0]),this.map.eachLayer(a=>{a instanceof l.GeoJSON&&this.map.removeLayer(a)});for(let a of n){let s=Number(a.latitude),i=Number(a.longitude);if(console.log(`Calculating route to center: ${a.name}`),console.log(`Center coordinates: [${s}, ${i}], types: [${typeof s}, ${typeof i}]`),isNaN(s)||isNaN(i)){console.error("Invalid center coordinates:",{centerLat:s,centerLng:i,center:a});continue}yield this.getRealRoute(e,t,s,i,this.travelMode,a.disaster_type)}let r="You are here!.";n.forEach((a,s)=>{let i=this.calculateDistance(e,t,Number(a.latitude),Number(a.longitude));r+=`<br> \u2022${s+1}: ${a.name} <br> Distance: ${(i/1e3).toFixed(2)} km`}),this.userMarker.bindPopup(r).openPopup()}else console.log("No nearest centers found"),this.map.setView([e,t],15)}else console.log("GPS disabled or no user marker, skipping route calculation"),this.map.setView([e,t],15)}catch(o){console.error("Failed to load evacuation centers",o),console.log("Network error loading evacuation centers - offline mode available")}finally{this.isLoadingCenters=!1}})}getRealRoute(s,i,c,m){return b(this,arguments,function*(e,t,o,n,r=this.travelMode,a){if(console.log("Clearing all existing routes before calculating new route"),this.map.eachLayer(g=>{g instanceof l.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(g))}),console.log("Requesting Mapbox route with coordinates:",{startLat:e,startLng:t,endLat:o,endLng:n,travelMode:r}),[e,t,o,n].some(g=>typeof g!="number"||isNaN(g))){(yield this.toastCtrl.create({message:"Invalid route coordinates. Cannot request directions.",duration:3e3,color:"danger"})).present();return}if(Math.abs(e)>90||Math.abs(o)>90||Math.abs(t)>180||Math.abs(n)>180){(yield this.toastCtrl.create({message:"Route coordinates out of range. Cannot request directions.",duration:3e3,color:"danger"})).present();return}try{console.log("Sending route request to OpenStreetMap");let g=this.mapboxRouting.convertTravelModeToProfile(r),C=yield this.mapboxRouting.getDirections(t,e,n,o,g);if(!C.routes||C.routes.length===0)throw new Error("No routes found");let w=C.routes[0],H=this.osmRouting.convertToGeoJSON(w),L="#3388ff";if(a)switch(a){case"Earthquake":L="#ffa500";break;case"Flood":L="#0000ff";break;case"Typhoon":L="#008000";break;default:console.warn(`Unknown disaster type for route color: ${a}`);break}console.log(`Route calculation - Disaster type: "${a}", Normalized type: "${a?a.toLowerCase():"none"}", Selected color: ${L}`),this.currentDisasterType&&this.currentDisasterType!=="all"&&(this.currentDisasterType==="Earthquake"?L="#ffa500":this.currentDisasterType==="Typhoon"?L="#008000":this.currentDisasterType==="Flood"&&(L="#0000ff"),console.log(`Filter mode active: ${this.currentDisasterType}, forcing route color to: ${L}`)),console.log(`Using route color: ${L} for disaster type: ${a||"unknown"}`);let Ie=l.geoJSON(H,{style:{color:L,weight:5,opacity:.8}}).addTo(this.map);this.routeTime=w.duration,this.routeDistance=w.distance;let Q=this.osmRouting.getRouteSummary(w);console.log(`OpenStreetMap route summary: ${Q.duration}, ${Q.distance}`),this.map.fitBounds(Ie.getBounds(),{padding:[50,50]})}catch(g){console.error("Failed to fetch route from Mapbox",g);let C="Failed to fetch route. Please check your internet connection or try again later.";g.message?g.message.includes("Invalid Mapbox access token")?C="Invalid Mapbox access token. Please check your token configuration.":g.message.includes("Rate limit exceeded")?C="Too many requests to Mapbox. Please wait a moment and try again.":g.message.includes("Network error")?C="Network error. Please check your internet connection.":g.message.includes("No routes found")?C="No route could be calculated between these points.":C=`Mapbox routing error: ${g.message}`:g.status===401?C="Invalid Mapbox access token. Please check your token.":g.status===422?C="Invalid coordinates or routing parameters.":g.status===429?C="Rate limit exceeded. Please try again later.":g.status===0&&(C="Network error. Please check your internet connection.");let w=r==="foot-walking"?"walking":r==="cycling-regular"?"cycling":r==="driving-car"?"driving":r;this.hasRecentErrorToast()||((yield this.toastCtrl.create({message:`Failed to fetch ${w} route: ${C}`,duration:5e3,color:"danger"})).present(),this.setLastErrorToast())}})}findNearestCenter(e,t,o){if(!o.length)return null;let n=o[0],r=this.calculateDistance(e,t,Number(n.latitude),Number(n.longitude));for(let a of o){let s=this.calculateDistance(e,t,Number(a.latitude),Number(a.longitude));s<r&&(r=s,n=a)}return n}calculateDistance(e,t,o,n){let a=e*Math.PI/180,s=o*Math.PI/180,i=(o-e)*Math.PI/180,c=(n-t)*Math.PI/180,m=Math.sin(i/2)*Math.sin(i/2)+Math.cos(a)*Math.cos(s)*Math.sin(c/2)*Math.sin(c/2);return 6371e3*(2*Math.atan2(Math.sqrt(m),Math.sqrt(1-m)))}downloadMap(){return b(this,null,function*(){try{yield this.loadingService.showLoading("Capturing map...");let e=document.getElementById("map");if(!e)throw new Error("Map element not found");console.log("Capturing map as image...");let t=yield(0,Ee.default)(e,{useCORS:!0,allowTaint:!0,scrollX:0,scrollY:0,windowWidth:document.documentElement.offsetWidth,windowHeight:document.documentElement.offsetHeight,scale:1});yield this.loadingService.dismissLoading();let o=t.toDataURL("image/png"),a=`evacuation-map-${new Date().toISOString().replace(/[:.]/g,"-").substring(0,19)}.png`;yield(yield this.alertCtrl.create({header:"Map Captured",message:"Your map has been captured. What would you like to do with it?",buttons:[{text:"Download",handler:()=>{this.downloadImage(o,a)}},{text:"Share",handler:()=>{this.shareImage(o,a)}},{text:"Cancel",role:"cancel"}]})).present()}catch(e){console.error("Error capturing map:",e),yield this.loadingService.dismissLoading(),(yield this.toastCtrl.create({message:"Failed to capture map. Please try again.",duration:3e3,color:"danger"})).present()}})}downloadImage(e,t){let o=document.createElement("a");o.href=e,o.download=t,document.body.appendChild(o),o.click(),document.body.removeChild(o),this.toastCtrl.create({message:"Map downloaded successfully",duration:2e3,color:"success"}).then(n=>n.present())}shareImage(e,t){return b(this,null,function*(){try{if(navigator.share){let o=yield(yield fetch(e)).blob(),n=new File([o],t,{type:"image/png"});yield navigator.share({title:"Evacuation Map",text:"Here is my evacuation map with routes to the nearest evacuation centers",files:[n]}),console.log("Map shared successfully")}else console.log("Web Share API not supported"),(yield this.toastCtrl.create({message:"Sharing not supported on this device. The map has been downloaded instead.",duration:3e3,color:"warning"})).present(),this.downloadImage(e,t)}catch(o){console.error("Error sharing map:",o),(yield this.toastCtrl.create({message:"Failed to share map. The map has been downloaded instead.",duration:3e3,color:"warning"})).present(),this.downloadImage(e,t)}})}showEvacuationCenterDetails(e,t,o){return b(this,null,function*(){console.log("Showing evacuation center details for:",e.name);let n=yield this.modalCtrl.create({component:Oe,componentProps:{center:e,userLat:t,userLng:o},cssClass:"evacuation-details-modal",breakpoints:[0,.5,.75,1],initialBreakpoint:.75});yield n.present();let{data:r}=yield n.onDidDismiss();if(r&&r.selectedMode&&(console.log("Selected travel mode:",r.selectedMode),this.travelMode=r.selectedMode,console.log("Clearing all existing routes before calculating new route"),this.map.eachLayer(a=>{a instanceof l.GeoJSON&&(console.log("Removing existing route layer"),this.map.removeLayer(a))}),this.userMarker)){let a=this.userMarker.getLatLng(),s=Number(e.latitude),i=Number(e.longitude);if(console.log("Recalculating route with new travel mode:",{userLat:a.lat,userLng:a.lng,centerLat:s,centerLng:i,travelMode:this.travelMode}),isNaN(s)||isNaN(i)){console.error("Invalid center coordinates:",{centerLat:s,centerLng:i}),(yield this.toastCtrl.create({message:"Invalid evacuation center coordinates. Cannot calculate route.",duration:3e3,color:"danger"})).present();return}this.toastCtrl.create({message:`Showing ${this.getTravelModeName().toLowerCase()} route to ${e.name}`,duration:2e3,color:"primary"}).then(c=>c.present()),yield this.getRealRoute(a.lat,a.lng,s,i,this.travelMode,this.getPrimaryDisasterType(e.disaster_type))}})}static{this.\u0275fac=function(t){return new(t||u)}}static{this.\u0275cmp=D({type:u,selectors:[["app-map"]],standalone:!0,features:[R],decls:15,vars:12,consts:[["id","map"],["class","location-request-container",4,"ngIf"],["class","route-summary-card",3,"click",4,"ngIf"],[3,"directions","travelMode","totalDistance","totalDuration","close",4,"ngIf"],["vertical","top","horizontal","end","slot","fixed"],["size","small",3,"click","color"],[3,"name"],[1,"gps-status",3,"click"],["class","disaster-type-indicator",4,"ngIf"],["vertical","bottom","horizontal","end","slot","fixed",4,"ngIf"],["vertical","bottom","horizontal","start","slot","fixed",4,"ngIf"],[1,"location-request-container"],["expand","block","color","primary",3,"click"],["name","locate","slot","start"],[1,"location-help-text"],[1,"route-summary-card",3,"click"],[3,"name","color"],[1,"summary-text"],[1,"travel-mode"],["name","chevron-up",1,"expand-icon"],[3,"close","directions","travelMode","totalDistance","totalDuration"],[1,"disaster-type-indicator"],["vertical","bottom","horizontal","end","slot","fixed"],["color","primary",3,"click"],["name","navigate-outline"],[1,"fab-label"],["vertical","bottom","horizontal","start","slot","fixed"],["color","tertiary",3,"click"],["name","list-outline"]],template:function(t,o){t&1&&(d(0,"ion-content"),M(1,"div",0),k(2,We,6,0,"div",1)(3,Ve,9,5,"div",2)(4,He,1,4,"app-directions-panel",3),d(5,"ion-fab",4)(6,"ion-fab-button",5),_("click",function(){return o.toggleGps({detail:{checked:!o.gpsEnabled}})}),M(7,"ion-icon",6),h()(),d(8,"div",7),_("click",function(){return o.showLocationHelp()}),M(9,"ion-icon",6),d(10,"span"),f(11),h()(),k(12,Be,4,2,"div",8)(13,Ye,5,0,"ion-fab",9)(14,Je,5,0,"ion-fab",10),h()),t&2&&(p(2),v("ngIf",o.showLocationRequestButton),p(),v("ngIf",o.routeTime&&o.routeDistance),p(),v("ngIf",o.showDirectionsPanel&&o.currentDirections.length>0),p(2),v("color",o.gpsEnabled?"success":"medium"),p(),v("name",o.gpsEnabled?"locate":"locate-outline"),p(),ne("active",o.gpsEnabled),p(),v("name",o.gpsEnabled?"location":"location-outline"),p(2),O("GPS ",o.gpsEnabled?"Active":"Inactive",""),p(),v("ngIf",o.isFilterMode&&o.currentDisasterType!=="all"),p(),v("ngIf",o.isFilterMode||o.evacuationCenters.length>0),p(),v("ngIf",o.currentDirections.length>0&&!o.showDirectionsPanel))},dependencies:[q,G,j,pe,he,z,U,F,A,re,Le],styles:['.offline-status-banner[_ngcontent-%COMP%]{position:absolute;top:10px;left:50%;transform:translate(-50%);background:linear-gradient(135deg,#ff6b35,#f7931e);color:#fff;border-radius:20px;padding:8px 16px;display:flex;align-items:center;gap:8px;z-index:1001;box-shadow:0 2px 8px #0003;font-size:14px;font-weight:500;animation:_ngcontent-%COMP%_slideDown .3s ease-out}.offline-status-banner[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}@keyframes _ngcontent-%COMP%_slideDown{0%{opacity:0;transform:translate(-50%) translateY(-20px)}to{opacity:1;transform:translate(-50%) translateY(0)}}#map[_ngcontent-%COMP%]{width:100%;height:100%}.mode-segment[_ngcontent-%COMP%]{position:absolute;left:50%;transform:translate(-50%);top:10px;z-index:1000;background:#ffffffe6;border-radius:20px;padding:4px;width:90%;max-width:400px;box-shadow:0 2px 8px #0000001a}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--background: transparent;--background-checked: var(--ion-color-light);--indicator-color: transparent;--border-radius: 16px;min-height:40px}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   .segment-icon[_ngcontent-%COMP%]{width:24px;height:24px;display:block;margin:0 auto 4px}.mode-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   .segment-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-summary-card[_ngcontent-%COMP%]{position:absolute;left:50%;transform:translate(-50%);top:70px;background:#fffffff2;border-radius:16px;box-shadow:0 2px 8px #0000001a;padding:12px 16px;display:flex;align-items:center;gap:12px;z-index:1000;cursor:pointer;transition:all .2s ease}.route-summary-card[_ngcontent-%COMP%]:hover{background:#fff;box-shadow:0 4px 12px #00000026;transform:translate(-50%) translateY(-2px)}.route-summary-card[_ngcontent-%COMP%]:active{transform:translate(-50%) translateY(0)}.route-summary-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]{line-height:1.3}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-size:16px}.route-summary-card[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   .travel-mode[_ngcontent-%COMP%]{font-size:12px;opacity:.8;margin-top:2px}.route-summary-card[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%]{font-size:18px;margin-left:8px;color:var(--ion-color-medium)}.fab-label[_ngcontent-%COMP%]{position:absolute;right:80px;bottom:30px;background:#fffffff2;padding:8px 16px;border-radius:20px;font-size:14px;color:var(--ion-color-primary);z-index:1000;box-shadow:0 2px 8px #0000001a;font-weight:500}ion-fab-button[activated][_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: white}.gps-status[_ngcontent-%COMP%]{position:absolute;top:10px;left:70px;background:var(--ion-color-medium);border-radius:20px;padding:8px 12px;display:flex;align-items:center;gap:6px;z-index:1000;box-shadow:0 2px 8px #0000001a;font-size:14px;color:#fff;cursor:pointer;transition:all .2s ease}.gps-status[_ngcontent-%COMP%]:hover{background:var(--ion-color-medium-shade);box-shadow:0 4px 12px #00000026;transform:translateY(-2px)}.gps-status[_ngcontent-%COMP%]:active{transform:translateY(0)}.gps-status.active[_ngcontent-%COMP%]{color:#fff;background:var(--ion-color-success)}.gps-status.active[_ngcontent-%COMP%]:hover{background:var(--ion-color-success-shade)}.gps-status.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s infinite;color:#fff}.gps-status[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.gps-status[_ngcontent-%COMP%]:after{content:"?";display:inline-block;width:16px;height:16px;line-height:16px;text-align:center;background:var(--ion-color-medium);color:#fff;border-radius:50%;font-size:12px;margin-left:6px;opacity:.7}.disaster-type-indicator[_ngcontent-%COMP%]{position:absolute;top:10px;right:80px;background:#ffffffe6;border-radius:20px;padding:8px 12px;display:flex;align-items:center;gap:6px;z-index:1000;box-shadow:0 2px 8px #0000001a;font-size:14px;font-weight:500;color:var(--ion-color-dark)}.disaster-type-indicator[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;color:var(--ion-color-primary)}.location-request-container[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:#fffffff2;border-radius:16px;box-shadow:0 4px 16px #00000026;padding:20px;text-align:center;max-width:300px;width:90%;z-index:1001;animation:_ngcontent-%COMP%_fadeIn .5s ease-out}.location-request-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin:10px 0;--border-radius: 10px;--box-shadow: 0 4px 8px rgba(var(--ion-color-primary-rgb), .3);font-weight:600;height:48px}.location-request-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]:active{--box-shadow: 0 2px 4px rgba(var(--ion-color-primary-rgb), .2);transform:translateY(2px)}.location-request-container[_ngcontent-%COMP%]   .location-help-text[_ngcontent-%COMP%]{margin:10px 0 0;font-size:14px;color:var(--ion-color-medium);line-height:1.4}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translate(-50%,-40%)}to{opacity:1;transform:translate(-50%,-50%)}}.map-default-message[_ngcontent-%COMP%]{position:absolute;bottom:30px;left:50%;transform:translate(-50%);background:#fffffff2;border-radius:16px;box-shadow:0 2px 8px #0000001a;padding:12px 16px;text-align:center;max-width:300px;z-index:1000}.map-default-message[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:var(--ion-color-primary);margin-bottom:8px}.map-default-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 5px;font-weight:500;font-size:16px;color:var(--ion-color-dark)}.map-default-message[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:13px;display:block;line-height:1.4}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:.6}50%{opacity:1}to{opacity:.6}}@keyframes _ngcontent-%COMP%_pulsate{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.5);opacity:.4}to{transform:scale(.8);opacity:.8}}.marker-pulse-container[_ngcontent-%COMP%]{position:relative}.marker-pulse[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;width:50px;height:50px;margin-top:-25px;margin-left:-25px;border-radius:50%;z-index:100;pointer-events:none;animation:_ngcontent-%COMP%_pulsate 1.5s ease-out infinite;box-shadow:0 0 10px #00000080}[_nghost-%COMP%]     .popup-button{background-color:var(--ion-color-primary);color:#fff;border:none;border-radius:4px;padding:6px 12px;font-size:14px;cursor:pointer;margin-top:8px;transition:background-color .2s}[_nghost-%COMP%]     .popup-button:hover{background-color:var(--ion-color-primary-shade)}[_nghost-%COMP%]     .evacuation-popup h3{margin:0 0 8px;font-size:16px;font-weight:600}[_nghost-%COMP%]     .evacuation-popup p{margin:4px 0;font-size:14px}.evacuation-details-modal[_ngcontent-%COMP%]{--border-radius: 16px 16px 0 0;--backdrop-opacity: .4}']})}}return u})();export{Pt as MapPage};
