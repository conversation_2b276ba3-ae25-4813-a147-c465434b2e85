<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MobileUser extends Model
{
    use HasFactory;

    protected $fillable = [
        'full_name',
        'mobile_number',
        'age',
        'gender',
        'address',
        'barangay',
        'status',
        'user_id', // Link to users table for onboarding tracking
    ];

    protected $casts = [
        'age' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Default status
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($mobileUser) {
            if (empty($mobileUser->status)) {
                $mobileUser->status = 'Active';
            }
        });
    }
}