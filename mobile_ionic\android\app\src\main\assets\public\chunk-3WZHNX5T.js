import{a as oe}from"./chunk-O4HFYYTJ.js";import"./chunk-GF36QAB3.js";import{a as ie}from"./chunk-WPPT3EJF.js";import{B as a,C as u,Db as G,F as f,G as m,Hb as H,Ib as I,K as o,Kb as U,L as r,Lb as J,M as l,Mb as K,Ob as X,P,Pb as Y,Q as p,Qb as Z,R as h,Tb as ee,U as F,Wb as te,Y as s,Yb as ne,Z as _,_ as z,aa as Q,ba as V,ca as W,cb as N,da as b,dc as w,ec as E,fb as R,fc as T,ib as q,m as x,ma as j,na as A,pa as O,r as v,ra as D,s as M,tb as B,ub as $,vb as y,ya as S}from"./chunk-E442IOFQ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-MJZLGLVY.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-7D2EH4XU.js";import"./chunk-MMIXVVWR.js";import"./chunk-SV7S5NYR.js";import{g as L}from"./chunk-2R6CW7ES.js";var re=(()=>{class i{constructor(e,t,n){this.modalCtrl=e,this.router=t,this.toastCtrl=n}dismiss(){this.modalCtrl.dismiss()}viewOnMap(){this.modalCtrl.dismiss(),this.router.navigate(["/tabs/map"],{queryParams:{lat:this.center.latitude,lng:this.center.longitude,name:this.center.name,viewOnly:"true"}}),this.toastCtrl.create({message:`Showing ${this.center.name} on map`,duration:2e3,color:"success"}).then(e=>e.present())}getDirections(){this.modalCtrl.dismiss(),this.router.navigate(["/tabs/map"],{queryParams:{lat:this.center.latitude,lng:this.center.longitude,name:this.center.name,directions:"true"}}),this.toastCtrl.create({message:`Getting directions to ${this.center.name}`,duration:2e3,color:"success"}).then(e=>e.present())}getDisasterTypeIcon(e){if(!e)return"alert-circle-outline";let t=e.toLowerCase();return e.startsWith("Others:")?"help-circle-outline":t.includes("earthquake")||t.includes("quake")?"earth-outline":t.includes("flood")||t.includes("flash")?"water-outline":t.includes("typhoon")||t.includes("storm")?"thunderstorm-outline":t.includes("fire")?"flame-outline":t.includes("landslide")||t.includes("slide")?"triangle-outline":t.includes("others")?"help-circle-outline":"alert-circle-outline"}getStatusColor(e){if(!e)return"medium";let t=e.toLowerCase();return t.includes("active")||t.includes("open")?"success":t.includes("inactive")||t.includes("closed")?"warning":t.includes("full")?"danger":"medium"}static{this.\u0275fac=function(t){return new(t||i)(u(w),u(S),u(E))}}static{this.\u0275cmp=x({type:i,selectors:[["app-evacuation-center-modal"]],inputs:{center:"center"},standalone:!0,features:[b],decls:23,vars:3,consts:[[1,"modal-container"],[1,"close-button",3,"click"],["name","close-circle","color","danger"],[1,"center-name"],[1,"info-section"],[1,"info-label"],[1,"info-value","contact"],["name","call-outline"],[1,"info-value","address"],["name","location-outline"],[1,"directions-button"],["expand","block","color","primary",3,"click"],["name","navigate","slot","start"]],template:function(t,n){t&1&&(o(0,"div",0)(1,"div",1),p("click",function(){return n.dismiss()}),l(2,"ion-icon",2),r(),o(3,"h2",3),s(4),r(),o(5,"div",4)(6,"div",5),s(7,"Contact Number"),r(),o(8,"div",6),l(9,"ion-icon",7),o(10,"span"),s(11),r()()(),o(12,"div",4)(13,"div",5),s(14,"Address"),r(),o(15,"div",8),l(16,"ion-icon",9),o(17,"span"),s(18),r()()(),o(19,"div",10)(20,"ion-button",11),p("click",function(){return n.getDirections()}),l(21,"ion-icon",12),s(22," Get Directions "),r()()()),t&2&&(a(4),_(n.center.name),a(7),_(n.center.contact||"No contact available"),a(7),_(n.center.address))},dependencies:[T,y,I,O],styles:[".modal-container[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px;overflow:hidden;width:100%;max-width:350px;margin:0 auto;position:relative;box-shadow:0 4px 12px #0000001a}.close-button[_ngcontent-%COMP%]{position:absolute;top:10px;right:10px;z-index:10}.close-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;background:#fff;border-radius:50%}.center-name[_ngcontent-%COMP%]{font-size:18px;font-weight:600;text-align:center;margin:15px 15px 10px;color:#000}.info-section[_ngcontent-%COMP%]{padding:10px 15px;border-bottom:1px solid #f0f0f0}.info-section[_ngcontent-%COMP%]:last-of-type{border-bottom:none}.info-label[_ngcontent-%COMP%]{font-size:14px;color:#09f;margin-bottom:5px}.info-value[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:15px;color:#333}.info-value[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px;font-size:18px;min-width:18px}.info-value.contact[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#333}.info-value.address[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#ff4961}.directions-button[_ngcontent-%COMP%]{padding:10px 15px 15px}.directions-button[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 8px;--background: #0099ff;font-weight:500;margin:0}.directions-button[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:5px}"]})}}return i})();function de(i,g){i&1&&(o(0,"ion-text",12)(1,"p"),s(2,"Search by center name, address, or disaster type"),r()())}function me(i,g){i&1&&(o(0,"div",13),l(1,"ion-spinner",14),o(2,"ion-text",15)(3,"p"),s(4,"Loading evacuation centers..."),r()()())}function ue(i,g){if(i&1){let e=P();o(0,"div",16),l(1,"ion-icon",17),o(2,"ion-text",18)(3,"p"),s(4),r()(),o(5,"ion-button",19),p("click",function(){v(e);let n=h();return M(n.loadEvacuationCenters())}),l(6,"ion-icon",20),s(7," Try Again "),r()()}if(i&2){let e=h();a(4),_(e.errorMessage)}}function ge(i,g){if(i&1&&(o(0,"ion-badge",26),s(1),r()),i&2){let e=h(2).$implicit;F("color",e.status==="Active"?"success":"warning"),a(),z(" ",e.status," ")}}function pe(i,g){if(i&1&&(o(0,"p")(1,"ion-badge",24),s(2),r(),f(3,ge,2,2,"ion-badge",25),r()),i&2){let e=h().$implicit;a(2),_(e.disaster_type),a(),m("ngIf",e.status)}}function he(i,g){if(i&1){let e=P();o(0,"ion-item",22),p("click",function(){let n=v(e).$implicit,d=h(2);return M(d.viewOnMap(n))}),l(1,"ion-icon",23),o(2,"ion-label")(3,"h2"),s(4),r(),o(5,"p"),s(6),r(),f(7,pe,4,2,"p",9),r()()}if(i&2){let e=g.$implicit;a(4),_(e.name),a(2),_(e.address),a(),m("ngIf",e.disaster_type)}}function _e(i,g){if(i&1&&(o(0,"ion-list"),f(1,he,8,3,"ion-item",21),r()),i&2){let e=h();a(),m("ngForOf",e.locations)}}function fe(i,g){if(i&1&&(o(0,"div",27),l(1,"ion-icon",28),o(2,"ion-text",15)(3,"p"),s(4),r()()()),i&2){let e=h();a(4),z('No evacuation centers found matching "',e.searchQuery,'"')}}function Ce(i,g){if(i&1){let e=P();o(0,"div",29),l(1,"ion-icon",30),o(2,"ion-text",15)(3,"h3"),s(4,"Search for Evacuation Centers"),r(),o(5,"p"),s(6,"Enter a name, address, or disaster type to find evacuation centers"),r()(),o(7,"ion-button",31),p("click",function(){v(e);let n=h();return n.searchQuery="all",M(n.onSearch({target:{value:"all"}}))}),s(8," Show All Centers "),r()()}}var ke=(()=>{class i{constructor(e,t,n,d,c){this.http=e,this.loadingService=t,this.toastCtrl=n,this.router=d,this.modalCtrl=c,this.searchQuery="",this.locations=[],this.allCenters=[],this.isLoading=!1,this.hasError=!1,this.errorMessage=""}ngOnInit(){this.loadEvacuationCenters()}loadEvacuationCenters(){return L(this,null,function*(){yield this.loadingService.showLoading("Loading evacuation centers..."),this.isLoading=!0;try{this.http.get(`${ie.apiUrl}/evacuation-centers`).subscribe({next:e=>{console.log("Loaded evacuation centers:",e),this.allCenters=e.data||[],this.isLoading=!1,this.loadingService.dismissLoading()},error:e=>{console.error("Error loading evacuation centers:",e),this.hasError=!0,this.errorMessage="Failed to load evacuation centers. Please try again later.",this.isLoading=!1,this.loadingService.dismissLoading(),this.toastCtrl.create({message:"Failed to load evacuation centers. Please try again later.",duration:3e3,color:"danger"}).then(t=>t.present())}})}catch(e){console.error("Exception loading evacuation centers:",e),this.hasError=!0,this.errorMessage="An unexpected error occurred. Please try again later.",this.isLoading=!1,this.loadingService.dismissLoading()}})}onSearch(e){let t=e.target.value.toLowerCase().trim();if(console.log("Searching for:",t),!t){this.locations=[];return}this.locations=this.allCenters.filter(n=>{let d=n.name.toLowerCase().startsWith(t),c=n.name.toLowerCase().includes(t),C=n.address?.toLowerCase().includes(t),k=!1;return n.disaster_type&&(Array.isArray(n.disaster_type)?k=n.disaster_type.some(ae=>ae.toLowerCase().includes(t)):k=n.disaster_type.toLowerCase().includes(t)),d||c||C||k}),this.locations.sort((n,d)=>{let c=n.name.toLowerCase().startsWith(t),C=d.name.toLowerCase().startsWith(t);return c&&!C?-1:!c&&C?1:0})}clearSearch(){this.searchQuery="",this.locations=[]}refreshCenters(e){this.loadEvacuationCenters().then(()=>{e.target.complete()})}viewOnMap(e){return L(this,null,function*(){yield(yield this.modalCtrl.create({component:re,componentProps:{center:e},cssClass:"evacuation-center-modal"})).present()})}static{this.\u0275fac=function(t){return new(t||i)(u(D),u(oe),u(E),u(S),u(w))}}static{this.\u0275cmp=x({type:i,selectors:[["app-search"]],standalone:!0,features:[b],decls:14,vars:8,consts:[[3,"translucent"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","pullingText","Pull to refresh","refreshingSpinner","circles","refreshingText","Refreshing..."],[1,"search-container"],["placeholder","Search evacuation centers by name","animated","true","showCancelButton","focus","debounce","300",3,"ngModelChange","ionInput","ionClear","ngModel"],["color","medium","class","search-hint",4,"ngIf"],[1,"search-results"],["class","loading-container",4,"ngIf"],["class","error-container",4,"ngIf"],[4,"ngIf"],["class","no-results",4,"ngIf"],["class","empty-state",4,"ngIf"],["color","medium",1,"search-hint"],[1,"loading-container"],["name","circles"],["color","medium"],[1,"error-container"],["name","alert-circle-outline","color","danger","size","large"],["color","danger"],["fill","outline","size","small",3,"click"],["name","refresh-outline","slot","start"],["button","","detail","",3,"click",4,"ngFor","ngForOf"],["button","","detail","",3,"click"],["name","location-outline","slot","start","color","primary"],["color","secondary"],[3,"color",4,"ngIf"],[3,"color"],[1,"no-results"],["name","search-outline","color","medium","size","large"],[1,"empty-state"],["name","search","color","primary","size","large"],["fill","outline",3,"click"]],template:function(t,n){t&1&&(o(0,"ion-header",0),l(1,"ion-toolbar"),r(),o(2,"ion-content")(3,"ion-refresher",1),p("ionRefresh",function(c){return n.refreshCenters(c)}),l(4,"ion-refresher-content",2),r(),o(5,"div",3)(6,"ion-searchbar",4),W("ngModelChange",function(c){return V(n.searchQuery,c)||(n.searchQuery=c),c}),p("ionInput",function(c){return n.onSearch(c)})("ionClear",function(){return n.clearSearch()}),r(),f(7,de,3,0,"ion-text",5),r(),o(8,"div",6),f(9,me,5,0,"div",7)(10,ue,8,1,"div",8)(11,_e,2,1,"ion-list",9)(12,fe,5,1,"div",10)(13,Ce,9,0,"div",11),r()()),t&2&&(m("translucent",!0),a(6),Q("ngModel",n.searchQuery),a(),m("ngIf",!n.searchQuery),a(2),m("ngIf",n.isLoading),a(),m("ngIf",n.hasError),a(),m("ngIf",n.locations.length>0),a(),m("ngIf",n.searchQuery&&n.locations.length===0&&!n.isLoading&&!n.hasError),a(),m("ngIf",!n.searchQuery&&!n.isLoading&&!n.hasError&&n.allCenters.length>0))},dependencies:[T,$,y,G,H,I,U,J,K,X,Y,Z,ee,te,ne,B,O,j,A,q,N,R],styles:["ion-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6,#2563eb);box-shadow:0 4px 12px #3b82f633}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--color: white}ion-content[_ngcontent-%COMP%]{--background: #f8f9fa}.search-container[_ngcontent-%COMP%]{padding:10px 16px 0}.search-container[_ngcontent-%COMP%]   ion-searchbar[_ngcontent-%COMP%]{--border-radius: 10px;--box-shadow: 0 2px 6px rgba(0, 0, 0, .1);--placeholder-color: var(--ion-color-medium);--icon-color: var(--ion-color-primary)}.search-container[_ngcontent-%COMP%]   .search-hint[_ngcontent-%COMP%]{font-size:12px;margin:0 0 10px 16px;display:block}.search-results[_ngcontent-%COMP%]{padding:0 16px 16px}.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;padding:32px 16px;min-height:200px}.loading-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:48px;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0;font-size:16px}.loading-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:16px}ion-list[_ngcontent-%COMP%]{background:transparent;padding:0}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--inner-padding-end: 16px;--background: white;margin-bottom:10px;border-radius:10px;--border-radius: 10px;box-shadow:0 2px 4px #0000000d}ion-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:16px;font-weight:500;margin-bottom:4px;color:var(--ion-color-dark)}ion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:var(--ion-color-medium);margin:2px 0}ion-item[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%]{margin-right:6px;padding:4px 8px;border-radius:4px}.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:500;margin:8px 0;color:var(--ion-color-dark)}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:20px}"]})}}return i})();export{ke as SearchPage};
