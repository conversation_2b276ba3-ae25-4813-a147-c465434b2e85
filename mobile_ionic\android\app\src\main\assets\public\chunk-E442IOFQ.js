import{b as Pc}from"./chunk-SV2ZKNWA.js";import{a as Eo}from"./chunk-HC6MZPB3.js";import{c as Ac,e as Rc,f as Nc,g as Oc,h as kc}from"./chunk-Q5Q6EGIP.js";import{a as wo}from"./chunk-RS5W3JWO.js";import{c as xc}from"./chunk-ZJ5IMUT4.js";import{m as Gp}from"./chunk-SGSBBWFA.js";import{b as wc,c as Ec,d as Mc,e as _c,f as Sc}from"./chunk-APL3YEA6.js";import{g as Pp,h as Fp}from"./chunk-HSXX7Y3C.js";import{a as Hp,d as zp}from"./chunk-FUGLTCJS.js";import{a as rn,d as Bp,e as Up,f as $p,h as bc}from"./chunk-XTVTS2NW.js";import{a as Tc}from"./chunk-NMYJD6OP.js";import{a as He,b as jp,c as Lp,d as Vp,e as Co,f as bo}from"./chunk-C5RQ2IC2.js";import{b as nn}from"./chunk-SV7S5NYR.js";import{a as b,b as V,d as Cc,g as ve}from"./chunk-2R6CW7ES.js";function on(e){let t=e(r=>{Error.call(r),r.stack=new Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var ct=on(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function k(e){return typeof e=="function"}var Mo=on(e=>function(t){e(this),this.message=t?`${t.length} errors occurred during unsubscription:
${t.map((r,i)=>`${i+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=t});function Fn(e,n){if(e){let t=e.indexOf(n);0<=t&&e.splice(t,1)}}var fe=class e{constructor(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let n;if(!this.closed){this.closed=!0;let{_parentage:t}=this;if(t)if(this._parentage=null,Array.isArray(t))for(let o of t)o.remove(this);else t.remove(this);let{initialTeardown:r}=this;if(k(r))try{r()}catch(o){n=o instanceof Mo?o.errors:[o]}let{_finalizers:i}=this;if(i){this._finalizers=null;for(let o of i)try{Wp(o)}catch(s){n=n??[],s instanceof Mo?n=[...n,...s.errors]:n.push(s)}}if(n)throw new Mo(n)}}add(n){var t;if(n&&n!==this)if(this.closed)Wp(n);else{if(n instanceof e){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=(t=this._finalizers)!==null&&t!==void 0?t:[]).push(n)}}_hasParent(n){let{_parentage:t}=this;return t===n||Array.isArray(t)&&t.includes(n)}_addParent(n){let{_parentage:t}=this;this._parentage=Array.isArray(t)?(t.push(n),t):t?[t,n]:n}_removeParent(n){let{_parentage:t}=this;t===n?this._parentage=null:Array.isArray(t)&&Fn(t,n)}remove(n){let{_finalizers:t}=this;t&&Fn(t,n),n instanceof e&&n._removeParent(this)}};fe.EMPTY=(()=>{let e=new fe;return e.closed=!0,e})();var Fc=fe.EMPTY;function _o(e){return e instanceof fe||e&&"closed"in e&&k(e.remove)&&k(e.add)&&k(e.unsubscribe)}function Wp(e){k(e)?e():e.unsubscribe()}var lt={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var hr={setTimeout(e,n,...t){let{delegate:r}=hr;return r?.setTimeout?r.setTimeout(e,n,...t):setTimeout(e,n,...t)},clearTimeout(e){let{delegate:n}=hr;return(n?.clearTimeout||clearTimeout)(e)},delegate:void 0};function So(e){hr.setTimeout(()=>{let{onUnhandledError:n}=lt;if(n)n(e);else throw e})}function hi(){}var qp=jc("C",void 0,void 0);function Zp(e){return jc("E",void 0,e)}function Qp(e){return jc("N",e,void 0)}function jc(e,n,t){return{kind:e,value:n,error:t}}var jn=null;function pr(e){if(lt.useDeprecatedSynchronousErrorHandling){let n=!jn;if(n&&(jn={errorThrown:!1,error:null}),e(),n){let{errorThrown:t,error:r}=jn;if(jn=null,t)throw r}}else e()}function Yp(e){lt.useDeprecatedSynchronousErrorHandling&&jn&&(jn.errorThrown=!0,jn.error=e)}var Ln=class extends fe{constructor(n){super(),this.isStopped=!1,n?(this.destination=n,_o(n)&&n.add(this)):this.destination=Z0}static create(n,t,r){return new sn(n,t,r)}next(n){this.isStopped?Vc(Qp(n),this):this._next(n)}error(n){this.isStopped?Vc(Zp(n),this):(this.isStopped=!0,this._error(n))}complete(){this.isStopped?Vc(qp,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(n){this.destination.next(n)}_error(n){try{this.destination.error(n)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},W0=Function.prototype.bind;function Lc(e,n){return W0.call(e,n)}var Bc=class{constructor(n){this.partialObserver=n}next(n){let{partialObserver:t}=this;if(t.next)try{t.next(n)}catch(r){To(r)}}error(n){let{partialObserver:t}=this;if(t.error)try{t.error(n)}catch(r){To(r)}else To(n)}complete(){let{partialObserver:n}=this;if(n.complete)try{n.complete()}catch(t){To(t)}}},sn=class extends Ln{constructor(n,t,r){super();let i;if(k(n)||!n)i={next:n??void 0,error:t??void 0,complete:r??void 0};else{let o;this&&lt.useDeprecatedNextContext?(o=Object.create(n),o.unsubscribe=()=>this.unsubscribe(),i={next:n.next&&Lc(n.next,o),error:n.error&&Lc(n.error,o),complete:n.complete&&Lc(n.complete,o)}):i=n}this.destination=new Bc(i)}};function To(e){lt.useDeprecatedSynchronousErrorHandling?Yp(e):So(e)}function q0(e){throw e}function Vc(e,n){let{onStoppedNotification:t}=lt;t&&hr.setTimeout(()=>t(e,n))}var Z0={closed:!0,next:hi,error:q0,complete:hi};function Q0(e,n){let t=typeof n=="object";return new Promise((r,i)=>{let o=new sn({next:s=>{r(s),o.unsubscribe()},error:i,complete:()=>{t?r(n.defaultValue):i(new ct)}});e.subscribe(o)})}var xo=class extends fe{constructor(n,t){super()}schedule(n,t=0){return this}};var pi={setInterval(e,n,...t){let{delegate:r}=pi;return r?.setInterval?r.setInterval(e,n,...t):setInterval(e,n,...t)},clearInterval(e){let{delegate:n}=pi;return(n?.clearInterval||clearInterval)(e)},delegate:void 0};var Ao=class extends xo{constructor(n,t){super(n,t),this.scheduler=n,this.work=t,this.pending=!1}schedule(n,t=0){var r;if(this.closed)return this;this.state=n;let i=this.id,o=this.scheduler;return i!=null&&(this.id=this.recycleAsyncId(o,i,t)),this.pending=!0,this.delay=t,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(o,this.id,t),this}requestAsyncId(n,t,r=0){return pi.setInterval(n.flush.bind(n,this),r)}recycleAsyncId(n,t,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return t;t!=null&&pi.clearInterval(t)}execute(n,t){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(n,t);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(n,t){let r=!1,i;try{this.work(n)}catch(o){r=!0,i=o||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),i}unsubscribe(){if(!this.closed){let{id:n,scheduler:t}=this,{actions:r}=t;this.work=this.state=this.scheduler=null,this.pending=!1,Fn(r,this),n!=null&&(this.id=this.recycleAsyncId(t,n,null)),this.delay=null,super.unsubscribe()}}};var Uc={now(){return(Uc.delegate||Date).now()},delegate:void 0};var gr=class e{constructor(n,t=e.now){this.schedulerActionCtor=n,this.now=t}schedule(n,t=0,r){return new this.schedulerActionCtor(this,n).schedule(r,t)}};gr.now=Uc.now;var Ro=class extends gr{constructor(n,t=gr.now){super(n,t),this.actions=[],this._active=!1}flush(n){let{actions:t}=this;if(this._active){t.push(n);return}let r;this._active=!0;do if(r=n.execute(n.state,n.delay))break;while(n=t.shift());if(this._active=!1,r){for(;n=t.shift();)n.unsubscribe();throw r}}};var gi=new Ro(Ao),Kp=gi;var mr=typeof Symbol=="function"&&Symbol.observable||"@@observable";function xe(e){return e}function $c(...e){return Hc(e)}function Hc(e){return e.length===0?xe:e.length===1?e[0]:function(t){return e.reduce((r,i)=>i(r),t)}}var W=(()=>{class e{constructor(t){t&&(this._subscribe=t)}lift(t){let r=new e;return r.source=this,r.operator=t,r}subscribe(t,r,i){let o=K0(t)?t:new sn(t,r,i);return pr(()=>{let{operator:s,source:a}=this;o.add(s?s.call(o,a):a?this._subscribe(o):this._trySubscribe(o))}),o}_trySubscribe(t){try{return this._subscribe(t)}catch(r){t.error(r)}}forEach(t,r){return r=Xp(r),new r((i,o)=>{let s=new sn({next:a=>{try{t(a)}catch(c){o(c),s.unsubscribe()}},error:o,complete:i});this.subscribe(s)})}_subscribe(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)}[mr](){return this}pipe(...t){return Hc(t)(this)}toPromise(t){return t=Xp(t),new t((r,i)=>{let o;this.subscribe(s=>o=s,s=>i(s),()=>r(o))})}}return e.create=n=>new e(n),e})();function Xp(e){var n;return(n=e??lt.Promise)!==null&&n!==void 0?n:Promise}function Y0(e){return e&&k(e.next)&&k(e.error)&&k(e.complete)}function K0(e){return e&&e instanceof Ln||Y0(e)&&_o(e)}function No(e){return e&&k(e.schedule)}function Oo(e){return e instanceof Date&&!isNaN(e)}function ko(e=0,n,t=Kp){let r=-1;return n!=null&&(No(n)?t=n:r=n),new W(i=>{let o=Oo(e)?+e-t.now():e;o<0&&(o=0);let s=0;return t.schedule(function(){i.closed||(i.next(s++),0<=r?this.schedule(void 0,r):i.complete())},o)})}function X0(e=0,n=gi){return e<0&&(e=0),ko(e,e,n)}function zc(e){return k(e?.lift)}function $(e){return n=>{if(zc(n))return n.lift(function(t){try{return e(t,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function L(e,n,t,r,i){return new Gc(e,n,t,r,i)}var Gc=class extends Ln{constructor(n,t,r,i,o,s){super(n),this.onFinalize=o,this.shouldUnsubscribe=s,this._next=t?function(a){try{t(a)}catch(c){n.error(c)}}:super._next,this._error=i?function(a){try{i(a)}catch(c){n.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){n.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:t}=this;super.unsubscribe(),!t&&((n=this.onFinalize)===null||n===void 0||n.call(this))}}};function vr(){return $((e,n)=>{let t=null;e._refCount++;let r=L(n,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){t=null;return}let i=e._connection,o=t;t=null,i&&(!o||i===o)&&i.unsubscribe(),n.unsubscribe()});e.subscribe(r),r.closed||(t=e.connect())})}var yr=class extends W{constructor(n,t){super(),this.source=n,this.subjectFactory=t,this._subject=null,this._refCount=0,this._connection=null,zc(n)&&(this.lift=n.lift)}_subscribe(n){return this.getSubject().subscribe(n)}getSubject(){let n=this._subject;return(!n||n.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:n}=this;this._subject=this._connection=null,n?.unsubscribe()}connect(){let n=this._connection;if(!n){n=this._connection=new fe;let t=this.getSubject();n.add(this.source.subscribe(L(t,void 0,()=>{this._teardown(),t.complete()},r=>{this._teardown(),t.error(r)},()=>this._teardown()))),n.closed&&(this._connection=null,n=fe.EMPTY)}return n}refCount(){return vr()(this)}};var Jp=on(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var ae=(()=>{class e extends W{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(t){let r=new Po(this,this);return r.operator=t,r}_throwIfClosed(){if(this.closed)throw new Jp}next(t){pr(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(t)}})}error(t){pr(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=t;let{observers:r}=this;for(;r.length;)r.shift().error(t)}})}complete(){pr(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:t}=this;for(;t.length;)t.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var t;return((t=this.observers)===null||t===void 0?void 0:t.length)>0}_trySubscribe(t){return this._throwIfClosed(),super._trySubscribe(t)}_subscribe(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)}_innerSubscribe(t){let{hasError:r,isStopped:i,observers:o}=this;return r||i?Fc:(this.currentObservers=null,o.push(t),new fe(()=>{this.currentObservers=null,Fn(o,t)}))}_checkFinalizedStatuses(t){let{hasError:r,thrownError:i,isStopped:o}=this;r?t.error(i):o&&t.complete()}asObservable(){let t=new W;return t.source=this,t}}return e.create=(n,t)=>new Po(n,t),e})(),Po=class extends ae{constructor(n,t){super(),this.destination=n,this.source=t}next(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.next)===null||r===void 0||r.call(t,n)}error(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.error)===null||r===void 0||r.call(t,n)}complete(){var n,t;(t=(n=this.destination)===null||n===void 0?void 0:n.complete)===null||t===void 0||t.call(n)}_subscribe(n){var t,r;return(r=(t=this.source)===null||t===void 0?void 0:t.subscribe(n))!==null&&r!==void 0?r:Fc}};var ye=class extends ae{constructor(n){super(),this._value=n}get value(){return this.getValue()}_subscribe(n){let t=super._subscribe(n);return!t.closed&&n.next(this._value),t}getValue(){let{hasError:n,thrownError:t,_value:r}=this;if(n)throw t;return this._throwIfClosed(),r}next(n){super.next(this._value=n)}};var ze=new W(e=>e.complete());function eg(e){return e[e.length-1]}function Fo(e){return k(eg(e))?e.pop():void 0}function an(e){return No(eg(e))?e.pop():void 0}function w(e,n,t,r){var i=arguments.length,o=i<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,t):r,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")o=Reflect.decorate(e,n,t,r);else for(var a=e.length-1;a>=0;a--)(s=e[a])&&(o=(i<3?s(o):i>3?s(n,t,o):s(n,t))||o);return i>3&&o&&Object.defineProperty(n,t,o),o}function ng(e,n,t,r){function i(o){return o instanceof t?o:new t(function(s){s(o)})}return new(t||(t=Promise))(function(o,s){function a(d){try{l(r.next(d))}catch(f){s(f)}}function c(d){try{l(r.throw(d))}catch(f){s(f)}}function l(d){d.done?o(d.value):i(d.value).then(a,c)}l((r=r.apply(e,n||[])).next())})}function tg(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function Vn(e){return this instanceof Vn?(this.v=e,this):new Vn(e)}function rg(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=t.apply(e,n||[]),i,o=[];return i=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),i[Symbol.asyncIterator]=function(){return this},i;function s(h){return function(y){return Promise.resolve(y).then(h,f)}}function a(h,y){r[h]&&(i[h]=function(M){return new Promise(function(T,F){o.push([h,M,T,F])>1||c(h,M)})},y&&(i[h]=y(i[h])))}function c(h,y){try{l(r[h](y))}catch(M){p(o[0][3],M)}}function l(h){h.value instanceof Vn?Promise.resolve(h.value.v).then(d,f):p(o[0][2],h)}function d(h){c("next",h)}function f(h){c("throw",h)}function p(h,y){h(y),o.shift(),o.length&&c(o[0][0],o[0][1])}}function ig(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=e[Symbol.asyncIterator],t;return n?n.call(e):(e=typeof tg=="function"?tg(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(o){t[o]=e[o]&&function(s){return new Promise(function(a,c){s=e[o](s),i(a,c,s.done,s.value)})}}function i(o,s,a,c){Promise.resolve(c).then(function(l){o({value:l,done:a})},s)}}var Dr=e=>e&&typeof e.length=="number"&&typeof e!="function";function jo(e){return k(e?.then)}function Lo(e){return k(e[mr])}function Vo(e){return Symbol.asyncIterator&&k(e?.[Symbol.asyncIterator])}function Bo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function J0(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Uo=J0();function $o(e){return k(e?.[Uo])}function Ho(e){return rg(this,arguments,function*(){let t=e.getReader();try{for(;;){let{value:r,done:i}=yield Vn(t.read());if(i)return yield Vn(void 0);yield yield Vn(r)}}finally{t.releaseLock()}})}function zo(e){return k(e?.getReader)}function ie(e){if(e instanceof W)return e;if(e!=null){if(Lo(e))return eC(e);if(Dr(e))return tC(e);if(jo(e))return nC(e);if(Vo(e))return og(e);if($o(e))return rC(e);if(zo(e))return iC(e)}throw Bo(e)}function eC(e){return new W(n=>{let t=e[mr]();if(k(t.subscribe))return t.subscribe(n);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function tC(e){return new W(n=>{for(let t=0;t<e.length&&!n.closed;t++)n.next(e[t]);n.complete()})}function nC(e){return new W(n=>{e.then(t=>{n.closed||(n.next(t),n.complete())},t=>n.error(t)).then(null,So)})}function rC(e){return new W(n=>{for(let t of e)if(n.next(t),n.closed)return;n.complete()})}function og(e){return new W(n=>{oC(e,n).catch(t=>n.error(t))})}function iC(e){return og(Ho(e))}function oC(e,n){var t,r,i,o;return ng(this,void 0,void 0,function*(){try{for(t=ig(e);r=yield t.next(),!r.done;){let s=r.value;if(n.next(s),n.closed)return}}catch(s){i={error:s}}finally{try{r&&!r.done&&(o=t.return)&&(yield o.call(t))}finally{if(i)throw i.error}}n.complete()})}function Ne(e,n,t,r=0,i=!1){let o=n.schedule(function(){t(),i?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(o),!i)return o}function Go(e,n=0){return $((t,r)=>{t.subscribe(L(r,i=>Ne(r,e,()=>r.next(i),n),()=>Ne(r,e,()=>r.complete(),n),i=>Ne(r,e,()=>r.error(i),n)))})}function Wo(e,n=0){return $((t,r)=>{r.add(e.schedule(()=>t.subscribe(r),n))})}function sg(e,n){return ie(e).pipe(Wo(n),Go(n))}function ag(e,n){return ie(e).pipe(Wo(n),Go(n))}function cg(e,n){return new W(t=>{let r=0;return n.schedule(function(){r===e.length?t.complete():(t.next(e[r++]),t.closed||this.schedule())})})}function lg(e,n){return new W(t=>{let r;return Ne(t,n,()=>{r=e[Uo](),Ne(t,n,()=>{let i,o;try{({value:i,done:o}=r.next())}catch(s){t.error(s);return}o?t.complete():t.next(i)},0,!0)}),()=>k(r?.return)&&r.return()})}function qo(e,n){if(!e)throw new Error("Iterable cannot be null");return new W(t=>{Ne(t,n,()=>{let r=e[Symbol.asyncIterator]();Ne(t,n,()=>{r.next().then(i=>{i.done?t.complete():t.next(i.value)})},0,!0)})})}function ug(e,n){return qo(Ho(e),n)}function dg(e,n){if(e!=null){if(Lo(e))return sg(e,n);if(Dr(e))return cg(e,n);if(jo(e))return ag(e,n);if(Vo(e))return qo(e,n);if($o(e))return lg(e,n);if(zo(e))return ug(e,n)}throw Bo(e)}function ce(e,n){return n?dg(e,n):ie(e)}function O(...e){let n=an(e);return ce(e,n)}function Ir(e,n){let t=k(e)?e:()=>e,r=i=>i.error(t());return new W(n?i=>n.schedule(r,0,i):r)}function Wc(e){return!!e&&(e instanceof W||k(e.lift)&&k(e.subscribe))}var sC=on(e=>function(t=null){e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=t});function aC(e,n){let{first:t,each:r,with:i=cC,scheduler:o=n??gi,meta:s=null}=Oo(e)?{first:e}:typeof e=="number"?{each:e}:e;if(t==null&&r==null)throw new TypeError("No timeout provided.");return $((a,c)=>{let l,d,f=null,p=0,h=y=>{d=Ne(c,o,()=>{try{l.unsubscribe(),ie(i({meta:s,lastValue:f,seen:p})).subscribe(c)}catch(M){c.error(M)}},y)};l=a.subscribe(L(c,y=>{d?.unsubscribe(),p++,c.next(f=y),r>0&&h(r)},void 0,void 0,()=>{d?.closed||d?.unsubscribe(),f=null})),!p&&h(t!=null?typeof t=="number"?t:+t-o.now():r)})}function cC(e){throw new sC(e)}function B(e,n){return $((t,r)=>{let i=0;t.subscribe(L(r,o=>{r.next(e.call(n,o,i++))}))})}var{isArray:lC}=Array;function uC(e,n){return lC(n)?e(...n):e(n)}function Cr(e){return B(n=>uC(e,n))}var{isArray:dC}=Array,{getPrototypeOf:fC,prototype:hC,keys:pC}=Object;function Zo(e){if(e.length===1){let n=e[0];if(dC(n))return{args:n,keys:null};if(gC(n)){let t=pC(n);return{args:t.map(r=>n[r]),keys:t}}}return{args:e,keys:null}}function gC(e){return e&&typeof e=="object"&&fC(e)===hC}function Qo(e,n){return e.reduce((t,r,i)=>(t[r]=n[i],t),{})}function Bn(...e){let n=an(e),t=Fo(e),{args:r,keys:i}=Zo(e);if(r.length===0)return ce([],n);let o=new W(mC(r,n,i?s=>Qo(i,s):xe));return t?o.pipe(Cr(t)):o}function mC(e,n,t=xe){return r=>{fg(n,()=>{let{length:i}=e,o=new Array(i),s=i,a=i;for(let c=0;c<i;c++)fg(n,()=>{let l=ce(e[c],n),d=!1;l.subscribe(L(r,f=>{o[c]=f,d||(d=!0,a--),a||r.next(t(o.slice()))},()=>{--s||r.complete()}))},r)},r)}}function fg(e,n,t){e?Ne(t,e,n):n()}function hg(e,n,t,r,i,o,s,a){let c=[],l=0,d=0,f=!1,p=()=>{f&&!c.length&&!l&&n.complete()},h=M=>l<r?y(M):c.push(M),y=M=>{o&&n.next(M),l++;let T=!1;ie(t(M,d++)).subscribe(L(n,F=>{i?.(F),o?h(F):n.next(F)},()=>{T=!0},void 0,()=>{if(T)try{for(l--;c.length&&l<r;){let F=c.shift();s?Ne(n,s,()=>y(F)):y(F)}p()}catch(F){n.error(F)}}))};return e.subscribe(L(n,h,()=>{f=!0,p()})),()=>{a?.()}}function he(e,n,t=1/0){return k(n)?he((r,i)=>B((o,s)=>n(r,o,i,s))(ie(e(r,i))),t):(typeof n=="number"&&(t=n),$((r,i)=>hg(r,i,e,t)))}function br(e=1/0){return he(xe,e)}function pg(){return br(1)}function wr(...e){return pg()(ce(e,an(e)))}function Yo(e){return new W(n=>{ie(e()).subscribe(n)})}function qc(...e){let n=Fo(e),{args:t,keys:r}=Zo(e),i=new W(o=>{let{length:s}=t;if(!s){o.complete();return}let a=new Array(s),c=s,l=s;for(let d=0;d<s;d++){let f=!1;ie(t[d]).subscribe(L(o,p=>{f||(f=!0,l--),a[d]=p},()=>c--,void 0,()=>{(!c||!f)&&(l||o.next(r?Qo(r,a):a),o.complete())}))}});return n?i.pipe(Cr(n)):i}var vC=["addListener","removeListener"],yC=["addEventListener","removeEventListener"],DC=["on","off"];function Un(e,n,t,r){if(k(t)&&(r=t,t=void 0),r)return Un(e,n,t).pipe(Cr(r));let[i,o]=bC(e)?yC.map(s=>a=>e[s](n,a,t)):IC(e)?vC.map(gg(e,n)):CC(e)?DC.map(gg(e,n)):[];if(!i&&Dr(e))return he(s=>Un(s,n,t))(ie(e));if(!i)throw new TypeError("Invalid event target");return new W(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return i(a),()=>o(a)})}function gg(e,n){return t=>r=>e[t](n,r)}function IC(e){return k(e.addListener)&&k(e.removeListener)}function CC(e){return k(e.on)&&k(e.off)}function bC(e){return k(e.addEventListener)&&k(e.removeEventListener)}function Ae(e,n){return $((t,r)=>{let i=0;t.subscribe(L(r,o=>e.call(n,o,i++)&&r.next(o)))})}function kt(e){return $((n,t)=>{let r=null,i=!1,o;r=n.subscribe(L(t,void 0,void 0,s=>{o=ie(e(s,kt(e)(n))),r?(r.unsubscribe(),r=null,o.subscribe(t)):i=!0})),i&&(r.unsubscribe(),r=null,o.subscribe(t))})}function mg(e,n,t,r,i){return(o,s)=>{let a=t,c=n,l=0;o.subscribe(L(s,d=>{let f=l++;c=a?e(c,d,f):(a=!0,d),r&&s.next(c)},i&&(()=>{a&&s.next(c),s.complete()})))}}function Pt(e,n){return k(n)?he(e,n,1):he(e,1)}function cn(e){return $((n,t)=>{let r=!1;n.subscribe(L(t,i=>{r=!0,t.next(i)},()=>{r||t.next(e),t.complete()}))})}function Ft(e){return e<=0?()=>ze:$((n,t)=>{let r=0;n.subscribe(L(t,i=>{++r<=e&&(t.next(i),e<=r&&t.complete())}))})}function Zc(e){return B(()=>e)}function Qc(e,n=xe){return e=e??wC,$((t,r)=>{let i,o=!0;t.subscribe(L(r,s=>{let a=n(s);(o||!e(i,a))&&(o=!1,i=a,r.next(s))}))})}function wC(e,n){return e===n}function Ko(e=EC){return $((n,t)=>{let r=!1;n.subscribe(L(t,i=>{r=!0,t.next(i)},()=>r?t.complete():t.error(e())))})}function EC(){return new ct}function ln(e){return $((n,t)=>{try{n.subscribe(t)}finally{t.add(e)}})}function Ct(e,n){let t=arguments.length>=2;return r=>r.pipe(e?Ae((i,o)=>e(i,o,r)):xe,Ft(1),t?cn(n):Ko(()=>new ct))}function Er(e){return e<=0?()=>ze:$((n,t)=>{let r=[];n.subscribe(L(t,i=>{r.push(i),e<r.length&&r.shift()},()=>{for(let i of r)t.next(i);t.complete()},void 0,()=>{r=null}))})}function Yc(e,n){let t=arguments.length>=2;return r=>r.pipe(e?Ae((i,o)=>e(i,o,r)):xe,Er(1),t?cn(n):Ko(()=>new ct))}function MC(e=1/0){let n;e&&typeof e=="object"?n=e:n={count:e};let{count:t=1/0,delay:r,resetOnSuccess:i=!1}=n;return t<=0?xe:$((o,s)=>{let a=0,c,l=()=>{let d=!1;c=o.subscribe(L(s,f=>{i&&(a=0),s.next(f)},void 0,f=>{if(a++<t){let p=()=>{c?(c.unsubscribe(),c=null,l()):d=!0};if(r!=null){let h=typeof r=="number"?ko(r):ie(r(f,a)),y=L(s,()=>{y.unsubscribe(),p()},()=>{s.complete()});h.subscribe(y)}else p()}else s.error(f)})),d&&(c.unsubscribe(),c=null,l())};l()})}function Kc(e,n){return $(mg(e,n,arguments.length>=2,!0))}function Xc(...e){let n=an(e);return $((t,r)=>{(n?wr(e,t,n):wr(e,t)).subscribe(r)})}function we(e,n){return $((t,r)=>{let i=null,o=0,s=!1,a=()=>s&&!i&&r.complete();t.subscribe(L(r,c=>{i?.unsubscribe();let l=0,d=o++;ie(e(c,d)).subscribe(i=L(r,f=>r.next(n?n(c,f,d,l++):f),()=>{i=null,a()}))},()=>{s=!0,a()}))})}function Jc(e){return $((n,t)=>{ie(e).subscribe(L(t,()=>t.complete(),hi)),!t.closed&&n.subscribe(t)})}function Ee(e,n,t){let r=k(e)||n||t?{next:e,error:n,complete:t}:e;return r?$((i,o)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;i.subscribe(L(o,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),o.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),o.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),o.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):xe}function vg(e,n){return Object.is(e,n)}var be=null,Xo=!1,Jo=1,jt=Symbol("SIGNAL");function Q(e){let n=be;return be=e,n}function yg(){return be}var vi={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function nl(e){if(Xo)throw new Error("");if(be===null)return;be.consumerOnSignalRead(e);let n=be.nextProducerIndex++;if(rs(be),n<be.producerNode.length&&be.producerNode[n]!==e&&mi(be)){let t=be.producerNode[n];ns(t,be.producerIndexOfThis[n])}be.producerNode[n]!==e&&(be.producerNode[n]=e,be.producerIndexOfThis[n]=mi(be)?bg(e,be,n):0),be.producerLastReadVersion[n]=e.version}function _C(){Jo++}function Dg(e){if(!(mi(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Jo)){if(!e.producerMustRecompute(e)&&!il(e)){e.dirty=!1,e.lastCleanEpoch=Jo;return}e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=Jo}}function Ig(e){if(e.liveConsumerNode===void 0)return;let n=Xo;Xo=!0;try{for(let t of e.liveConsumerNode)t.dirty||SC(t)}finally{Xo=n}}function Cg(){return be?.consumerAllowSignalWrites!==!1}function SC(e){e.dirty=!0,Ig(e),e.consumerMarkedDirty?.(e)}function ts(e){return e&&(e.nextProducerIndex=0),Q(e)}function rl(e,n){if(Q(n),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(mi(e))for(let t=e.nextProducerIndex;t<e.producerNode.length;t++)ns(e.producerNode[t],e.producerIndexOfThis[t]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function il(e){rs(e);for(let n=0;n<e.producerNode.length;n++){let t=e.producerNode[n],r=e.producerLastReadVersion[n];if(r!==t.version||(Dg(t),r!==t.version))return!0}return!1}function ol(e){if(rs(e),mi(e))for(let n=0;n<e.producerNode.length;n++)ns(e.producerNode[n],e.producerIndexOfThis[n]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function bg(e,n,t){if(wg(e),e.liveConsumerNode.length===0&&Eg(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=bg(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(t),e.liveConsumerNode.push(n)-1}function ns(e,n){if(wg(e),e.liveConsumerNode.length===1&&Eg(e))for(let r=0;r<e.producerNode.length;r++)ns(e.producerNode[r],e.producerIndexOfThis[r]);let t=e.liveConsumerNode.length-1;if(e.liveConsumerNode[n]=e.liveConsumerNode[t],e.liveConsumerIndexOfThis[n]=e.liveConsumerIndexOfThis[t],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,n<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[n],i=e.liveConsumerNode[n];rs(i),i.producerIndexOfThis[r]=n}}function mi(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function rs(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function wg(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Eg(e){return e.producerNode!==void 0}function Mg(e){let n=Object.create(TC);n.computation=e;let t=()=>{if(Dg(n),nl(n),n.value===es)throw n.error;return n.value};return t[jt]=n,t}var el=Symbol("UNSET"),tl=Symbol("COMPUTING"),es=Symbol("ERRORED"),TC=V(b({},vi),{value:el,dirty:!0,error:null,equal:vg,producerMustRecompute(e){return e.value===el||e.value===tl},producerRecomputeValue(e){if(e.value===tl)throw new Error("Detected cycle in computations.");let n=e.value;e.value=tl;let t=ts(e),r;try{r=e.computation()}catch(i){r=es,e.error=i}finally{rl(e,t)}if(n!==el&&n!==es&&r!==es&&e.equal(n,r)){e.value=n;return}e.value=r,e.version++}});function xC(){throw new Error}var _g=xC;function Sg(){_g()}function Tg(e){_g=e}var AC=null;function xg(e){let n=Object.create(Rg);n.value=e;let t=()=>(nl(n),n.value);return t[jt]=n,t}function sl(e,n){Cg()||Sg(),e.equal(e.value,n)||(e.value=n,RC(e))}function Ag(e,n){Cg()||Sg(),sl(e,n(e.value))}var Rg=V(b({},vi),{equal:vg,value:void 0});function RC(e){e.version++,_C(),Ig(e),AC?.()}var ym="https://g.co/ng/security#xss",R=class extends Error{constructor(n,t){super(Us(n,t)),this.code=n}};function Us(e,n){return`${`NG0${Math.abs(e)}`}${n?": "+n:""}`}function Si(e){return{toString:e}.toString()}var is="__parameters__";function NC(e){return function(...t){if(e){let r=e(...t);for(let i in r)this[i]=r[i]}}}function Dm(e,n,t){return Si(()=>{let r=NC(n);function i(...o){if(this instanceof i)return r.apply(this,o),this;let s=new i(...o);return a.annotation=s,a;function a(c,l,d){let f=c.hasOwnProperty(is)?c[is]:Object.defineProperty(c,is,{value:[]})[is];for(;f.length<=d;)f.push(null);return(f[d]=f[d]||[]).push(s),c}}return t&&(i.prototype=Object.create(t.prototype)),i.prototype.ngMetadataName=e,i.annotationCls=i,i})}var Lt=globalThis;function te(e){for(let n in e)if(e[n]===te)return n;throw Error("Could not find renamed property on target object.")}function OC(e,n){for(let t in n)n.hasOwnProperty(t)&&!e.hasOwnProperty(t)&&(e[t]=n[t])}function Fe(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(Fe).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let n=e.toString();if(n==null)return""+n;let t=n.indexOf(`
`);return t===-1?n:n.substring(0,t)}function Cl(e,n){return e==null||e===""?n===null?"":n:n==null||n===""?e:e+" "+n}var kC=te({__forward_ref__:te});function et(e){return e.__forward_ref__=et,e.toString=function(){return Fe(this())},e}function Pe(e){return Im(e)?e():e}function Im(e){return typeof e=="function"&&e.hasOwnProperty(kC)&&e.__forward_ref__===et}function x(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function pt(e){return{providers:e.providers||[],imports:e.imports||[]}}function $s(e){return Ng(e,bm)||Ng(e,wm)}function Cm(e){return $s(e)!==null}function Ng(e,n){return e.hasOwnProperty(n)?e[n]:null}function PC(e){let n=e&&(e[bm]||e[wm]);return n||null}function Og(e){return e&&(e.hasOwnProperty(kg)||e.hasOwnProperty(FC))?e[kg]:null}var bm=te({\u0275prov:te}),kg=te({\u0275inj:te}),wm=te({ngInjectableDef:te}),FC=te({ngInjectorDef:te}),A=class{constructor(n,t){this._desc=n,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof t=="number"?this.__NG_ELEMENT_ID__=t:t!==void 0&&(this.\u0275prov=x({token:this,providedIn:t.providedIn||"root",factory:t.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Em(e){return e&&!!e.\u0275providers}var jC=te({\u0275cmp:te}),LC=te({\u0275dir:te}),VC=te({\u0275pipe:te}),BC=te({\u0275mod:te}),vs=te({\u0275fac:te}),Di=te({__NG_ELEMENT_ID__:te}),Pg=te({__NG_ENV_ID__:te});function zn(e){return typeof e=="string"?e:e==null?"":String(e)}function UC(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():zn(e)}function $C(e,n){let t=n?`. Dependency path: ${n.join(" > ")} > ${e}`:"";throw new R(-200,e)}function wu(e,n){throw new R(-201,!1)}var z=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(z||{}),bl;function Mm(){return bl}function Ve(e){let n=bl;return bl=e,n}function _m(e,n,t){let r=$s(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(t&z.Optional)return null;if(n!==void 0)return n;wu(e,"Injector")}var HC={},Ii=HC,wl="__NG_DI_FLAG__",ys="ngTempTokenPath",zC="ngTokenPath",GC=/\n/gm,WC="\u0275",Fg="__source",xr;function qC(){return xr}function un(e){let n=xr;return xr=e,n}function ZC(e,n=z.Default){if(xr===void 0)throw new R(-203,!1);return xr===null?_m(e,void 0,n):xr.get(e,n&z.Optional?null:void 0,n)}function N(e,n=z.Default){return(Mm()||ZC)(Pe(e),n)}function v(e,n=z.Default){return N(e,Hs(n))}function Hs(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function El(e){let n=[];for(let t=0;t<e.length;t++){let r=Pe(e[t]);if(Array.isArray(r)){if(r.length===0)throw new R(900,!1);let i,o=z.Default;for(let s=0;s<r.length;s++){let a=r[s],c=QC(a);typeof c=="number"?c===-1?i=a.token:o|=c:i=a}n.push(N(i,o))}else n.push(N(r))}return n}function Sm(e,n){return e[wl]=n,e.prototype[wl]=n,e}function QC(e){return e[wl]}function YC(e,n,t,r){let i=e[ys];throw n[Fg]&&i.unshift(n[Fg]),e.message=KC(`
`+e.message,i,t,r),e[zC]=i,e[ys]=null,e}function KC(e,n,t,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==WC?e.slice(2):e;let i=Fe(n);if(Array.isArray(n))i=n.map(Fe).join(" -> ");else if(typeof n=="object"){let o=[];for(let s in n)if(n.hasOwnProperty(s)){let a=n[s];o.push(s+":"+(typeof a=="string"?JSON.stringify(a):Fe(a)))}i=`{${o.join(", ")}}`}return`${t}${r?"("+r+")":""}[${i}]: ${e.replace(GC,`
  `)}`}var zs=Sm(Dm("Optional"),8);var Eu=Sm(Dm("SkipSelf"),4);function Gn(e,n){let t=e.hasOwnProperty(vs);return t?e[vs]:null}function XC(e,n,t){if(e.length!==n.length)return!1;for(let r=0;r<e.length;r++){let i=e[r],o=n[r];if(t&&(i=t(i),o=t(o)),o!==i)return!1}return!0}function JC(e){return e.flat(Number.POSITIVE_INFINITY)}function Mu(e,n){e.forEach(t=>Array.isArray(t)?Mu(t,n):n(t))}function Tm(e,n,t){n>=e.length?e.push(t):e.splice(n,0,t)}function Ds(e,n){return n>=e.length-1?e.pop():e.splice(n,1)[0]}function eb(e,n){let t=[];for(let r=0;r<e;r++)t.push(n);return t}function tb(e,n,t,r){let i=e.length;if(i==n)e.push(t,r);else if(i===1)e.push(r,e[0]),e[0]=t;else{for(i--,e.push(e[i-1],e[i]);i>n;){let o=i-2;e[i]=e[o],i--}e[n]=t,e[n+1]=r}}function _u(e,n,t){let r=Ti(e,n);return r>=0?e[r|1]=t:(r=~r,tb(e,r,n,t)),r}function al(e,n){let t=Ti(e,n);if(t>=0)return e[t|1]}function Ti(e,n){return nb(e,n,1)}function nb(e,n,t){let r=0,i=e.length>>t;for(;i!==r;){let o=r+(i-r>>1),s=e[o<<t];if(n===s)return o<<t;s>n?i=o:r=o+1}return~(i<<t)}var Rr={},Be=[],Nr=new A(""),xm=new A("",-1),Am=new A(""),Is=class{get(n,t=Ii){if(t===Ii){let r=new Error(`NullInjectorError: No provider for ${Fe(n)}!`);throw r.name="NullInjectorError",r}return t}},Rm=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Rm||{}),Et=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Et||{}),hn=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(hn||{});function rb(e,n,t){let r=e.length;for(;;){let i=e.indexOf(n,t);if(i===-1)return i;if(i===0||e.charCodeAt(i-1)<=32){let o=n.length;if(i+o===r||e.charCodeAt(i+o)<=32)return i}t=i+1}}function Ml(e,n,t){let r=0;for(;r<t.length;){let i=t[r];if(typeof i=="number"){if(i!==0)break;r++;let o=t[r++],s=t[r++],a=t[r++];e.setAttribute(n,s,a,o)}else{let o=i,s=t[++r];ib(o)?e.setProperty(n,o,s):e.setAttribute(n,o,s),r++}}return r}function Nm(e){return e===3||e===4||e===6}function ib(e){return e.charCodeAt(0)===64}function Ci(e,n){if(!(n===null||n.length===0))if(e===null||e.length===0)e=n.slice();else{let t=-1;for(let r=0;r<n.length;r++){let i=n[r];typeof i=="number"?t=i:t===0||(t===-1||t===2?jg(e,t,i,null,n[++r]):jg(e,t,i,null,null))}}return e}function jg(e,n,t,r,i){let o=0,s=e.length;if(n===-1)s=-1;else for(;o<e.length;){let a=e[o++];if(typeof a=="number"){if(a===n){s=-1;break}else if(a>n){s=o-1;break}}}for(;o<e.length;){let a=e[o];if(typeof a=="number")break;if(a===t){if(r===null){i!==null&&(e[o+1]=i);return}else if(r===e[o+1]){e[o+2]=i;return}}o++,r!==null&&o++,i!==null&&o++}s!==-1&&(e.splice(s,0,n),o=s+1),e.splice(o++,0,t),r!==null&&e.splice(o++,0,r),i!==null&&e.splice(o++,0,i)}var Om="ng-template";function ob(e,n,t,r){let i=0;if(r){for(;i<n.length&&typeof n[i]=="string";i+=2)if(n[i]==="class"&&rb(n[i+1].toLowerCase(),t,0)!==-1)return!0}else if(Su(e))return!1;if(i=n.indexOf(1,i),i>-1){let o;for(;++i<n.length&&typeof(o=n[i])=="string";)if(o.toLowerCase()===t)return!0}return!1}function Su(e){return e.type===4&&e.value!==Om}function sb(e,n,t){let r=e.type===4&&!t?Om:e.value;return n===r}function ab(e,n,t){let r=4,i=e.attrs,o=i!==null?ub(i):0,s=!1;for(let a=0;a<n.length;a++){let c=n[a];if(typeof c=="number"){if(!s&&!ut(r)&&!ut(c))return!1;if(s&&ut(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!sb(e,c,t)||c===""&&n.length===1){if(ut(r))return!1;s=!0}}else if(r&8){if(i===null||!ob(e,i,c,t)){if(ut(r))return!1;s=!0}}else{let l=n[++a],d=cb(c,i,Su(e),t);if(d===-1){if(ut(r))return!1;s=!0;continue}if(l!==""){let f;if(d>o?f="":f=i[d+1].toLowerCase(),r&2&&l!==f){if(ut(r))return!1;s=!0}}}}return ut(r)||s}function ut(e){return(e&1)===0}function cb(e,n,t,r){if(n===null)return-1;let i=0;if(r||!t){let o=!1;for(;i<n.length;){let s=n[i];if(s===e)return i;if(s===3||s===6)o=!0;else if(s===1||s===2){let a=n[++i];for(;typeof a=="string";)a=n[++i];continue}else{if(s===4)break;if(s===0){i+=4;continue}}i+=o?1:2}return-1}else return db(n,e)}function km(e,n,t=!1){for(let r=0;r<n.length;r++)if(ab(e,n[r],t))return!0;return!1}function lb(e){let n=e.attrs;if(n!=null){let t=n.indexOf(5);if(!(t&1))return n[t+1]}return null}function ub(e){for(let n=0;n<e.length;n++){let t=e[n];if(Nm(t))return n}return e.length}function db(e,n){let t=e.indexOf(4);if(t>-1)for(t++;t<e.length;){let r=e[t];if(typeof r=="number")return-1;if(r===n)return t;t++}return-1}function fb(e,n){e:for(let t=0;t<n.length;t++){let r=n[t];if(e.length===r.length){for(let i=0;i<e.length;i++)if(e[i]!==r[i])continue e;return!0}}return!1}function Lg(e,n){return e?":not("+n.trim()+")":n}function hb(e){let n=e[0],t=1,r=2,i="",o=!1;for(;t<e.length;){let s=e[t];if(typeof s=="string")if(r&2){let a=e[++t];i+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?i+="."+s:r&4&&(i+=" "+s);else i!==""&&!ut(s)&&(n+=Lg(o,i),i=""),r=s,o=o||!ut(r);t++}return i!==""&&(n+=Lg(o,i)),n}function pb(e){return e.map(hb).join(",")}function gb(e){let n=[],t=[],r=1,i=2;for(;r<e.length;){let o=e[r];if(typeof o=="string")i===2?o!==""&&n.push(o,e[++r]):i===8&&t.push(o);else{if(!ut(i))break;i=o}r++}return{attrs:n,classes:t}}function I(e){return Si(()=>{let n=Bm(e),t=V(b({},n),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Rm.OnPush,directiveDefs:null,pipeDefs:null,dependencies:n.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Et.Emulated,styles:e.styles||Be,_:null,schemas:e.schemas||null,tView:null,id:""});Um(t);let r=e.dependencies;return t.directiveDefs=Bg(r,!1),t.pipeDefs=Bg(r,!0),t.id=yb(t),t})}function mb(e){return Vt(e)||Fm(e)}function vb(e){return e!==null}function gt(e){return Si(()=>({type:e.type,bootstrap:e.bootstrap||Be,declarations:e.declarations||Be,imports:e.imports||Be,exports:e.exports||Be,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Vg(e,n){if(e==null)return Rr;let t={};for(let r in e)if(e.hasOwnProperty(r)){let i=e[r],o,s,a=hn.None;Array.isArray(i)?(a=i[0],o=i[1],s=i[2]??o):(o=i,s=i),n?(t[o]=a!==hn.None?[r,a]:r,n[o]=s):t[o]=r}return t}function H(e){return Si(()=>{let n=Bm(e);return Um(n),n})}function Pm(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone===!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Vt(e){return e[jC]||null}function Fm(e){return e[LC]||null}function jm(e){return e[VC]||null}function Lm(e){let n=Vt(e)||Fm(e)||jm(e);return n!==null?n.standalone:!1}function Vm(e,n){let t=e[BC]||null;if(!t&&n===!0)throw new Error(`Type ${Fe(e)} does not have '\u0275mod' property.`);return t}function Bm(e){let n={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:n,inputTransforms:null,inputConfig:e.inputs||Rr,exportAs:e.exportAs||null,standalone:e.standalone===!0,signals:e.signals===!0,selectors:e.selectors||Be,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Vg(e.inputs,n),outputs:Vg(e.outputs),debugInfo:null}}function Um(e){e.features?.forEach(n=>n(e))}function Bg(e,n){if(!e)return null;let t=n?jm:mb;return()=>(typeof e=="function"?e():e).map(r=>t(r)).filter(vb)}function yb(e){let n=0,t=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(let i of t)n=Math.imul(31,n)+i.charCodeAt(0)<<0;return n+=**********,"c"+n}function xi(e){return{\u0275providers:e}}function Db(...e){return{\u0275providers:$m(!0,e),\u0275fromNgModule:!0}}function $m(e,...n){let t=[],r=new Set,i,o=s=>{t.push(s)};return Mu(n,s=>{let a=s;_l(a,o,[],r)&&(i||=[],i.push(a))}),i!==void 0&&Hm(i,o),t}function Hm(e,n){for(let t=0;t<e.length;t++){let{ngModule:r,providers:i}=e[t];Tu(i,o=>{n(o,r)})}}function _l(e,n,t,r){if(e=Pe(e),!e)return!1;let i=null,o=Og(e),s=!o&&Vt(e);if(!o&&!s){let c=e.ngModule;if(o=Og(c),o)i=c;else return!1}else{if(s&&!s.standalone)return!1;i=e}let a=r.has(i);if(s){if(a)return!1;if(r.add(i),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)_l(l,n,t,r)}}else if(o){if(o.imports!=null&&!a){r.add(i);let l;try{Mu(o.imports,d=>{_l(d,n,t,r)&&(l||=[],l.push(d))})}finally{}l!==void 0&&Hm(l,n)}if(!a){let l=Gn(i)||(()=>new i);n({provide:i,useFactory:l,deps:Be},i),n({provide:Am,useValue:i,multi:!0},i),n({provide:Nr,useValue:()=>N(i),multi:!0},i)}let c=o.providers;if(c!=null&&!a){let l=e;Tu(c,d=>{n(d,l)})}}else return!1;return i!==e&&e.providers!==void 0}function Tu(e,n){for(let t of e)Em(t)&&(t=t.\u0275providers),Array.isArray(t)?Tu(t,n):n(t)}var Ib=te({provide:String,useValue:te});function zm(e){return e!==null&&typeof e=="object"&&Ib in e}function Cb(e){return!!(e&&e.useExisting)}function bb(e){return!!(e&&e.useFactory)}function Or(e){return typeof e=="function"}function wb(e){return!!e.useClass}var Gs=new A(""),ds={},Eb={},cl;function Ws(){return cl===void 0&&(cl=new Is),cl}var ue=class{},bi=class extends ue{get destroyed(){return this._destroyed}constructor(n,t,r,i){super(),this.parent=t,this.source=r,this.scopes=i,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,Tl(n,s=>this.processProvider(s)),this.records.set(xm,Mr(void 0,this)),i.has("environment")&&this.records.set(ue,Mr(void 0,this));let o=this.records.get(Gs);o!=null&&typeof o.value=="string"&&this.scopes.add(o.value),this.injectorDefTypes=new Set(this.get(Am,Be,z.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let n=Q(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let t=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of t)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),Q(n)}}onDestroy(n){return this.assertNotDestroyed(),this._onDestroyHooks.push(n),()=>this.removeOnDestroy(n)}runInContext(n){this.assertNotDestroyed();let t=un(this),r=Ve(void 0),i;try{return n()}finally{un(t),Ve(r)}}get(n,t=Ii,r=z.Default){if(this.assertNotDestroyed(),n.hasOwnProperty(Pg))return n[Pg](this);r=Hs(r);let i,o=un(this),s=Ve(void 0);try{if(!(r&z.SkipSelf)){let c=this.records.get(n);if(c===void 0){let l=xb(n)&&$s(n);l&&this.injectableDefInScope(l)?c=Mr(Sl(n),ds):c=null,this.records.set(n,c)}if(c!=null)return this.hydrate(n,c)}let a=r&z.Self?Ws():this.parent;return t=r&z.Optional&&t===Ii?null:t,a.get(n,t)}catch(a){if(a.name==="NullInjectorError"){if((a[ys]=a[ys]||[]).unshift(Fe(n)),o)throw a;return YC(a,n,"R3InjectorError",this.source)}else throw a}finally{Ve(s),un(o)}}resolveInjectorInitializers(){let n=Q(null),t=un(this),r=Ve(void 0),i;try{let o=this.get(Nr,Be,z.Self);for(let s of o)s()}finally{un(t),Ve(r),Q(n)}}toString(){let n=[],t=this.records;for(let r of t.keys())n.push(Fe(r));return`R3Injector[${n.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new R(205,!1)}processProvider(n){n=Pe(n);let t=Or(n)?n:Pe(n&&n.provide),r=_b(n);if(!Or(n)&&n.multi===!0){let i=this.records.get(t);i||(i=Mr(void 0,ds,!0),i.factory=()=>El(i.multi),this.records.set(t,i)),t=n,i.multi.push(n)}this.records.set(t,r)}hydrate(n,t){let r=Q(null);try{return t.value===ds&&(t.value=Eb,t.value=t.factory()),typeof t.value=="object"&&t.value&&Tb(t.value)&&this._ngOnDestroyHooks.add(t.value),t.value}finally{Q(r)}}injectableDefInScope(n){if(!n.providedIn)return!1;let t=Pe(n.providedIn);return typeof t=="string"?t==="any"||this.scopes.has(t):this.injectorDefTypes.has(t)}removeOnDestroy(n){let t=this._onDestroyHooks.indexOf(n);t!==-1&&this._onDestroyHooks.splice(t,1)}};function Sl(e){let n=$s(e),t=n!==null?n.factory:Gn(e);if(t!==null)return t;if(e instanceof A)throw new R(204,!1);if(e instanceof Function)return Mb(e);throw new R(204,!1)}function Mb(e){if(e.length>0)throw new R(204,!1);let t=PC(e);return t!==null?()=>t.factory(e):()=>new e}function _b(e){if(zm(e))return Mr(void 0,e.useValue);{let n=Gm(e);return Mr(n,ds)}}function Gm(e,n,t){let r;if(Or(e)){let i=Pe(e);return Gn(i)||Sl(i)}else if(zm(e))r=()=>Pe(e.useValue);else if(bb(e))r=()=>e.useFactory(...El(e.deps||[]));else if(Cb(e))r=()=>N(Pe(e.useExisting));else{let i=Pe(e&&(e.useClass||e.provide));if(Sb(e))r=()=>new i(...El(e.deps));else return Gn(i)||Sl(i)}return r}function Mr(e,n,t=!1){return{factory:e,value:n,multi:t?[]:void 0}}function Sb(e){return!!e.deps}function Tb(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function xb(e){return typeof e=="function"||typeof e=="object"&&e instanceof A}function Tl(e,n){for(let t of e)Array.isArray(t)?Tl(t,n):t&&Em(t)?Tl(t.\u0275providers,n):n(t)}function tt(e,n){e instanceof bi&&e.assertNotDestroyed();let t,r=un(e),i=Ve(void 0);try{return n()}finally{un(r),Ve(i)}}function Wm(){return Mm()!==void 0||qC()!=null}function Ab(e){if(!Wm())throw new R(-203,!1)}function Rb(e){return typeof e=="function"}var Ht=0,U=1,P=2,Oe=3,ft=4,Ue=5,wi=6,Cs=7,ht=8,kr=9,Mt=10,de=11,Ei=12,Ug=13,Ur=14,Je=15,Wn=16,_r=17,Bt=18,qs=19,qm=20,dn=21,ll=22,Xe=23,We=25,Zm=1;var qn=7,bs=8,Pr=9,Ge=10,ws=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(ws||{});function fn(e){return Array.isArray(e)&&typeof e[Zm]=="object"}function zt(e){return Array.isArray(e)&&e[Zm]===!0}function xu(e){return(e.flags&4)!==0}function Zs(e){return e.componentOffset>-1}function Qs(e){return(e.flags&1)===1}function pn(e){return!!e.template}function xl(e){return(e[P]&512)!==0}var Al=class{constructor(n,t,r){this.previousValue=n,this.currentValue=t,this.firstChange=r}isFirstChange(){return this.firstChange}};function Qm(e,n,t,r){n!==null?n.applyValueToInputSignal(n,r):e[t]=r}function nt(){return Ym}function Ym(e){return e.type.prototype.ngOnChanges&&(e.setInput=Ob),Nb}nt.ngInherit=!0;function Nb(){let e=Xm(this),n=e?.current;if(n){let t=e.previous;if(t===Rr)e.previous=n;else for(let r in n)t[r]=n[r];e.current=null,this.ngOnChanges(n)}}function Ob(e,n,t,r,i){let o=this.declaredInputs[r],s=Xm(e)||kb(e,{previous:Rr,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[o];a[o]=new Al(l&&l.currentValue,t,c===Rr),Qm(e,n,i,t)}var Km="__ngSimpleChanges__";function Xm(e){return e[Km]||null}function kb(e,n){return e[Km]=n}var $g=null;var bt=function(e,n,t){$g?.(e,n,t)},Pb="svg",Fb="math";function _t(e){for(;Array.isArray(e);)e=e[Ht];return e}function Jm(e,n){return _t(n[e])}function rt(e,n){return _t(n[e.index])}function ev(e,n){return e.data[n]}function jb(e,n){return e[n]}function yn(e,n){let t=n[e];return fn(t)?t:t[Ht]}function Lb(e){return(e[P]&4)===4}function Au(e){return(e[P]&128)===128}function Vb(e){return zt(e[Oe])}function Fr(e,n){return n==null?null:e[n]}function tv(e){e[_r]=0}function nv(e){e[P]&1024||(e[P]|=1024,Au(e)&&Ks(e))}function Bb(e,n){for(;e>0;)n=n[Ur],e--;return n}function Ys(e){return!!(e[P]&9216||e[Xe]?.dirty)}function Rl(e){e[Mt].changeDetectionScheduler?.notify(8),e[P]&64&&(e[P]|=1024),Ys(e)&&Ks(e)}function Ks(e){e[Mt].changeDetectionScheduler?.notify(0);let n=Zn(e);for(;n!==null&&!(n[P]&8192||(n[P]|=8192,!Au(n)));)n=Zn(n)}function rv(e,n){if((e[P]&256)===256)throw new R(911,!1);e[dn]===null&&(e[dn]=[]),e[dn].push(n)}function Ub(e,n){if(e[dn]===null)return;let t=e[dn].indexOf(n);t!==-1&&e[dn].splice(t,1)}function Zn(e){let n=e[Oe];return zt(n)?n[Oe]:n}var G={lFrame:hv(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var iv=!1;function $b(){return G.lFrame.elementDepthCount}function Hb(){G.lFrame.elementDepthCount++}function zb(){G.lFrame.elementDepthCount--}function ov(){return G.bindingsEnabled}function sv(){return G.skipHydrationRootTNode!==null}function Gb(e){return G.skipHydrationRootTNode===e}function Wb(){G.skipHydrationRootTNode=null}function q(){return G.lFrame.lView}function pe(){return G.lFrame.tView}function Ru(e){return G.lFrame.contextLView=e,e[ht]}function Nu(e){return G.lFrame.contextLView=null,e}function Re(){let e=av();for(;e!==null&&e.type===64;)e=e.parent;return e}function av(){return G.lFrame.currentTNode}function qb(){let e=G.lFrame,n=e.currentTNode;return e.isParent?n:n.parent}function tr(e,n){let t=G.lFrame;t.currentTNode=e,t.isParent=n}function Ou(){return G.lFrame.isParent}function ku(){G.lFrame.isParent=!1}function cv(){return iv}function Hg(e){iv=e}function lv(){let e=G.lFrame,n=e.bindingRootIndex;return n===-1&&(n=e.bindingRootIndex=e.tView.bindingStartIndex),n}function Zb(){return G.lFrame.bindingIndex}function Qb(e){return G.lFrame.bindingIndex=e}function Xs(){return G.lFrame.bindingIndex++}function Pu(e){let n=G.lFrame,t=n.bindingIndex;return n.bindingIndex=n.bindingIndex+e,t}function Yb(){return G.lFrame.inI18n}function Kb(e,n){let t=G.lFrame;t.bindingIndex=t.bindingRootIndex=e,Nl(n)}function Xb(){return G.lFrame.currentDirectiveIndex}function Nl(e){G.lFrame.currentDirectiveIndex=e}function Jb(e){let n=G.lFrame.currentDirectiveIndex;return n===-1?null:e[n]}function uv(){return G.lFrame.currentQueryIndex}function Fu(e){G.lFrame.currentQueryIndex=e}function ew(e){let n=e[U];return n.type===2?n.declTNode:n.type===1?e[Ue]:null}function dv(e,n,t){if(t&z.SkipSelf){let i=n,o=e;for(;i=i.parent,i===null&&!(t&z.Host);)if(i=ew(o),i===null||(o=o[Ur],i.type&10))break;if(i===null)return!1;n=i,e=o}let r=G.lFrame=fv();return r.currentTNode=n,r.lView=e,!0}function ju(e){let n=fv(),t=e[U];G.lFrame=n,n.currentTNode=t.firstChild,n.lView=e,n.tView=t,n.contextLView=e,n.bindingIndex=t.bindingStartIndex,n.inI18n=!1}function fv(){let e=G.lFrame,n=e===null?null:e.child;return n===null?hv(e):n}function hv(e){let n={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=n),n}function pv(){let e=G.lFrame;return G.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var gv=pv;function Lu(){let e=pv();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function tw(e){return(G.lFrame.contextLView=Bb(e,G.lFrame.contextLView))[ht]}function Dn(){return G.lFrame.selectedIndex}function Qn(e){G.lFrame.selectedIndex=e}function Js(){let e=G.lFrame;return ev(e.tView,e.selectedIndex)}function nw(){return G.lFrame.currentNamespace}var mv=!0;function ea(){return mv}function ta(e){mv=e}function rw(e,n,t){let{ngOnChanges:r,ngOnInit:i,ngDoCheck:o}=n.type.prototype;if(r){let s=Ym(n);(t.preOrderHooks??=[]).push(e,s),(t.preOrderCheckHooks??=[]).push(e,s)}i&&(t.preOrderHooks??=[]).push(0-e,i),o&&((t.preOrderHooks??=[]).push(e,o),(t.preOrderCheckHooks??=[]).push(e,o))}function na(e,n){for(let t=n.directiveStart,r=n.directiveEnd;t<r;t++){let o=e.data[t].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:d}=o;s&&(e.contentHooks??=[]).push(-t,s),a&&((e.contentHooks??=[]).push(t,a),(e.contentCheckHooks??=[]).push(t,a)),c&&(e.viewHooks??=[]).push(-t,c),l&&((e.viewHooks??=[]).push(t,l),(e.viewCheckHooks??=[]).push(t,l)),d!=null&&(e.destroyHooks??=[]).push(t,d)}}function fs(e,n,t){vv(e,n,3,t)}function hs(e,n,t,r){(e[P]&3)===t&&vv(e,n,t,r)}function ul(e,n){let t=e[P];(t&3)===n&&(t&=16383,t+=1,e[P]=t)}function vv(e,n,t,r){let i=r!==void 0?e[_r]&65535:0,o=r??-1,s=n.length-1,a=0;for(let c=i;c<s;c++)if(typeof n[c+1]=="number"){if(a=n[c],r!=null&&a>=r)break}else n[c]<0&&(e[_r]+=65536),(a<o||o==-1)&&(iw(e,t,n,c),e[_r]=(e[_r]&**********)+c+2),c++}function zg(e,n){bt(4,e,n);let t=Q(null);try{n.call(e)}finally{Q(t),bt(5,e,n)}}function iw(e,n,t,r){let i=t[r]<0,o=t[r+1],s=i?-t[r]:t[r],a=e[s];i?e[P]>>14<e[_r]>>16&&(e[P]&3)===n&&(e[P]+=16384,zg(a,o)):zg(a,o)}var Ar=-1,Yn=class{constructor(n,t,r){this.factory=n,this.resolving=!1,this.canSeeViewProviders=t,this.injectImpl=r}};function ow(e){return e instanceof Yn}function sw(e){return(e.flags&8)!==0}function aw(e){return(e.flags&16)!==0}var dl={},Ol=class{constructor(n,t){this.injector=n,this.parentInjector=t}get(n,t,r){r=Hs(r);let i=this.injector.get(n,dl,r);return i!==dl||t===dl?i:this.parentInjector.get(n,t,r)}};function yv(e){return e!==Ar}function Es(e){return e&32767}function cw(e){return e>>16}function Ms(e,n){let t=cw(e),r=n;for(;t>0;)r=r[Ur],t--;return r}var kl=!0;function _s(e){let n=kl;return kl=e,n}var lw=256,Dv=lw-1,Iv=5,uw=0,wt={};function dw(e,n,t){let r;typeof t=="string"?r=t.charCodeAt(0)||0:t.hasOwnProperty(Di)&&(r=t[Di]),r==null&&(r=t[Di]=uw++);let i=r&Dv,o=1<<i;n.data[e+(i>>Iv)]|=o}function Ss(e,n){let t=Cv(e,n);if(t!==-1)return t;let r=n[U];r.firstCreatePass&&(e.injectorIndex=n.length,fl(r.data,e),fl(n,null),fl(r.blueprint,null));let i=Vu(e,n),o=e.injectorIndex;if(yv(i)){let s=Es(i),a=Ms(i,n),c=a[U].data;for(let l=0;l<8;l++)n[o+l]=a[s+l]|c[s+l]}return n[o+8]=i,o}function fl(e,n){e.push(0,0,0,0,0,0,0,0,n)}function Cv(e,n){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||n[e.injectorIndex+8]===null?-1:e.injectorIndex}function Vu(e,n){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let t=0,r=null,i=n;for(;i!==null;){if(r=_v(i),r===null)return Ar;if(t++,i=i[Ur],r.injectorIndex!==-1)return r.injectorIndex|t<<16}return Ar}function Pl(e,n,t){dw(e,n,t)}function fw(e,n){if(n==="class")return e.classes;if(n==="style")return e.styles;let t=e.attrs;if(t){let r=t.length,i=0;for(;i<r;){let o=t[i];if(Nm(o))break;if(o===0)i=i+2;else if(typeof o=="number")for(i++;i<r&&typeof t[i]=="string";)i++;else{if(o===n)return t[i+1];i=i+2}}}return null}function bv(e,n,t){if(t&z.Optional||e!==void 0)return e;wu(n,"NodeInjector")}function wv(e,n,t,r){if(t&z.Optional&&r===void 0&&(r=null),!(t&(z.Self|z.Host))){let i=e[kr],o=Ve(void 0);try{return i?i.get(n,r,t&z.Optional):_m(n,r,t&z.Optional)}finally{Ve(o)}}return bv(r,n,t)}function Ev(e,n,t,r=z.Default,i){if(e!==null){if(n[P]&2048&&!(r&z.Self)){let s=mw(e,n,t,r,wt);if(s!==wt)return s}let o=Mv(e,n,t,r,wt);if(o!==wt)return o}return wv(n,t,r,i)}function Mv(e,n,t,r,i){let o=pw(t);if(typeof o=="function"){if(!dv(n,e,r))return r&z.Host?bv(i,t,r):wv(n,t,r,i);try{let s;if(s=o(r),s==null&&!(r&z.Optional))wu(t);else return s}finally{gv()}}else if(typeof o=="number"){let s=null,a=Cv(e,n),c=Ar,l=r&z.Host?n[Je][Ue]:null;for((a===-1||r&z.SkipSelf)&&(c=a===-1?Vu(e,n):n[a+8],c===Ar||!Wg(r,!1)?a=-1:(s=n[U],a=Es(c),n=Ms(c,n)));a!==-1;){let d=n[U];if(Gg(o,a,d.data)){let f=hw(a,n,t,s,r,l);if(f!==wt)return f}c=n[a+8],c!==Ar&&Wg(r,n[U].data[a+8]===l)&&Gg(o,a,n)?(s=d,a=Es(c),n=Ms(c,n)):a=-1}}return i}function hw(e,n,t,r,i,o){let s=n[U],a=s.data[e+8],c=r==null?Zs(a)&&kl:r!=s&&(a.type&3)!==0,l=i&z.Host&&o===a,d=ps(a,s,t,c,l);return d!==null?Kn(n,s,d,a):wt}function ps(e,n,t,r,i){let o=e.providerIndexes,s=n.data,a=o&1048575,c=e.directiveStart,l=e.directiveEnd,d=o>>20,f=r?a:a+d,p=i?a+d:l;for(let h=f;h<p;h++){let y=s[h];if(h<c&&t===y||h>=c&&y.type===t)return h}if(i){let h=s[c];if(h&&pn(h)&&h.type===t)return c}return null}function Kn(e,n,t,r){let i=e[t],o=n.data;if(ow(i)){let s=i;s.resolving&&$C(UC(o[t]));let a=_s(s.canSeeViewProviders);s.resolving=!0;let c,l=s.injectImpl?Ve(s.injectImpl):null,d=dv(e,r,z.Default);try{i=e[t]=s.factory(void 0,o,e,r),n.firstCreatePass&&t>=r.directiveStart&&rw(t,o[t],n)}finally{l!==null&&Ve(l),_s(a),s.resolving=!1,gv()}}return i}function pw(e){if(typeof e=="string")return e.charCodeAt(0)||0;let n=e.hasOwnProperty(Di)?e[Di]:void 0;return typeof n=="number"?n>=0?n&Dv:gw:n}function Gg(e,n,t){let r=1<<e;return!!(t[n+(e>>Iv)]&r)}function Wg(e,n){return!(e&z.Self)&&!(e&z.Host&&n)}var Hn=class{constructor(n,t){this._tNode=n,this._lView=t}get(n,t,r){return Ev(this._tNode,this._lView,n,Hs(r),t)}};function gw(){return new Hn(Re(),q())}function ke(e){return Si(()=>{let n=e.prototype.constructor,t=n[vs]||Fl(n),r=Object.prototype,i=Object.getPrototypeOf(e.prototype).constructor;for(;i&&i!==r;){let o=i[vs]||Fl(i);if(o&&o!==t)return o;i=Object.getPrototypeOf(i)}return o=>new o})}function Fl(e){return Im(e)?()=>{let n=Fl(Pe(e));return n&&n()}:Gn(e)}function mw(e,n,t,r,i){let o=e,s=n;for(;o!==null&&s!==null&&s[P]&2048&&!(s[P]&512);){let a=Mv(o,s,t,r|z.Self,wt);if(a!==wt)return a;let c=o.parent;if(!c){let l=s[qm];if(l){let d=l.get(t,wt,r);if(d!==wt)return d}c=_v(s),s=s[Ur]}o=c}return i}function _v(e){let n=e[U],t=n.type;return t===2?n.declTNode:t===1?e[Ue]:null}function Gt(e){return fw(Re(),e)}function qg(e,n=null,t=null,r){let i=Sv(e,n,t,r);return i.resolveInjectorInitializers(),i}function Sv(e,n=null,t=null,r,i=new Set){let o=[t||Be,Db(e)];return r=r||(typeof e=="object"?void 0:Fe(e)),new bi(o,n||Ws(),r||null,i)}var le=class e{static{this.THROW_IF_NOT_FOUND=Ii}static{this.NULL=new Is}static create(n,t){if(Array.isArray(n))return qg({name:""},t,n,"");{let r=n.name??"";return qg({name:r},n.parent,n.providers,r)}}static{this.\u0275prov=x({token:e,providedIn:"any",factory:()=>N(xm)})}static{this.__NG_ELEMENT_ID__=-1}};var vw=new A("");vw.__NG_ELEMENT_ID__=e=>{let n=Re();if(n===null)throw new R(204,!1);if(n.type&2)return n.value;if(e&z.Optional)return null;throw new R(204,!1)};var yw="ngOriginalError";function hl(e){return e[yw]}var Tv=!0,Bu=(()=>{class e{static{this.__NG_ELEMENT_ID__=Dw}static{this.__NG_ENV_ID__=t=>t}}return e})(),jl=class extends Bu{constructor(n){super(),this._lView=n}onDestroy(n){return rv(this._lView,n),()=>Ub(this._lView,n)}};function Dw(){return new jl(q())}var Wt=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new ye(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let t=this.taskId++;return this.pendingTasks.add(t),t}remove(t){this.pendingTasks.delete(t),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static{this.\u0275prov=x({token:e,providedIn:"root",factory:()=>new e})}}return e})();var Ll=class extends ae{constructor(n=!1){super(),this.destroyRef=void 0,this.pendingTasks=void 0,this.__isAsync=n,Wm()&&(this.destroyRef=v(Bu,{optional:!0})??void 0,this.pendingTasks=v(Wt,{optional:!0})??void 0)}emit(n){let t=Q(null);try{super.next(n)}finally{Q(t)}}subscribe(n,t,r){let i=n,o=t||(()=>null),s=r;if(n&&typeof n=="object"){let c=n;i=c.next?.bind(c),o=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(o=this.wrapInTimeout(o),i&&(i=this.wrapInTimeout(i)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:i,error:o,complete:s});return n instanceof fe&&n.add(a),a}wrapInTimeout(n){return t=>{let r=this.pendingTasks?.add();setTimeout(()=>{n(t),r!==void 0&&this.pendingTasks?.remove(r)})}}},ee=Ll;function Ts(...e){}function xv(e){let n,t;function r(){e=Ts;try{t!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(t),n!==void 0&&clearTimeout(n)}catch{}}return n=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(t=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Zg(e){return queueMicrotask(()=>e()),()=>{e=Ts}}var Uu="isAngularZone",xs=Uu+"_ID",Iw=0,g=class e{constructor(n){this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new ee(!1),this.onMicrotaskEmpty=new ee(!1),this.onStable=new ee(!1),this.onError=new ee(!1);let{enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:i=!1,scheduleInRootZone:o=Tv}=n;if(typeof Zone>"u")throw new R(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!i&&r,s.shouldCoalesceRunChangeDetection=i,s.callbackScheduled=!1,s.scheduleInRootZone=o,ww(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Uu)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new R(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new R(909,!1)}run(n,t,r){return this._inner.run(n,t,r)}runTask(n,t,r,i){let o=this._inner,s=o.scheduleEventTask("NgZoneEvent: "+i,n,Cw,Ts,Ts);try{return o.runTask(s,t,r)}finally{o.cancelTask(s)}}runGuarded(n,t,r){return this._inner.runGuarded(n,t,r)}runOutsideAngular(n){return this._outer.run(n)}},Cw={};function $u(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function bw(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function n(){xv(()=>{e.callbackScheduled=!1,Vl(e),e.isCheckStableRunning=!0,$u(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{n()}):e._outer.run(()=>{n()}),Vl(e)}function ww(e){let n=()=>{bw(e)},t=Iw++;e._inner=e._inner.fork({name:"angular",properties:{[Uu]:!0,[xs]:t,[xs+t]:!0},onInvokeTask:(r,i,o,s,a,c)=>{if(Ew(c))return r.invokeTask(o,s,a,c);try{return Qg(e),r.invokeTask(o,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&n(),Yg(e)}},onInvoke:(r,i,o,s,a,c,l)=>{try{return Qg(e),r.invoke(o,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Mw(c)&&n(),Yg(e)}},onHasTask:(r,i,o,s)=>{r.hasTask(o,s),i===o&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Vl(e),$u(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,i,o,s)=>(r.handleError(o,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Vl(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Qg(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Yg(e){e._nesting--,$u(e)}var Bl=class{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new ee,this.onMicrotaskEmpty=new ee,this.onStable=new ee,this.onError=new ee}run(n,t,r){return n.apply(t,r)}runGuarded(n,t,r){return n.apply(t,r)}runOutsideAngular(n){return n()}runTask(n,t,r,i){return n.apply(t,r)}};function Ew(e){return Av(e,"__ignore_ng_zone__")}function Mw(e){return Av(e,"__scheduler_tick__")}function Av(e,n){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[n]===!0}var Ut=class{constructor(){this._console=console}handleError(n){let t=this._findOriginalError(n);this._console.error("ERROR",n),t&&this._console.error("ORIGINAL ERROR",t)}_findOriginalError(n){let t=n&&hl(n);for(;t&&hl(t);)t=hl(t);return t||null}},_w=new A("",{providedIn:"root",factory:()=>{let e=v(g),n=v(Ut);return t=>e.runOutsideAngular(()=>n.handleError(t))}});function Sw(){return $r(Re(),q())}function $r(e,n){return new m(rt(e,n))}var m=(()=>{class e{constructor(t){this.nativeElement=t}static{this.__NG_ELEMENT_ID__=Sw}}return e})();function Tw(e){return e instanceof m?e.nativeElement:e}function xw(){return this._results[Symbol.iterator]()}var Ul=class e{get changes(){return this._changes??=new ee}constructor(n=!1){this._emitDistinctChangesOnly=n,this.dirty=!0,this._onDirty=void 0,this._results=[],this._changesDetected=!1,this._changes=void 0,this.length=0,this.first=void 0,this.last=void 0;let t=e.prototype;t[Symbol.iterator]||(t[Symbol.iterator]=xw)}get(n){return this._results[n]}map(n){return this._results.map(n)}filter(n){return this._results.filter(n)}find(n){return this._results.find(n)}reduce(n,t){return this._results.reduce(n,t)}forEach(n){this._results.forEach(n)}some(n){return this._results.some(n)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(n,t){this.dirty=!1;let r=JC(n);(this._changesDetected=!XC(this._results,r,t))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}onDirty(n){this._onDirty=n}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}};function Rv(e){return(e.flags&128)===128}var Nv=new Map,Aw=0;function Rw(){return Aw++}function Nw(e){Nv.set(e[qs],e)}function $l(e){Nv.delete(e[qs])}var Kg="__ngContext__";function gn(e,n){fn(n)?(e[Kg]=n[qs],Nw(n)):e[Kg]=n}function Ov(e){return Pv(e[Ei])}function kv(e){return Pv(e[ft])}function Pv(e){for(;e!==null&&!zt(e);)e=e[ft];return e}var Hl;function Fv(e){Hl=e}function jv(){if(Hl!==void 0)return Hl;if(typeof document<"u")return document;throw new R(210,!1)}var Hu=new A("",{providedIn:"root",factory:()=>Ow}),Ow="ng",zu=new A(""),Tt=new A("",{providedIn:"platform",factory:()=>"unknown"});var Gu=new A("",{providedIn:"root",factory:()=>jv().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var kw="h",Pw="b";var Fw=()=>null;function Wu(e,n,t=!1){return Fw(e,n,t)}var Lv=!1,jw=new A("",{providedIn:"root",factory:()=>Lv});var os;function Lw(){if(os===void 0&&(os=null,Lt.trustedTypes))try{os=Lt.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return os}function ra(e){return Lw()?.createHTML(e)||e}var ss;function Vv(){if(ss===void 0&&(ss=null,Lt.trustedTypes))try{ss=Lt.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ss}function Xg(e){return Vv()?.createHTML(e)||e}function Jg(e){return Vv()?.createScriptURL(e)||e}var As=class{constructor(n){this.changingThisBreaksApplicationSecurity=n}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${ym})`}};function nr(e){return e instanceof As?e.changingThisBreaksApplicationSecurity:e}function ia(e,n){let t=Vw(e);if(t!=null&&t!==n){if(t==="ResourceURL"&&n==="URL")return!0;throw new Error(`Required a safe ${n}, got a ${t} (see ${ym})`)}return t===n}function Vw(e){return e instanceof As&&e.getTypeName()||null}function Bw(e){let n=new Gl(e);return Uw()?new zl(n):n}var zl=class{constructor(n){this.inertDocumentHelper=n}getInertBodyElement(n){n="<body><remove></remove>"+n;try{let t=new window.DOMParser().parseFromString(ra(n),"text/html").body;return t===null?this.inertDocumentHelper.getInertBodyElement(n):(t.firstChild?.remove(),t)}catch{return null}}},Gl=class{constructor(n){this.defaultDoc=n,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(n){let t=this.inertDocument.createElement("template");return t.innerHTML=ra(n),t}};function Uw(){try{return!!new window.DOMParser().parseFromString(ra(""),"text/html")}catch{return!1}}var $w=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function qu(e){return e=String(e),e.match($w)?e:"unsafe:"+e}function qt(e){let n={};for(let t of e.split(","))n[t]=!0;return n}function Ai(...e){let n={};for(let t of e)for(let r in t)t.hasOwnProperty(r)&&(n[r]=!0);return n}var Bv=qt("area,br,col,hr,img,wbr"),Uv=qt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),$v=qt("rp,rt"),Hw=Ai($v,Uv),zw=Ai(Uv,qt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Gw=Ai($v,qt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),em=Ai(Bv,zw,Gw,Hw),Hv=qt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Ww=qt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),qw=qt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Zw=Ai(Hv,Ww,qw),Qw=qt("script,style,template"),Wl=class{constructor(){this.sanitizedSomething=!1,this.buf=[]}sanitizeChildren(n){let t=n.firstChild,r=!0,i=[];for(;t;){if(t.nodeType===Node.ELEMENT_NODE?r=this.startElement(t):t.nodeType===Node.TEXT_NODE?this.chars(t.nodeValue):this.sanitizedSomething=!0,r&&t.firstChild){i.push(t),t=Xw(t);continue}for(;t;){t.nodeType===Node.ELEMENT_NODE&&this.endElement(t);let o=Kw(t);if(o){t=o;break}t=i.pop()}}return this.buf.join("")}startElement(n){let t=tm(n).toLowerCase();if(!em.hasOwnProperty(t))return this.sanitizedSomething=!0,!Qw.hasOwnProperty(t);this.buf.push("<"),this.buf.push(t);let r=n.attributes;for(let i=0;i<r.length;i++){let o=r.item(i),s=o.name,a=s.toLowerCase();if(!Zw.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=o.value;Hv[a]&&(c=qu(c)),this.buf.push(" ",s,'="',nm(c),'"')}return this.buf.push(">"),!0}endElement(n){let t=tm(n).toLowerCase();em.hasOwnProperty(t)&&!Bv.hasOwnProperty(t)&&(this.buf.push("</"),this.buf.push(t),this.buf.push(">"))}chars(n){this.buf.push(nm(n))}};function Yw(e,n){return(e.compareDocumentPosition(n)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function Kw(e){let n=e.nextSibling;if(n&&e!==n.previousSibling)throw zv(n);return n}function Xw(e){let n=e.firstChild;if(n&&Yw(e,n))throw zv(n);return n}function tm(e){let n=e.nodeName;return typeof n=="string"?n:"FORM"}function zv(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var Jw=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,eE=/([^\#-~ |!])/g;function nm(e){return e.replace(/&/g,"&amp;").replace(Jw,function(n){let t=n.charCodeAt(0),r=n.charCodeAt(1);return"&#"+((t-55296)*1024+(r-56320)+65536)+";"}).replace(eE,function(n){return"&#"+n.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var as;function Gv(e,n){let t=null;try{as=as||Bw(e);let r=n?String(n):"";t=as.getInertBodyElement(r);let i=5,o=r;do{if(i===0)throw new Error("Failed to sanitize html because the input is unstable");i--,r=o,o=t.innerHTML,t=as.getInertBodyElement(r)}while(r!==o);let a=new Wl().sanitizeChildren(rm(t)||t);return ra(a)}finally{if(t){let r=rm(t)||t;for(;r.firstChild;)r.firstChild.remove()}}}function rm(e){return"content"in e&&tE(e)?e.content:null}function tE(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var Ri=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Ri||{});function BB(e){let n=Zu();return n?Xg(n.sanitize(Ri.HTML,e)||""):ia(e,"HTML")?Xg(nr(e)):Gv(jv(),zn(e))}function nE(e){let n=Zu();return n?n.sanitize(Ri.URL,e)||"":ia(e,"URL")?nr(e):qu(zn(e))}function rE(e){let n=Zu();if(n)return Jg(n.sanitize(Ri.RESOURCE_URL,e)||"");if(ia(e,"ResourceURL"))return Jg(nr(e));throw new R(904,!1)}function iE(e,n){return n==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||n==="href"&&(e==="base"||e==="link")?rE:nE}function Wv(e,n,t){return iE(n,t)(e)}function Zu(){let e=q();return e&&e[Mt].sanitizer}var oE=/^>|^->|<!--|-->|--!>|<!-$/g,sE=/(<|>)/g,aE="\u200B$1\u200B";function cE(e){return e.replace(oE,n=>n.replace(sE,aE))}function qv(e){return e instanceof Function?e():e}function lE(e){return(e??v(le)).get(Tt)==="browser"}var $t=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}($t||{}),uE;function Qu(e,n){return uE(e,n)}function Sr(e,n,t,r,i){if(r!=null){let o,s=!1;zt(r)?o=r:fn(r)&&(s=!0,r=r[Ht]);let a=_t(r);e===0&&t!==null?i==null?Jv(n,t,a):Rs(n,t,a,i||null,!0):e===1&&t!==null?Rs(n,t,a,i||null,!0):e===2?EE(n,a,s):e===3&&n.destroyNode(a),o!=null&&_E(n,e,o,t,i)}}function dE(e,n){return e.createText(n)}function fE(e,n,t){e.setValue(n,t)}function hE(e,n){return e.createComment(cE(n))}function Zv(e,n,t){return e.createElement(n,t)}function pE(e,n){Qv(e,n),n[Ht]=null,n[Ue]=null}function gE(e,n,t,r,i,o){r[Ht]=i,r[Ue]=n,sa(e,r,t,1,i,o)}function Qv(e,n){n[Mt].changeDetectionScheduler?.notify(9),sa(e,n,n[de],2,null,null)}function mE(e){let n=e[Ei];if(!n)return pl(e[U],e);for(;n;){let t=null;if(fn(n))t=n[Ei];else{let r=n[Ge];r&&(t=r)}if(!t){for(;n&&!n[ft]&&n!==e;)fn(n)&&pl(n[U],n),n=n[Oe];n===null&&(n=e),fn(n)&&pl(n[U],n),t=n&&n[ft]}n=t}}function vE(e,n,t,r){let i=Ge+r,o=t.length;r>0&&(t[i-1][ft]=n),r<o-Ge?(n[ft]=t[i],Tm(t,Ge+r,n)):(t.push(n),n[ft]=null),n[Oe]=t;let s=n[Wn];s!==null&&t!==s&&Yv(s,n);let a=n[Bt];a!==null&&a.insertView(e),Rl(n),n[P]|=128}function Yv(e,n){let t=e[Pr],r=n[Oe];if(fn(r))e[P]|=ws.HasTransplantedViews;else{let i=r[Oe][Je];n[Je]!==i&&(e[P]|=ws.HasTransplantedViews)}t===null?e[Pr]=[n]:t.push(n)}function Yu(e,n){let t=e[Pr],r=t.indexOf(n);t.splice(r,1)}function ql(e,n){if(e.length<=Ge)return;let t=Ge+n,r=e[t];if(r){let i=r[Wn];i!==null&&i!==e&&Yu(i,r),n>0&&(e[t-1][ft]=r[ft]);let o=Ds(e,Ge+n);pE(r[U],r);let s=o[Bt];s!==null&&s.detachView(o[U]),r[Oe]=null,r[ft]=null,r[P]&=-129}return r}function Kv(e,n){if(!(n[P]&256)){let t=n[de];t.destroyNode&&sa(e,n,t,3,null,null),mE(n)}}function pl(e,n){if(n[P]&256)return;let t=Q(null);try{n[P]&=-129,n[P]|=256,n[Xe]&&ol(n[Xe]),DE(e,n),yE(e,n),n[U].type===1&&n[de].destroy();let r=n[Wn];if(r!==null&&zt(n[Oe])){r!==n[Oe]&&Yu(r,n);let i=n[Bt];i!==null&&i.detachView(e)}$l(n)}finally{Q(t)}}function yE(e,n){let t=e.cleanup,r=n[Cs];if(t!==null)for(let o=0;o<t.length-1;o+=2)if(typeof t[o]=="string"){let s=t[o+3];s>=0?r[s]():r[-s].unsubscribe(),o+=2}else{let s=r[t[o+1]];t[o].call(s)}r!==null&&(n[Cs]=null);let i=n[dn];if(i!==null){n[dn]=null;for(let o=0;o<i.length;o++){let s=i[o];s()}}}function DE(e,n){let t;if(e!=null&&(t=e.destroyHooks)!=null)for(let r=0;r<t.length;r+=2){let i=n[t[r]];if(!(i instanceof Yn)){let o=t[r+1];if(Array.isArray(o))for(let s=0;s<o.length;s+=2){let a=i[o[s]],c=o[s+1];bt(4,a,c);try{c.call(a)}finally{bt(5,a,c)}}else{bt(4,i,o);try{o.call(i)}finally{bt(5,i,o)}}}}}function Xv(e,n,t){return IE(e,n.parent,t)}function IE(e,n,t){let r=n;for(;r!==null&&r.type&168;)n=r,r=n.parent;if(r===null)return t[Ht];{let{componentOffset:i}=r;if(i>-1){let{encapsulation:o}=e.data[r.directiveStart+i];if(o===Et.None||o===Et.Emulated)return null}return rt(r,t)}}function Rs(e,n,t,r,i){e.insertBefore(n,t,r,i)}function Jv(e,n,t){e.appendChild(n,t)}function im(e,n,t,r,i){r!==null?Rs(e,n,t,r,i):Jv(e,n,t)}function ey(e,n){return e.parentNode(n)}function CE(e,n){return e.nextSibling(n)}function ty(e,n,t){return wE(e,n,t)}function bE(e,n,t){return e.type&40?rt(e,t):null}var wE=bE,om;function oa(e,n,t,r){let i=Xv(e,r,n),o=n[de],s=r.parent||n[Ue],a=ty(s,r,n);if(i!=null)if(Array.isArray(t))for(let c=0;c<t.length;c++)im(o,i,t[c],a,!1);else im(o,i,t,a,!1);om!==void 0&&om(o,r,n,t,i)}function yi(e,n){if(n!==null){let t=n.type;if(t&3)return rt(n,e);if(t&4)return Zl(-1,e[n.index]);if(t&8){let r=n.child;if(r!==null)return yi(e,r);{let i=e[n.index];return zt(i)?Zl(-1,i):_t(i)}}else{if(t&128)return yi(e,n.next);if(t&32)return Qu(n,e)()||_t(e[n.index]);{let r=ny(e,n);if(r!==null){if(Array.isArray(r))return r[0];let i=Zn(e[Je]);return yi(i,r)}else return yi(e,n.next)}}}return null}function ny(e,n){if(n!==null){let r=e[Je][Ue],i=n.projection;return r.projection[i]}return null}function Zl(e,n){let t=Ge+e+1;if(t<n.length){let r=n[t],i=r[U].firstChild;if(i!==null)return yi(r,i)}return n[qn]}function EE(e,n,t){e.removeChild(null,n,t)}function Ku(e,n,t,r,i,o,s){for(;t!=null;){if(t.type===128){t=t.next;continue}let a=r[t.index],c=t.type;if(s&&n===0&&(a&&gn(_t(a),r),t.flags|=2),(t.flags&32)!==32)if(c&8)Ku(e,n,t.child,r,i,o,!1),Sr(n,e,i,a,o);else if(c&32){let l=Qu(t,r),d;for(;d=l();)Sr(n,e,i,d,o);Sr(n,e,i,a,o)}else c&16?ry(e,n,r,t,i,o):Sr(n,e,i,a,o);t=s?t.projectionNext:t.next}}function sa(e,n,t,r,i,o){Ku(t,r,e.firstChild,n,i,o,!1)}function ME(e,n,t){let r=n[de],i=Xv(e,t,n),o=t.parent||n[Ue],s=ty(o,t,n);ry(r,0,n,t,i,s)}function ry(e,n,t,r,i,o){let s=t[Je],c=s[Ue].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let d=c[l];Sr(n,e,i,d,o)}else{let l=c,d=s[Oe];Rv(r)&&(l.flags|=128),Ku(e,n,l,d,i,o,!0)}}function _E(e,n,t,r,i){let o=t[qn],s=_t(t);o!==s&&Sr(n,e,r,o,i);for(let a=Ge;a<t.length;a++){let c=t[a];sa(c[U],c,e,n,r,o)}}function SE(e,n,t,r,i){if(n)i?e.addClass(t,r):e.removeClass(t,r);else{let o=r.indexOf("-")===-1?void 0:$t.DashCase;i==null?e.removeStyle(t,r,o):(typeof i=="string"&&i.endsWith("!important")&&(i=i.slice(0,-10),o|=$t.Important),e.setStyle(t,r,i,o))}}function TE(e,n,t){e.setAttribute(n,"style",t)}function iy(e,n,t){t===""?e.removeAttribute(n,"class"):e.setAttribute(n,"class",t)}function oy(e,n,t){let{mergedAttrs:r,classes:i,styles:o}=t;r!==null&&Ml(e,n,r),i!==null&&iy(e,n,i),o!==null&&TE(e,n,o)}var it={};function aa(e=1){sy(pe(),q(),Dn()+e,!1)}function sy(e,n,t,r){if(!r)if((n[P]&3)===3){let o=e.preOrderCheckHooks;o!==null&&fs(n,o,t)}else{let o=e.preOrderHooks;o!==null&&hs(n,o,0,t)}Qn(t)}function u(e,n=z.Default){let t=q();if(t===null)return N(e,n);let r=Re();return Ev(r,t,Pe(e),n)}function ay(){let e="invalid";throw new Error(e)}function cy(e,n,t,r,i,o){let s=Q(null);try{let a=null;i&hn.SignalBased&&(a=n[r][jt]),a!==null&&a.transformFn!==void 0&&(o=a.transformFn(o)),i&hn.HasDecoratorInputTransform&&(o=e.inputTransforms[r].call(n,o)),e.setInput!==null?e.setInput(n,a,o,t,r):Qm(n,a,r,o)}finally{Q(s)}}function xE(e,n){let t=e.hostBindingOpCodes;if(t!==null)try{for(let r=0;r<t.length;r++){let i=t[r];if(i<0)Qn(~i);else{let o=i,s=t[++r],a=t[++r];Kb(s,o);let c=n[o];a(2,c)}}}finally{Qn(-1)}}function ca(e,n,t,r,i,o,s,a,c,l,d){let f=n.blueprint.slice();return f[Ht]=i,f[P]=r|4|128|8|64,(l!==null||e&&e[P]&2048)&&(f[P]|=2048),tv(f),f[Oe]=f[Ur]=e,f[ht]=t,f[Mt]=s||e&&e[Mt],f[de]=a||e&&e[de],f[kr]=c||e&&e[kr]||null,f[Ue]=o,f[qs]=Rw(),f[wi]=d,f[qm]=l,f[Je]=n.type==2?e[Je]:f,f}function Hr(e,n,t,r,i){let o=e.data[n];if(o===null)o=AE(e,n,t,r,i),Yb()&&(o.flags|=32);else if(o.type&64){o.type=t,o.value=r,o.attrs=i;let s=qb();o.injectorIndex=s===null?-1:s.injectorIndex}return tr(o,!0),o}function AE(e,n,t,r,i){let o=av(),s=Ou(),a=s?o:o&&o.parent,c=e.data[n]=FE(e,a,t,n,r,i);return e.firstChild===null&&(e.firstChild=c),o!==null&&(s?o.child==null&&c.parent!==null&&(o.child=c):o.next===null&&(o.next=c,c.prev=o)),c}function ly(e,n,t,r){if(t===0)return-1;let i=n.length;for(let o=0;o<t;o++)n.push(r),e.blueprint.push(r),e.data.push(null);return i}function uy(e,n,t,r,i){let o=Dn(),s=r&2;try{Qn(-1),s&&n.length>We&&sy(e,n,We,!1),bt(s?2:0,i),t(r,i)}finally{Qn(o),bt(s?3:1,i)}}function Xu(e,n,t){if(xu(n)){let r=Q(null);try{let i=n.directiveStart,o=n.directiveEnd;for(let s=i;s<o;s++){let a=e.data[s];if(a.contentQueries){let c=t[s];a.contentQueries(1,c,s)}}}finally{Q(r)}}}function Ju(e,n,t){ov()&&($E(e,n,t,rt(t,n)),(t.flags&64)===64&&hy(e,n,t))}function ed(e,n,t=rt){let r=n.localNames;if(r!==null){let i=n.index+1;for(let o=0;o<r.length;o+=2){let s=r[o+1],a=s===-1?t(n,e):e[s];e[i++]=a}}}function dy(e){let n=e.tView;return n===null||n.incompleteFirstPass?e.tView=td(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):n}function td(e,n,t,r,i,o,s,a,c,l,d){let f=We+r,p=f+i,h=RE(f,p),y=typeof l=="function"?l():l;return h[U]={type:e,blueprint:h,template:t,queries:null,viewQuery:a,declTNode:n,data:h.slice().fill(null,f),bindingStartIndex:f,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof o=="function"?o():o,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:y,incompleteFirstPass:!1,ssrId:d}}function RE(e,n){let t=[];for(let r=0;r<n;r++)t.push(r<e?null:it);return t}function NE(e,n,t,r){let o=r.get(jw,Lv)||t===Et.ShadowDom,s=e.selectRootElement(n,o);return OE(s),s}function OE(e){kE(e)}var kE=()=>null;function PE(e,n,t,r){let i=my(n);i.push(t),e.firstCreatePass&&vy(e).push(r,i.length-1)}function FE(e,n,t,r,i,o){let s=n?n.injectorIndex:-1,a=0;return sv()&&(a|=128),{type:t,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:i,attrs:o,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:n,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function sm(e,n,t,r,i){for(let o in n){if(!n.hasOwnProperty(o))continue;let s=n[o];if(s===void 0)continue;r??={};let a,c=hn.None;Array.isArray(s)?(a=s[0],c=s[1]):a=s;let l=o;if(i!==null){if(!i.hasOwnProperty(o))continue;l=i[o]}e===0?am(r,t,l,a,c):am(r,t,l,a)}return r}function am(e,n,t,r,i){let o;e.hasOwnProperty(t)?(o=e[t]).push(n,r):o=e[t]=[n,r],i!==void 0&&o.push(i)}function jE(e,n,t){let r=n.directiveStart,i=n.directiveEnd,o=e.data,s=n.attrs,a=[],c=null,l=null;for(let d=r;d<i;d++){let f=o[d],p=t?t.get(f):null,h=p?p.inputs:null,y=p?p.outputs:null;c=sm(0,f.inputs,d,c,h),l=sm(1,f.outputs,d,l,y);let M=c!==null&&s!==null&&!Su(n)?JE(c,d,s):null;a.push(M)}c!==null&&(c.hasOwnProperty("class")&&(n.flags|=8),c.hasOwnProperty("style")&&(n.flags|=16)),n.initialInputs=a,n.inputs=c,n.outputs=l}function LE(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function nd(e,n,t,r,i,o,s,a){let c=rt(n,t),l=n.inputs,d;!a&&l!=null&&(d=l[r])?(id(e,t,d,r,i),Zs(n)&&VE(t,n.index)):n.type&3?(r=LE(r),i=s!=null?s(i,n.value||"",r):i,o.setProperty(c,r,i)):n.type&12}function VE(e,n){let t=yn(n,e);t[P]&16||(t[P]|=64)}function rd(e,n,t,r){if(ov()){let i=r===null?null:{"":-1},o=zE(e,t),s,a;o===null?s=a=null:[s,a]=o,s!==null&&fy(e,n,t,s,i,a),i&&GE(t,r,i)}t.mergedAttrs=Ci(t.mergedAttrs,t.attrs)}function fy(e,n,t,r,i,o){for(let l=0;l<r.length;l++)Pl(Ss(t,n),e,r[l].type);qE(t,e.data.length,r.length);for(let l=0;l<r.length;l++){let d=r[l];d.providersResolver&&d.providersResolver(d)}let s=!1,a=!1,c=ly(e,n,r.length,null);for(let l=0;l<r.length;l++){let d=r[l];t.mergedAttrs=Ci(t.mergedAttrs,d.hostAttrs),ZE(e,t,n,c,d),WE(c,d,i),d.contentQueries!==null&&(t.flags|=4),(d.hostBindings!==null||d.hostAttrs!==null||d.hostVars!==0)&&(t.flags|=64);let f=d.type.prototype;!s&&(f.ngOnChanges||f.ngOnInit||f.ngDoCheck)&&((e.preOrderHooks??=[]).push(t.index),s=!0),!a&&(f.ngOnChanges||f.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(t.index),a=!0),c++}jE(e,t,o)}function BE(e,n,t,r,i){let o=i.hostBindings;if(o){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~n.index;UE(s)!=a&&s.push(a),s.push(t,r,o)}}function UE(e){let n=e.length;for(;n>0;){let t=e[--n];if(typeof t=="number"&&t<0)return t}return 0}function $E(e,n,t,r){let i=t.directiveStart,o=t.directiveEnd;Zs(t)&&QE(n,t,e.data[i+t.componentOffset]),e.firstCreatePass||Ss(t,n),gn(r,n);let s=t.initialInputs;for(let a=i;a<o;a++){let c=e.data[a],l=Kn(n,e,a,t);if(gn(l,n),s!==null&&XE(n,a-i,l,c,t,s),pn(c)){let d=yn(t.index,n);d[ht]=Kn(n,e,a,t)}}}function hy(e,n,t){let r=t.directiveStart,i=t.directiveEnd,o=t.index,s=Xb();try{Qn(o);for(let a=r;a<i;a++){let c=e.data[a],l=n[a];Nl(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&HE(c,l)}}finally{Qn(-1),Nl(s)}}function HE(e,n){e.hostBindings!==null&&e.hostBindings(1,n)}function zE(e,n){let t=e.directiveRegistry,r=null,i=null;if(t)for(let o=0;o<t.length;o++){let s=t[o];if(km(n,s.selectors,!1))if(r||(r=[]),pn(s))if(s.findHostDirectiveDefs!==null){let a=[];i=i||new Map,s.findHostDirectiveDefs(s,a,i),r.unshift(...a,s);let c=a.length;Ql(e,n,c)}else r.unshift(s),Ql(e,n,0);else i=i||new Map,s.findHostDirectiveDefs?.(s,r,i),r.push(s)}return r===null?null:[r,i]}function Ql(e,n,t){n.componentOffset=t,(e.components??=[]).push(n.index)}function GE(e,n,t){if(n){let r=e.localNames=[];for(let i=0;i<n.length;i+=2){let o=t[n[i+1]];if(o==null)throw new R(-301,!1);r.push(n[i],o)}}}function WE(e,n,t){if(t){if(n.exportAs)for(let r=0;r<n.exportAs.length;r++)t[n.exportAs[r]]=e;pn(n)&&(t[""]=e)}}function qE(e,n,t){e.flags|=1,e.directiveStart=n,e.directiveEnd=n+t,e.providerIndexes=n}function ZE(e,n,t,r,i){e.data[r]=i;let o=i.factory||(i.factory=Gn(i.type,!0)),s=new Yn(o,pn(i),u);e.blueprint[r]=s,t[r]=s,BE(e,n,r,ly(e,t,i.hostVars,it),i)}function QE(e,n,t){let r=rt(n,e),i=dy(t),o=e[Mt].rendererFactory,s=16;t.signals?s=4096:t.onPush&&(s=64);let a=la(e,ca(e,i,null,s,r,n,null,o.createRenderer(r,t),null,null,null));e[n.index]=a}function YE(e,n,t,r,i,o){let s=rt(e,n);KE(n[de],s,o,e.value,t,r,i)}function KE(e,n,t,r,i,o,s){if(o==null)e.removeAttribute(n,i,t);else{let a=s==null?zn(o):s(o,r||"",i);e.setAttribute(n,i,a,t)}}function XE(e,n,t,r,i,o){let s=o[n];if(s!==null)for(let a=0;a<s.length;){let c=s[a++],l=s[a++],d=s[a++],f=s[a++];cy(r,t,c,l,d,f)}}function JE(e,n,t){let r=null,i=0;for(;i<t.length;){let o=t[i];if(o===0){i+=4;continue}else if(o===5){i+=2;continue}if(typeof o=="number")break;if(e.hasOwnProperty(o)){r===null&&(r=[]);let s=e[o];for(let a=0;a<s.length;a+=3)if(s[a]===n){r.push(o,s[a+1],s[a+2],t[i+1]);break}}i+=2}return r}function py(e,n,t,r){return[e,!0,0,n,null,r,null,t,null,null]}function gy(e,n){let t=e.contentQueries;if(t!==null){let r=Q(null);try{for(let i=0;i<t.length;i+=2){let o=t[i],s=t[i+1];if(s!==-1){let a=e.data[s];Fu(o),a.contentQueries(2,n[s],s)}}}finally{Q(r)}}}function la(e,n){return e[Ei]?e[Ug][ft]=n:e[Ei]=n,e[Ug]=n,n}function Yl(e,n,t){Fu(0);let r=Q(null);try{n(e,t)}finally{Q(r)}}function my(e){return e[Cs]??=[]}function vy(e){return e.cleanup??=[]}function yy(e,n){let t=e[kr],r=t?t.get(Ut,null):null;r&&r.handleError(n)}function id(e,n,t,r,i){for(let o=0;o<t.length;){let s=t[o++],a=t[o++],c=t[o++],l=n[s],d=e.data[s];cy(d,l,r,a,c,i)}}function Dy(e,n,t){let r=Jm(n,e);fE(e[de],r,t)}function eM(e,n){let t=yn(n,e),r=t[U];tM(r,t);let i=t[Ht];i!==null&&t[wi]===null&&(t[wi]=Wu(i,t[kr])),od(r,t,t[ht])}function tM(e,n){for(let t=n.length;t<e.blueprint.length;t++)n.push(e.blueprint[t])}function od(e,n,t){ju(n);try{let r=e.viewQuery;r!==null&&Yl(1,r,t);let i=e.template;i!==null&&uy(e,n,i,1,t),e.firstCreatePass&&(e.firstCreatePass=!1),n[Bt]?.finishViewCreation(e),e.staticContentQueries&&gy(e,n),e.staticViewQueries&&Yl(2,e.viewQuery,t);let o=e.components;o!==null&&nM(n,o)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{n[P]&=-5,Lu()}}function nM(e,n){for(let t=0;t<n.length;t++)eM(e,n[t])}function Iy(e,n,t,r){let i=Q(null);try{let o=n.tView,a=e[P]&4096?4096:16,c=ca(e,o,t,a,null,n,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[n.index];c[Wn]=l;let d=e[Bt];return d!==null&&(c[Bt]=d.createEmbeddedView(o)),od(o,c,t),c}finally{Q(i)}}function Kl(e,n){return!n||n.firstChild===null||Rv(e)}function Cy(e,n,t,r=!0){let i=n[U];if(vE(i,n,e,t),r){let s=Zl(t,e),a=n[de],c=ey(a,e[qn]);c!==null&&gE(i,e[Ue],a,n,c,s)}let o=n[wi];o!==null&&o.firstChild!==null&&(o.firstChild=null)}function Ns(e,n,t,r,i=!1){for(;t!==null;){if(t.type===128){t=i?t.projectionNext:t.next;continue}let o=n[t.index];o!==null&&r.push(_t(o)),zt(o)&&rM(o,r);let s=t.type;if(s&8)Ns(e,n,t.child,r);else if(s&32){let a=Qu(t,n),c;for(;c=a();)r.push(c)}else if(s&16){let a=ny(n,t);if(Array.isArray(a))r.push(...a);else{let c=Zn(n[Je]);Ns(c[U],c,a,r,!0)}}t=i?t.projectionNext:t.next}return r}function rM(e,n){for(let t=Ge;t<e.length;t++){let r=e[t],i=r[U].firstChild;i!==null&&Ns(r[U],r,i,n)}e[qn]!==e[Ht]&&n.push(e[qn])}var by=[];function iM(e){return e[Xe]??oM(e)}function oM(e){let n=by.pop()??Object.create(aM);return n.lView=e,n}function sM(e){e.lView[Xe]!==e&&(e.lView=null,by.push(e))}var aM=V(b({},vi),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{Ks(e.lView)},consumerOnSignalRead(){this.lView[Xe]=this}});function cM(e){let n=e[Xe]??Object.create(lM);return n.lView=e,n}var lM=V(b({},vi),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{let n=Zn(e.lView);for(;n&&!wy(n[U]);)n=Zn(n);n&&nv(n)},consumerOnSignalRead(){this.lView[Xe]=this}});function wy(e){return e.type!==2}var uM=100;function Ey(e,n=!0,t=0){let r=e[Mt],i=r.rendererFactory,o=!1;o||i.begin?.();try{dM(e,t)}catch(s){throw n&&yy(e,s),s}finally{o||(i.end?.(),r.inlineEffectRunner?.flush())}}function dM(e,n){let t=cv();try{Hg(!0),Xl(e,n);let r=0;for(;Ys(e);){if(r===uM)throw new R(103,!1);r++,Xl(e,1)}}finally{Hg(t)}}function fM(e,n,t,r){let i=n[P];if((i&256)===256)return;let o=!1,s=!1;!o&&n[Mt].inlineEffectRunner?.flush(),ju(n);let a=!0,c=null,l=null;o||(wy(e)?(l=iM(n),c=ts(l)):yg()===null?(a=!1,l=cM(n),c=ts(l)):n[Xe]&&(ol(n[Xe]),n[Xe]=null));try{tv(n),Qb(e.bindingStartIndex),t!==null&&uy(e,n,t,2,r);let d=(i&3)===3;if(!o)if(d){let h=e.preOrderCheckHooks;h!==null&&fs(n,h,null)}else{let h=e.preOrderHooks;h!==null&&hs(n,h,0,null),ul(n,0)}if(s||hM(n),My(n,0),e.contentQueries!==null&&gy(e,n),!o)if(d){let h=e.contentCheckHooks;h!==null&&fs(n,h)}else{let h=e.contentHooks;h!==null&&hs(n,h,1),ul(n,1)}xE(e,n);let f=e.components;f!==null&&Sy(n,f,0);let p=e.viewQuery;if(p!==null&&Yl(2,p,r),!o)if(d){let h=e.viewCheckHooks;h!==null&&fs(n,h)}else{let h=e.viewHooks;h!==null&&hs(n,h,2),ul(n,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),n[ll]){for(let h of n[ll])h();n[ll]=null}o||(n[P]&=-73)}catch(d){throw o||Ks(n),d}finally{l!==null&&(rl(l,c),a&&sM(l)),Lu()}}function My(e,n){for(let t=Ov(e);t!==null;t=kv(t))for(let r=Ge;r<t.length;r++){let i=t[r];_y(i,n)}}function hM(e){for(let n=Ov(e);n!==null;n=kv(n)){if(!(n[P]&ws.HasTransplantedViews))continue;let t=n[Pr];for(let r=0;r<t.length;r++){let i=t[r];nv(i)}}}function pM(e,n,t){let r=yn(n,e);_y(r,t)}function _y(e,n){Au(e)&&Xl(e,n)}function Xl(e,n){let r=e[U],i=e[P],o=e[Xe],s=!!(n===0&&i&16);if(s||=!!(i&64&&n===0),s||=!!(i&1024),s||=!!(o?.dirty&&il(o)),s||=!1,o&&(o.dirty=!1),e[P]&=-9217,s)fM(r,e,r.template,e[ht]);else if(i&8192){My(e,1);let a=r.components;a!==null&&Sy(e,a,1)}}function Sy(e,n,t){for(let r=0;r<n.length;r++)pM(e,n[r],t)}function sd(e,n){let t=cv()?64:1088;for(e[Mt].changeDetectionScheduler?.notify(n);e;){e[P]|=t;let r=Zn(e);if(xl(e)&&!r)return e;e=r}return null}var Xn=class{get rootNodes(){let n=this._lView,t=n[U];return Ns(t,n,t.firstChild,[])}constructor(n,t,r=!0){this._lView=n,this._cdRefInjectingView=t,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[ht]}set context(n){this._lView[ht]=n}get destroyed(){return(this._lView[P]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let n=this._lView[Oe];if(zt(n)){let t=n[bs],r=t?t.indexOf(this):-1;r>-1&&(ql(n,r),Ds(t,r))}this._attachedToViewContainer=!1}Kv(this._lView[U],this._lView)}onDestroy(n){rv(this._lView,n)}markForCheck(){sd(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[P]&=-129}reattach(){Rl(this._lView),this._lView[P]|=128}detectChanges(){this._lView[P]|=1024,Ey(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new R(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let n=xl(this._lView),t=this._lView[Wn];t!==null&&!n&&Yu(t,this._lView),Qv(this._lView[U],this._lView)}attachToAppRef(n){if(this._attachedToViewContainer)throw new R(902,!1);this._appRef=n;let t=xl(this._lView),r=this._lView[Wn];r!==null&&!t&&Yv(r,this._lView),Rl(this._lView)}},St=(()=>{class e{static{this.__NG_ELEMENT_ID__=vM}}return e})(),gM=St,mM=class extends gM{constructor(n,t,r){super(),this._declarationLView=n,this._declarationTContainer=t,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,t){return this.createEmbeddedViewImpl(n,t)}createEmbeddedViewImpl(n,t,r){let i=Iy(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:t,dehydratedView:r});return new Xn(i)}};function vM(){return ad(Re(),q())}function ad(e,n){return e.type&4?new mM(n,e,$r(e,n)):null}var $B=new RegExp(`^(\\d+)*(${Pw}|${kw})*(.*)`);var yM=()=>null;function Jl(e,n){return yM(e,n)}var jr=class{},cd=new A("",{providedIn:"root",factory:()=>!1});var Ty=new A(""),xy=new A(""),eu=class{},Os=class{};function DM(e){let n=Error(`No component factory found for ${Fe(e)}.`);return n[IM]=e,n}var IM="ngComponent";var tu=class{resolveComponentFactory(n){throw DM(n)}},Lr=class{static{this.NULL=new tu}},Vr=class{},In=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>CM()}}return e})();function CM(){let e=q(),n=Re(),t=yn(n.index,e);return(fn(t)?t:e)[de]}var bM=(()=>{class e{static{this.\u0275prov=x({token:e,providedIn:"root",factory:()=>null})}}return e})();function ks(e,n,t){let r=t?e.styles:null,i=t?e.classes:null,o=0;if(n!==null)for(let s=0;s<n.length;s++){let a=n[s];if(typeof a=="number")o=a;else if(o==1)i=Cl(i,a);else if(o==2){let c=a,l=n[++s];r=Cl(r,c+": "+l+";")}}t?e.styles=r:e.stylesWithoutHost=r,t?e.classes=i:e.classesWithoutHost=i}var Ps=class extends Lr{constructor(n){super(),this.ngModule=n}resolveComponentFactory(n){let t=Vt(n);return new Jn(t,this.ngModule)}};function cm(e,n){let t=[];for(let r in e){if(!e.hasOwnProperty(r))continue;let i=e[r];if(i===void 0)continue;let o=Array.isArray(i),s=o?i[0]:i,a=o?i[1]:hn.None;n?t.push({propName:s,templateName:r,isSignal:(a&hn.SignalBased)!==0}):t.push({propName:s,templateName:r})}return t}function wM(e){let n=e.toLowerCase();return n==="svg"?Pb:n==="math"?Fb:null}var Jn=class extends Os{get inputs(){let n=this.componentDef,t=n.inputTransforms,r=cm(n.inputs,!0);if(t!==null)for(let i of r)t.hasOwnProperty(i.propName)&&(i.transform=t[i.propName]);return r}get outputs(){return cm(this.componentDef.outputs,!1)}constructor(n,t){super(),this.componentDef=n,this.ngModule=t,this.componentType=n.type,this.selector=pb(n.selectors),this.ngContentSelectors=n.ngContentSelectors?n.ngContentSelectors:[],this.isBoundToModule=!!t}create(n,t,r,i){let o=Q(null);try{i=i||this.ngModule;let s=i instanceof ue?i:i?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new Ol(n,s):n,c=a.get(Vr,null);if(c===null)throw new R(407,!1);let l=a.get(bM,null),d=a.get(jr,null),f={rendererFactory:c,sanitizer:l,inlineEffectRunner:null,changeDetectionScheduler:d},p=c.createRenderer(null,this.componentDef),h=this.componentDef.selectors[0][0]||"div",y=r?NE(p,r,this.componentDef.encapsulation,a):Zv(p,h,wM(h)),M=512;this.componentDef.signals?M|=4096:this.componentDef.onPush||(M|=16);let T=null;y!==null&&(T=Wu(y,a,!0));let F=td(0,null,null,1,0,null,null,null,null,null,null),re=ca(null,F,null,M,null,null,f,p,a,null,T);ju(re);let Z,me,Te=null;try{let Ce=this.componentDef,Ot,Ic=null;Ce.findHostDirectiveDefs?(Ot=[],Ic=new Map,Ce.findHostDirectiveDefs(Ce,Ot,Ic),Ot.push(Ce)):Ot=[Ce];let G0=EM(re,y);Te=MM(G0,y,Ce,Ot,re,f,p),me=ev(F,We),y&&TM(p,Ce,y,r),t!==void 0&&xM(me,this.ngContentSelectors,t),Z=SM(Te,Ce,Ot,Ic,re,[AM]),od(F,re,null)}catch(Ce){throw Te!==null&&$l(Te),$l(re),Ce}finally{Lu()}return new nu(this.componentType,Z,$r(me,re),re,me)}finally{Q(o)}}},nu=class extends eu{constructor(n,t,r,i,o){super(),this.location=r,this._rootLView=i,this._tNode=o,this.previousInputValues=null,this.instance=t,this.hostView=this.changeDetectorRef=new Xn(i,void 0,!1),this.componentType=n}setInput(n,t){let r=this._tNode.inputs,i;if(r!==null&&(i=r[n])){if(this.previousInputValues??=new Map,this.previousInputValues.has(n)&&Object.is(this.previousInputValues.get(n),t))return;let o=this._rootLView;id(o[U],o,i,n,t),this.previousInputValues.set(n,t);let s=yn(this._tNode.index,o);sd(s,1)}}get injector(){return new Hn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(n){this.hostView.onDestroy(n)}};function EM(e,n){let t=e[U],r=We;return e[r]=n,Hr(t,r,2,"#host",null)}function MM(e,n,t,r,i,o,s){let a=i[U];_M(r,e,n,s);let c=null;n!==null&&(c=Wu(n,i[kr]));let l=o.rendererFactory.createRenderer(n,t),d=16;t.signals?d=4096:t.onPush&&(d=64);let f=ca(i,dy(t),null,d,i[e.index],e,o,l,null,null,c);return a.firstCreatePass&&Ql(a,e,r.length-1),la(i,f),i[e.index]=f}function _M(e,n,t,r){for(let i of e)n.mergedAttrs=Ci(n.mergedAttrs,i.hostAttrs);n.mergedAttrs!==null&&(ks(n,n.mergedAttrs,!0),t!==null&&oy(r,t,n))}function SM(e,n,t,r,i,o){let s=Re(),a=i[U],c=rt(s,i);fy(a,i,s,t,null,r);for(let d=0;d<t.length;d++){let f=s.directiveStart+d,p=Kn(i,a,f,s);gn(p,i)}hy(a,i,s),c&&gn(c,i);let l=Kn(i,a,s.directiveStart+s.componentOffset,s);if(e[ht]=i[ht]=l,o!==null)for(let d of o)d(l,n);return Xu(a,s,i),l}function TM(e,n,t,r){if(r)Ml(e,t,["ng-version","18.2.13"]);else{let{attrs:i,classes:o}=gb(n.selectors[0]);i&&Ml(e,t,i),o&&o.length>0&&iy(e,t,o.join(" "))}}function xM(e,n,t){let r=e.projection=[];for(let i=0;i<n.length;i++){let o=t[i];r.push(o!=null?Array.from(o):null)}}function AM(){let e=Re();na(q()[U],e)}var qe=(()=>{class e{static{this.__NG_ELEMENT_ID__=RM}}return e})();function RM(){let e=Re();return Ry(e,q())}var NM=qe,Ay=class extends NM{constructor(n,t,r){super(),this._lContainer=n,this._hostTNode=t,this._hostLView=r}get element(){return $r(this._hostTNode,this._hostLView)}get injector(){return new Hn(this._hostTNode,this._hostLView)}get parentInjector(){let n=Vu(this._hostTNode,this._hostLView);if(yv(n)){let t=Ms(n,this._hostLView),r=Es(n),i=t[U].data[r+8];return new Hn(i,t)}else return new Hn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(n){let t=lm(this._lContainer);return t!==null&&t[n]||null}get length(){return this._lContainer.length-Ge}createEmbeddedView(n,t,r){let i,o;typeof r=="number"?i=r:r!=null&&(i=r.index,o=r.injector);let s=Jl(this._lContainer,n.ssrId),a=n.createEmbeddedViewImpl(t||{},o,s);return this.insertImpl(a,i,Kl(this._hostTNode,s)),a}createComponent(n,t,r,i,o){let s=n&&!Rb(n),a;if(s)a=t;else{let y=t||{};a=y.index,r=y.injector,i=y.projectableNodes,o=y.environmentInjector||y.ngModuleRef}let c=s?n:new Jn(Vt(n)),l=r||this.parentInjector;if(!o&&c.ngModule==null){let M=(s?l:this.parentInjector).get(ue,null);M&&(o=M)}let d=Vt(c.componentType??{}),f=Jl(this._lContainer,d?.id??null),p=f?.firstChild??null,h=c.create(l,i,p,o);return this.insertImpl(h.hostView,a,Kl(this._hostTNode,f)),h}insert(n,t){return this.insertImpl(n,t,!0)}insertImpl(n,t,r){let i=n._lView;if(Vb(i)){let a=this.indexOf(n);if(a!==-1)this.detach(a);else{let c=i[Oe],l=new Ay(c,c[Ue],c[Oe]);l.detach(l.indexOf(n))}}let o=this._adjustIndex(t),s=this._lContainer;return Cy(s,i,o,r),n.attachToViewContainerRef(),Tm(gl(s),o,n),n}move(n,t){return this.insert(n,t)}indexOf(n){let t=lm(this._lContainer);return t!==null?t.indexOf(n):-1}remove(n){let t=this._adjustIndex(n,-1),r=ql(this._lContainer,t);r&&(Ds(gl(this._lContainer),t),Kv(r[U],r))}detach(n){let t=this._adjustIndex(n,-1),r=ql(this._lContainer,t);return r&&Ds(gl(this._lContainer),t)!=null?new Xn(r):null}_adjustIndex(n,t=0){return n??this.length+t}};function lm(e){return e[bs]}function gl(e){return e[bs]||(e[bs]=[])}function Ry(e,n){let t,r=n[e.index];return zt(r)?t=r:(t=py(r,n,null,e),n[e.index]=t,la(n,t)),kM(t,n,e,r),new Ay(t,e,n)}function OM(e,n){let t=e[de],r=t.createComment(""),i=rt(n,e),o=ey(t,i);return Rs(t,o,r,CE(t,i),!1),r}var kM=jM,PM=()=>!1;function FM(e,n,t){return PM(e,n,t)}function jM(e,n,t,r){if(e[qn])return;let i;t.type&8?i=_t(r):i=OM(n,t),e[qn]=i}var ru=class e{constructor(n){this.queryList=n,this.matches=null}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},iu=class e{constructor(n=[]){this.queries=n}createEmbeddedView(n){let t=n.queries;if(t!==null){let r=n.contentQueries!==null?n.contentQueries[0]:t.length,i=[];for(let o=0;o<r;o++){let s=t.getByIndex(o),a=this.queries[s.indexInDeclarationView];i.push(a.clone())}return new e(i)}return null}insertView(n){this.dirtyQueriesWithMatches(n)}detachView(n){this.dirtyQueriesWithMatches(n)}finishViewCreation(n){this.dirtyQueriesWithMatches(n)}dirtyQueriesWithMatches(n){for(let t=0;t<this.queries.length;t++)ld(n,t).matches!==null&&this.queries[t].setDirty()}},Fs=class{constructor(n,t,r=null){this.flags=t,this.read=r,typeof n=="string"?this.predicate=GM(n):this.predicate=n}},ou=class e{constructor(n=[]){this.queries=n}elementStart(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(n,t)}elementEnd(n){for(let t=0;t<this.queries.length;t++)this.queries[t].elementEnd(n)}embeddedTView(n){let t=null;for(let r=0;r<this.length;r++){let i=t!==null?t.length:0,o=this.getByIndex(r).embeddedTView(n,i);o&&(o.indexInDeclarationView=r,t!==null?t.push(o):t=[o])}return t!==null?new e(t):null}template(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].template(n,t)}getByIndex(n){return this.queries[n]}get length(){return this.queries.length}track(n){this.queries.push(n)}},su=class e{constructor(n,t=-1){this.metadata=n,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=t}elementStart(n,t){this.isApplyingToNode(t)&&this.matchTNode(n,t)}elementEnd(n){this._declarationNodeIndex===n.index&&(this._appliesToNextNode=!1)}template(n,t){this.elementStart(n,t)}embeddedTView(n,t){return this.isApplyingToNode(n)?(this.crossesNgTemplate=!0,this.addMatch(-n.index,t),new e(this.metadata)):null}isApplyingToNode(n){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let t=this._declarationNodeIndex,r=n.parent;for(;r!==null&&r.type&8&&r.index!==t;)r=r.parent;return t===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(n,t){let r=this.metadata.predicate;if(Array.isArray(r))for(let i=0;i<r.length;i++){let o=r[i];this.matchTNodeWithReadOption(n,t,LM(t,o)),this.matchTNodeWithReadOption(n,t,ps(t,n,o,!1,!1))}else r===St?t.type&4&&this.matchTNodeWithReadOption(n,t,-1):this.matchTNodeWithReadOption(n,t,ps(t,n,r,!1,!1))}matchTNodeWithReadOption(n,t,r){if(r!==null){let i=this.metadata.read;if(i!==null)if(i===m||i===qe||i===St&&t.type&4)this.addMatch(t.index,-2);else{let o=ps(t,n,i,!1,!1);o!==null&&this.addMatch(t.index,o)}else this.addMatch(t.index,r)}}addMatch(n,t){this.matches===null?this.matches=[n,t]:this.matches.push(n,t)}};function LM(e,n){let t=e.localNames;if(t!==null){for(let r=0;r<t.length;r+=2)if(t[r]===n)return t[r+1]}return null}function VM(e,n){return e.type&11?$r(e,n):e.type&4?ad(e,n):null}function BM(e,n,t,r){return t===-1?VM(n,e):t===-2?UM(e,n,r):Kn(e,e[U],t,n)}function UM(e,n,t){if(t===m)return $r(n,e);if(t===St)return ad(n,e);if(t===qe)return Ry(n,e)}function Ny(e,n,t,r){let i=n[Bt].queries[r];if(i.matches===null){let o=e.data,s=t.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let d=o[l];a.push(BM(n,d,s[c+1],t.metadata.read))}}i.matches=a}return i.matches}function au(e,n,t,r){let i=e.queries.getByIndex(t),o=i.matches;if(o!==null){let s=Ny(e,n,i,t);for(let a=0;a<o.length;a+=2){let c=o[a];if(c>0)r.push(s[a/2]);else{let l=o[a+1],d=n[-c];for(let f=Ge;f<d.length;f++){let p=d[f];p[Wn]===p[Oe]&&au(p[U],p,l,r)}if(d[Pr]!==null){let f=d[Pr];for(let p=0;p<f.length;p++){let h=f[p];au(h[U],h,l,r)}}}}}return r}function $M(e,n){return e[Bt].queries[n].queryList}function Oy(e,n,t){let r=new Ul((t&4)===4);return PE(e,n,r,r.destroy),(n[Bt]??=new iu).queries.push(new ru(r))-1}function HM(e,n,t){let r=pe();return r.firstCreatePass&&(ky(r,new Fs(e,n,t),-1),(n&2)===2&&(r.staticViewQueries=!0)),Oy(r,q(),n)}function zM(e,n,t,r){let i=pe();if(i.firstCreatePass){let o=Re();ky(i,new Fs(n,t,r),o.index),WM(i,e),(t&2)===2&&(i.staticContentQueries=!0)}return Oy(i,q(),t)}function GM(e){return e.split(",").map(n=>n.trim())}function ky(e,n,t){e.queries===null&&(e.queries=new ou),e.queries.track(new su(n,t))}function WM(e,n){let t=e.contentQueries||(e.contentQueries=[]),r=t.length?t[t.length-1]:-1;n!==r&&t.push(e.queries.length-1,n)}function ld(e,n){return e.queries.getByIndex(n)}function qM(e,n){let t=e[U],r=ld(t,n);return r.crossesNgTemplate?au(t,e,n,[]):Ny(t,e,r,n)}var um=new Set;function zr(e){um.has(e)||(um.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}function ZM(e){return typeof e=="function"&&e[jt]!==void 0}function Ni(e,n){zr("NgSignals");let t=xg(e),r=t[jt];return n?.equal&&(r.equal=n.equal),t.set=i=>sl(r,i),t.update=i=>Ag(r,i),t.asReadonly=QM.bind(t),t}function QM(){let e=this[jt];if(e.readonlyFn===void 0){let n=()=>this();n[jt]=e,e.readonlyFn=n}return e.readonlyFn}function Py(e){return ZM(e)&&typeof e.set=="function"}function YM(e){return Object.getPrototypeOf(e.prototype).constructor}function oe(e){let n=YM(e.type),t=!0,r=[e];for(;n;){let i;if(pn(e))i=n.\u0275cmp||n.\u0275dir;else{if(n.\u0275cmp)throw new R(903,!1);i=n.\u0275dir}if(i){if(t){r.push(i);let s=e;s.inputs=cs(e.inputs),s.inputTransforms=cs(e.inputTransforms),s.declaredInputs=cs(e.declaredInputs),s.outputs=cs(e.outputs);let a=i.hostBindings;a&&t_(e,a);let c=i.viewQuery,l=i.contentQueries;if(c&&JM(e,c),l&&e_(e,l),KM(e,i),OC(e.outputs,i.outputs),pn(i)&&i.data.animation){let d=e.data;d.animation=(d.animation||[]).concat(i.data.animation)}}let o=i.features;if(o)for(let s=0;s<o.length;s++){let a=o[s];a&&a.ngInherit&&a(e),a===oe&&(t=!1)}}n=Object.getPrototypeOf(n)}XM(r)}function KM(e,n){for(let t in n.inputs){if(!n.inputs.hasOwnProperty(t)||e.inputs.hasOwnProperty(t))continue;let r=n.inputs[t];if(r!==void 0&&(e.inputs[t]=r,e.declaredInputs[t]=n.declaredInputs[t],n.inputTransforms!==null)){let i=Array.isArray(r)?r[0]:r;if(!n.inputTransforms.hasOwnProperty(i))continue;e.inputTransforms??={},e.inputTransforms[i]=n.inputTransforms[i]}}}function XM(e){let n=0,t=null;for(let r=e.length-1;r>=0;r--){let i=e[r];i.hostVars=n+=i.hostVars,i.hostAttrs=Ci(i.hostAttrs,t=Ci(t,i.hostAttrs))}}function cs(e){return e===Rr?{}:e===Be?[]:e}function JM(e,n){let t=e.viewQuery;t?e.viewQuery=(r,i)=>{n(r,i),t(r,i)}:e.viewQuery=n}function e_(e,n){let t=e.contentQueries;t?e.contentQueries=(r,i,o)=>{n(r,i,o),t(r,i,o)}:e.contentQueries=n}function t_(e,n){let t=e.hostBindings;t?e.hostBindings=(r,i)=>{n(r,i),t(r,i)}:e.hostBindings=n}function ud(e){let n=e.inputConfig,t={};for(let r in n)if(n.hasOwnProperty(r)){let i=n[r];Array.isArray(i)&&i[3]&&(t[r]=i[3])}e.inputTransforms=t}var mn=class{},Mi=class{};var cu=class extends mn{constructor(n,t,r,i=!0){super(),this.ngModuleType=n,this._parent=t,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new Ps(this);let o=Vm(n);this._bootstrapComponents=qv(o.bootstrap),this._r3Injector=Sv(n,t,[{provide:mn,useValue:this},{provide:Lr,useValue:this.componentFactoryResolver},...r],Fe(n),new Set(["environment"])),i&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let n=this._r3Injector;!n.destroyed&&n.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(n){this.destroyCbs.push(n)}},lu=class extends Mi{constructor(n){super(),this.moduleType=n}create(n){return new cu(this.moduleType,n,[])}};var js=class extends mn{constructor(n){super(),this.componentFactoryResolver=new Ps(this),this.instance=null;let t=new bi([...n.providers,{provide:mn,useValue:this},{provide:Lr,useValue:this.componentFactoryResolver}],n.parent||Ws(),n.debugName,new Set(["environment"]));this.injector=t,n.runEnvironmentInitializers&&t.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(n){this.injector.onDestroy(n)}};function ua(e,n,t=null){return new js({providers:e,parent:n,debugName:t,runEnvironmentInitializers:!0}).injector}function Fy(e){return r_(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function n_(e,n){if(Array.isArray(e))for(let t=0;t<e.length;t++)n(e[t]);else{let t=e[Symbol.iterator](),r;for(;!(r=t.next()).done;)n(r.value)}}function r_(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function i_(e,n,t){return e[n]=t}function vn(e,n,t){let r=e[n];return Object.is(r,t)?!1:(e[n]=t,!0)}function jy(e,n,t,r){let i=vn(e,n,t);return vn(e,n+1,r)||i}function o_(e){return(e.flags&32)===32}function s_(e,n,t,r,i,o,s,a,c){let l=n.consts,d=Hr(n,e,4,s||null,a||null);rd(n,t,d,Fr(l,c)),na(n,d);let f=d.tView=td(2,d,r,i,o,n.directiveRegistry,n.pipeRegistry,null,n.schemas,l,null);return n.queries!==null&&(n.queries.template(n,d),f.queries=n.queries.embeddedTView(d)),d}function Ly(e,n,t,r,i,o,s,a,c,l){let d=t+We,f=n.firstCreatePass?s_(d,n,e,r,i,o,s,a,c):n.data[d];tr(f,!1);let p=a_(n,e,f,t);ea()&&oa(n,e,p,f),gn(p,e);let h=py(p,e,p,f);return e[d]=h,la(e,h),FM(h,f,e),Qs(f)&&Ju(n,e,f),c!=null&&ed(e,f,l),f}function Oi(e,n,t,r,i,o,s,a){let c=q(),l=pe(),d=Fr(l.consts,o);return Ly(c,l,e,n,t,r,i,d,s,a),Oi}var a_=c_;function c_(e,n,t,r){return ta(!0),n[de].createComment("")}var Tr=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Tr||{}),Vy=(()=>{class e{constructor(){this.impl=null}execute(){this.impl?.execute()}static{this.\u0275prov=x({token:e,providedIn:"root",factory:()=>new e})}}return e})(),uu=class e{constructor(){this.ngZone=v(g),this.scheduler=v(jr),this.errorHandler=v(Ut,{optional:!0}),this.sequences=new Set,this.deferredRegistrations=new Set,this.executing=!1}static{this.PHASES=[Tr.EarlyRead,Tr.Write,Tr.MixedReadWrite,Tr.Read]}execute(){this.executing=!0;for(let n of e.PHASES)for(let t of this.sequences)if(!(t.erroredOrDestroyed||!t.hooks[n]))try{t.pipelinedValue=this.ngZone.runOutsideAngular(()=>t.hooks[n](t.pipelinedValue))}catch(r){t.erroredOrDestroyed=!0,this.errorHandler?.handleError(r)}this.executing=!1;for(let n of this.sequences)n.afterRun(),n.once&&(this.sequences.delete(n),n.destroy());for(let n of this.deferredRegistrations)this.sequences.add(n);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear()}register(n){this.executing?this.deferredRegistrations.add(n):(this.sequences.add(n),this.scheduler.notify(6))}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}static{this.\u0275prov=x({token:e,providedIn:"root",factory:()=>new e})}},du=class{constructor(n,t,r,i){this.impl=n,this.hooks=t,this.once=r,this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.()}};function dd(e,n){!n?.injector&&Ab(dd);let t=n?.injector??v(le);return lE(t)?(zr("NgAfterNextRender"),u_(e,t,n,!0)):d_}function l_(e,n){if(e instanceof Function){let t=[void 0,void 0,void 0,void 0];return t[n]=e,t}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function u_(e,n,t,r){let i=n.get(Vy);i.impl??=n.get(uu);let o=t?.phase??Tr.MixedReadWrite,s=t?.manualCleanup!==!0?n.get(Bu):null,a=new du(i.impl,l_(e,o),r,s);return i.impl.register(a),a}var d_={destroy(){}};function xt(e,n,t,r){let i=q(),o=Xs();if(vn(i,o,n)){let s=pe(),a=Js();YE(a,i,e,n,t,r)}return xt}function By(e,n,t,r){return vn(e,Xs(),t)?n+zn(t)+r:it}function f_(e,n,t,r,i,o){let s=Zb(),a=jy(e,s,t,i);return Pu(2),a?n+zn(t)+r+zn(i)+o:it}function ls(e,n){return e<<17|n<<2}function er(e){return e>>17&32767}function h_(e){return(e&2)==2}function p_(e,n){return e&131071|n<<17}function fu(e){return e|2}function Br(e){return(e&131068)>>2}function ml(e,n){return e&-131069|n<<2}function g_(e){return(e&1)===1}function hu(e){return e|1}function m_(e,n,t,r,i,o){let s=o?n.classBindings:n.styleBindings,a=er(s),c=Br(s);e[r]=t;let l=!1,d;if(Array.isArray(t)){let f=t;d=f[1],(d===null||Ti(f,d)>0)&&(l=!0)}else d=t;if(i)if(c!==0){let p=er(e[a+1]);e[r+1]=ls(p,a),p!==0&&(e[p+1]=ml(e[p+1],r)),e[a+1]=p_(e[a+1],r)}else e[r+1]=ls(a,0),a!==0&&(e[a+1]=ml(e[a+1],r)),a=r;else e[r+1]=ls(c,0),a===0?a=r:e[c+1]=ml(e[c+1],r),c=r;l&&(e[r+1]=fu(e[r+1])),dm(e,d,r,!0),dm(e,d,r,!1),v_(n,d,e,r,o),s=ls(a,c),o?n.classBindings=s:n.styleBindings=s}function v_(e,n,t,r,i){let o=i?e.residualClasses:e.residualStyles;o!=null&&typeof n=="string"&&Ti(o,n)>=0&&(t[r+1]=hu(t[r+1]))}function dm(e,n,t,r){let i=e[t+1],o=n===null,s=r?er(i):Br(i),a=!1;for(;s!==0&&(a===!1||o);){let c=e[s],l=e[s+1];y_(c,n)&&(a=!0,e[s+1]=r?hu(l):fu(l)),s=r?er(l):Br(l)}a&&(e[t+1]=r?fu(i):hu(i))}function y_(e,n){return e===null||n==null||(Array.isArray(e)?e[1]:e)===n?!0:Array.isArray(e)&&typeof n=="string"?Ti(e,n)>=0:!1}var dt={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function D_(e){return e.substring(dt.key,dt.keyEnd)}function I_(e){return C_(e),Uy(e,$y(e,0,dt.textEnd))}function Uy(e,n){let t=dt.textEnd;return t===n?-1:(n=dt.keyEnd=b_(e,dt.key=n,t),$y(e,n,t))}function C_(e){dt.key=0,dt.keyEnd=0,dt.value=0,dt.valueEnd=0,dt.textEnd=e.length}function $y(e,n,t){for(;n<t&&e.charCodeAt(n)<=32;)n++;return n}function b_(e,n,t){for(;n<t&&e.charCodeAt(n)>32;)n++;return n}function Cn(e,n,t){let r=q(),i=Xs();if(vn(r,i,n)){let o=pe(),s=Js();nd(o,s,r,e,n,r[de],t,!1)}return Cn}function pu(e,n,t,r,i){let o=n.inputs,s=i?"class":"style";id(e,t,o[s],s,r)}function Hy(e,n,t){return zy(e,n,t,!1),Hy}function da(e,n){return zy(e,n,null,!0),da}function zB(e){E_(A_,w_,e,!0)}function w_(e,n){for(let t=I_(n);t>=0;t=Uy(n,t))_u(e,D_(n),!0)}function zy(e,n,t,r){let i=q(),o=pe(),s=Pu(2);if(o.firstUpdatePass&&Wy(o,e,s,r),n!==it&&vn(i,s,n)){let a=o.data[Dn()];qy(o,a,i,i[de],e,i[s+1]=N_(n,t),r,s)}}function E_(e,n,t,r){let i=pe(),o=Pu(2);i.firstUpdatePass&&Wy(i,null,o,r);let s=q();if(t!==it&&vn(s,o,t)){let a=i.data[Dn()];if(Zy(a,r)&&!Gy(i,o)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(t=Cl(c,t||"")),pu(i,a,s,t,r)}else R_(i,a,s,s[de],s[o+1],s[o+1]=x_(e,n,t),r,o)}}function Gy(e,n){return n>=e.expandoStartIndex}function Wy(e,n,t,r){let i=e.data;if(i[t+1]===null){let o=i[Dn()],s=Gy(e,t);Zy(o,r)&&n===null&&!s&&(n=!1),n=M_(i,o,n,r),m_(i,o,n,t,s,r)}}function M_(e,n,t,r){let i=Jb(e),o=r?n.residualClasses:n.residualStyles;if(i===null)(r?n.classBindings:n.styleBindings)===0&&(t=vl(null,e,n,t,r),t=_i(t,n.attrs,r),o=null);else{let s=n.directiveStylingLast;if(s===-1||e[s]!==i)if(t=vl(i,e,n,t,r),o===null){let c=__(e,n,r);c!==void 0&&Array.isArray(c)&&(c=vl(null,e,n,c[1],r),c=_i(c,n.attrs,r),S_(e,n,r,c))}else o=T_(e,n,r)}return o!==void 0&&(r?n.residualClasses=o:n.residualStyles=o),t}function __(e,n,t){let r=t?n.classBindings:n.styleBindings;if(Br(r)!==0)return e[er(r)]}function S_(e,n,t,r){let i=t?n.classBindings:n.styleBindings;e[er(i)]=r}function T_(e,n,t){let r,i=n.directiveEnd;for(let o=1+n.directiveStylingLast;o<i;o++){let s=e[o].hostAttrs;r=_i(r,s,t)}return _i(r,n.attrs,t)}function vl(e,n,t,r,i){let o=null,s=t.directiveEnd,a=t.directiveStylingLast;for(a===-1?a=t.directiveStart:a++;a<s&&(o=n[a],r=_i(r,o.hostAttrs,i),o!==e);)a++;return e!==null&&(t.directiveStylingLast=a),r}function _i(e,n,t){let r=t?1:2,i=-1;if(n!==null)for(let o=0;o<n.length;o++){let s=n[o];typeof s=="number"?i=s:i===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),_u(e,s,t?!0:n[++o]))}return e===void 0?null:e}function x_(e,n,t){if(t==null||t==="")return Be;let r=[],i=nr(t);if(Array.isArray(i))for(let o=0;o<i.length;o++)e(r,i[o],!0);else if(typeof i=="object")for(let o in i)i.hasOwnProperty(o)&&e(r,o,i[o]);else typeof i=="string"&&n(r,i);return r}function A_(e,n,t){let r=String(n);r!==""&&!r.includes(" ")&&_u(e,r,t)}function R_(e,n,t,r,i,o,s,a){i===it&&(i=Be);let c=0,l=0,d=0<i.length?i[0]:null,f=0<o.length?o[0]:null;for(;d!==null||f!==null;){let p=c<i.length?i[c+1]:void 0,h=l<o.length?o[l+1]:void 0,y=null,M;d===f?(c+=2,l+=2,p!==h&&(y=f,M=h)):f===null||d!==null&&d<f?(c+=2,y=d):(l+=2,y=f,M=h),y!==null&&qy(e,n,t,r,y,M,s,a),d=c<i.length?i[c]:null,f=l<o.length?o[l]:null}}function qy(e,n,t,r,i,o,s,a){if(!(n.type&3))return;let c=e.data,l=c[a+1],d=g_(l)?fm(c,n,t,i,Br(l),s):void 0;if(!Ls(d)){Ls(o)||h_(l)&&(o=fm(c,null,t,i,a,s));let f=Jm(Dn(),t);SE(r,s,f,i,o)}}function fm(e,n,t,r,i,o){let s=n===null,a;for(;i>0;){let c=e[i],l=Array.isArray(c),d=l?c[1]:c,f=d===null,p=t[i+1];p===it&&(p=f?Be:void 0);let h=f?al(p,r):d===r?p:void 0;if(l&&!Ls(h)&&(h=al(c,r)),Ls(h)&&(a=h,s))return a;let y=e[i+1];i=s?er(y):Br(y)}if(n!==null){let c=o?n.residualClasses:n.residualStyles;c!=null&&(a=al(c,r))}return a}function Ls(e){return e!==void 0}function N_(e,n){return e==null||e===""||(typeof n=="string"?e=e+n:typeof e=="object"&&(e=Fe(nr(e)))),e}function Zy(e,n){return(e.flags&(n?8:16))!==0}function O_(e,n,t,r,i,o){let s=n.consts,a=Fr(s,i),c=Hr(n,e,2,r,a);return rd(n,t,c,Fr(s,o)),c.attrs!==null&&ks(c,c.attrs,!1),c.mergedAttrs!==null&&ks(c,c.mergedAttrs,!0),n.queries!==null&&n.queries.elementStart(n,c),c}function Gr(e,n,t,r){let i=q(),o=pe(),s=We+e,a=i[de],c=o.firstCreatePass?O_(s,o,i,n,t,r):o.data[s],l=k_(o,i,c,a,n,e);i[s]=l;let d=Qs(c);return tr(c,!0),oy(a,l,c),!o_(c)&&ea()&&oa(o,i,l,c),$b()===0&&gn(l,i),Hb(),d&&(Ju(o,i,c),Xu(o,c,i)),r!==null&&ed(i,c),Gr}function Wr(){let e=Re();Ou()?ku():(e=e.parent,tr(e,!1));let n=e;Gb(n)&&Wb(),zb();let t=pe();return t.firstCreatePass&&(na(t,e),xu(e)&&t.queries.elementEnd(e)),n.classesWithoutHost!=null&&sw(n)&&pu(t,n,q(),n.classesWithoutHost,!0),n.stylesWithoutHost!=null&&aw(n)&&pu(t,n,q(),n.stylesWithoutHost,!1),Wr}function fd(e,n,t,r){return Gr(e,n,t,r),Wr(),fd}var k_=(e,n,t,r,i,o)=>(ta(!0),Zv(r,i,nw()));function P_(e,n,t,r,i){let o=n.consts,s=Fr(o,r),a=Hr(n,e,8,"ng-container",s);s!==null&&ks(a,s,!0);let c=Fr(o,i);return rd(n,t,a,c),n.queries!==null&&n.queries.elementStart(n,a),a}function fa(e,n,t){let r=q(),i=pe(),o=e+We,s=i.firstCreatePass?P_(o,i,r,n,t):i.data[o];tr(s,!0);let a=F_(i,r,s,e);return r[o]=a,ea()&&oa(i,r,a,s),gn(a,r),Qs(s)&&(Ju(i,r,s),Xu(i,s,r)),t!=null&&ed(r,s),fa}function ha(){let e=Re(),n=pe();return Ou()?ku():(e=e.parent,tr(e,!1)),n.firstCreatePass&&(na(n,e),xu(e)&&n.queries.elementEnd(e)),ha}function pa(e,n,t){return fa(e,n,t),ha(),pa}var F_=(e,n,t,r)=>(ta(!0),hE(n[de],""));function Qy(){return q()}var $n=void 0;function j_(e){let n=e,t=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return t===1&&r===0?1:5}var L_=["en",[["a","p"],["AM","PM"],$n],[["AM","PM"],$n,$n],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],$n,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],$n,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",$n,"{1} 'at' {0}",$n],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",j_],yl={};function ot(e){let n=V_(e),t=hm(n);if(t)return t;let r=n.split("-")[0];if(t=hm(r),t)return t;if(r==="en")return L_;throw new R(701,!1)}function hm(e){return e in yl||(yl[e]=Lt.ng&&Lt.ng.common&&Lt.ng.common.locales&&Lt.ng.common.locales[e]),yl[e]}var ge=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(ge||{});function V_(e){return e.toLowerCase().replace(/_/g,"-")}var Vs="en-US";var B_=Vs;function U_(e){typeof e=="string"&&(B_=e.toLowerCase().replace(/_/g,"-"))}var $_=(e,n,t)=>{};function Me(e,n,t,r){let i=q(),o=pe(),s=Re();return Yy(o,i,i[de],s,e,n,r),Me}function H_(e,n,t,r){let i=e.cleanup;if(i!=null)for(let o=0;o<i.length-1;o+=2){let s=i[o];if(s===t&&i[o+1]===r){let a=n[Cs],c=i[o+2];return a.length>c?a[c]:null}typeof s=="string"&&(o+=2)}return null}function Yy(e,n,t,r,i,o,s){let a=Qs(r),l=e.firstCreatePass&&vy(e),d=n[ht],f=my(n),p=!0;if(r.type&3||s){let M=rt(r,n),T=s?s(M):M,F=f.length,re=s?me=>s(_t(me[r.index])):r.index,Z=null;if(!s&&a&&(Z=H_(e,n,i,r.index)),Z!==null){let me=Z.__ngLastListenerFn__||Z;me.__ngNextListenerFn__=o,Z.__ngLastListenerFn__=o,p=!1}else{o=gm(r,n,d,o),$_(M,i,o);let me=t.listen(T,i,o);f.push(o,me),l&&l.push(i,re,F,F+1)}}else o=gm(r,n,d,o);let h=r.outputs,y;if(p&&h!==null&&(y=h[i])){let M=y.length;if(M)for(let T=0;T<M;T+=2){let F=y[T],re=y[T+1],Te=n[F][re].subscribe(o),Ce=f.length;f.push(o,Te),l&&l.push(i,r.index,Ce,-(Ce+1))}}}function pm(e,n,t,r){let i=Q(null);try{return bt(6,n,t),t(r)!==!1}catch(o){return yy(e,o),!1}finally{bt(7,n,t),Q(i)}}function gm(e,n,t,r){return function i(o){if(o===Function)return r;let s=e.componentOffset>-1?yn(e.index,n):n;sd(s,5);let a=pm(n,t,r,o),c=i.__ngNextListenerFn__;for(;c;)a=pm(n,t,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function ki(e=1){return tw(e)}function z_(e,n){let t=null,r=lb(e);for(let i=0;i<n.length;i++){let o=n[i];if(o==="*"){t=i;continue}if(r===null?km(e,o,!0):fb(r,o))return i}return t}function E(e){let n=q()[Je][Ue];if(!n.projection){let t=e?e.length:1,r=n.projection=eb(t,null),i=r.slice(),o=n.child;for(;o!==null;){if(o.type!==128){let s=e?z_(o,e):0;s!==null&&(i[s]?i[s].projectionNext=o:r[s]=o,i[s]=o)}o=o.next}}}function C(e,n=0,t,r,i,o){let s=q(),a=pe(),c=r?e+1:null;c!==null&&Ly(s,a,c,r,i,o,null,t);let l=Hr(a,We+e,16,null,t||null);l.projection===null&&(l.projection=n),ku();let f=!s[wi]||sv();s[Je][Ue].projection[l.projection]===null&&c!==null?G_(s,a,c):f&&(l.flags&32)!==32&&ME(a,s,l)}function G_(e,n,t){let r=We+t,i=n.data[r],o=e[r],s=Jl(o,i.tView.ssrId),a=Iy(e,i,void 0,{dehydratedView:s});Cy(o,a,0,Kl(i,s))}function W_(e,n,t){return Ky(e,"",n,"",t),W_}function Ky(e,n,t,r,i){let o=q(),s=By(o,n,t,r);if(s!==it){let a=pe(),c=Js();nd(a,c,o,e,s,o[de],i,!1)}return Ky}function bn(e,n,t,r){zM(e,n,t,r)}function Pi(e,n,t){HM(e,n,t)}function mt(e){let n=q(),t=pe(),r=uv();Fu(r+1);let i=ld(t,r);if(e.dirty&&Lb(n)===((i.metadata.flags&2)===2)){if(i.matches===null)e.reset([]);else{let o=qM(n,r);e.reset(o,Tw),e.notifyOnChanges()}return!0}return!1}function vt(){return $M(q(),uv())}function q_(e,n,t,r){t>=e.data.length&&(e.data[t]=null,e.blueprint[t]=null),n[t]=r}function GB(e,n=""){let t=q(),r=pe(),i=e+We,o=r.firstCreatePass?Hr(r,i,1,n,null):r.data[i],s=Z_(r,t,o,n,e);t[i]=s,ea()&&oa(r,t,s,o),tr(o,!1)}var Z_=(e,n,t,r,i)=>(ta(!0),dE(n[de],r));function Q_(e){return Xy("",e,""),Q_}function Xy(e,n,t){let r=q(),i=By(r,e,n,t);return i!==it&&Dy(r,Dn(),i),Xy}function Y_(e,n,t,r,i){let o=q(),s=f_(o,e,n,t,r,i);return s!==it&&Dy(o,Dn(),s),Y_}function K_(e,n,t){Py(n)&&(n=n());let r=q(),i=Xs();if(vn(r,i,n)){let o=pe(),s=Js();nd(o,s,r,e,n,r[de],t,!1)}return K_}function WB(e,n){let t=Py(e);return t&&e.set(n),t}function X_(e,n){let t=q(),r=pe(),i=Re();return Yy(r,t,t[de],i,e,n),X_}function J_(e,n,t){let r=pe();if(r.firstCreatePass){let i=pn(e);gu(t,r.data,r.blueprint,i,!0),gu(n,r.data,r.blueprint,i,!1)}}function gu(e,n,t,r,i){if(e=Pe(e),Array.isArray(e))for(let o=0;o<e.length;o++)gu(e[o],n,t,r,i);else{let o=pe(),s=q(),a=Re(),c=Or(e)?e:Pe(e.provide),l=Gm(e),d=a.providerIndexes&1048575,f=a.directiveStart,p=a.providerIndexes>>20;if(Or(e)||!e.multi){let h=new Yn(l,i,u),y=Il(c,n,i?d:d+p,f);y===-1?(Pl(Ss(a,s),o,c),Dl(o,e,n.length),n.push(c),a.directiveStart++,a.directiveEnd++,i&&(a.providerIndexes+=1048576),t.push(h),s.push(h)):(t[y]=h,s[y]=h)}else{let h=Il(c,n,d+p,f),y=Il(c,n,d,d+p),M=h>=0&&t[h],T=y>=0&&t[y];if(i&&!T||!i&&!M){Pl(Ss(a,s),o,c);let F=nS(i?tS:eS,t.length,i,r,l);!i&&T&&(t[y].providerFactory=F),Dl(o,e,n.length,0),n.push(c),a.directiveStart++,a.directiveEnd++,i&&(a.providerIndexes+=1048576),t.push(F),s.push(F)}else{let F=Jy(t[i?y:h],l,!i&&r);Dl(o,e,h>-1?h:y,F)}!i&&r&&T&&t[y].componentProviders++}}}function Dl(e,n,t,r){let i=Or(n),o=wb(n);if(i||o){let c=(o?Pe(n.useClass):n).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!i&&n.multi){let d=l.indexOf(t);d===-1?l.push(t,[r,c]):l[d+1].push(r,c)}else l.push(t,c)}}}function Jy(e,n,t){return t&&e.componentProviders++,e.multi.push(n)-1}function Il(e,n,t,r){for(let i=t;i<r;i++)if(n[i]===e)return i;return-1}function eS(e,n,t,r){return mu(this.multi,[])}function tS(e,n,t,r){let i=this.multi,o;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Kn(t,t[U],this.providerFactory.index,r);o=a.slice(0,s),mu(i,o);for(let c=s;c<a.length;c++)o.push(a[c])}else o=[],mu(i,o);return o}function mu(e,n){for(let t=0;t<e.length;t++){let r=e[t];n.push(r())}return n}function nS(e,n,t,r,i){let o=new Yn(e,t,u);return o.multi=[],o.index=n,o.componentProviders=0,Jy(o,i,r&&!t),o}function $e(e,n=[]){return t=>{t.providersResolver=(r,i)=>J_(r,i?i(e):e,n)}}var rS=(()=>{class e{constructor(t){this._injector=t,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(t){if(!t.standalone)return null;if(!this.cachedInjectors.has(t)){let r=$m(!1,t.type),i=r.length>0?ua([r],this._injector,`Standalone[${t.type.name}]`):null;this.cachedInjectors.set(t,i)}return this.cachedInjectors.get(t)}ngOnDestroy(){try{for(let t of this.cachedInjectors.values())t!==null&&t.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=x({token:e,providedIn:"environment",factory:()=>new e(N(ue))})}}return e})();function eD(e){zr("NgStandalone"),e.getStandaloneInjector=n=>n.get(rS).getOrCreateStandaloneInjector(e)}function qB(e,n,t,r,i){return tD(q(),lv(),e,n,t,r,i)}function iS(e,n){let t=e[n];return t===it?void 0:t}function tD(e,n,t,r,i,o,s){let a=n+t;return jy(e,a,i,o)?i_(e,a+2,s?r.call(s,i,o):r(i,o)):iS(e,a+2)}function ZB(e,n){let t=pe(),r,i=e+We;t.firstCreatePass?(r=oS(n,t.pipeRegistry),t.data[i]=r,r.onDestroy&&(t.destroyHooks??=[]).push(i,r.onDestroy)):r=t.data[i];let o=r.factory||(r.factory=Gn(r.type,!0)),s,a=Ve(u);try{let c=_s(!1),l=o();return _s(c),q_(t,q(),i,l),l}finally{Ve(a)}}function oS(e,n){if(n)for(let t=n.length-1;t>=0;t--){let r=n[t];if(e===r.name)return r}}function QB(e,n,t,r){let i=e+We,o=q(),s=jb(o,i);return sS(o,i)?tD(o,lv(),n,s.transform,t,r,s):s.transform(t,r)}function sS(e,n){return e[U].data[n].pure}var ga=(()=>{class e{log(t){console.log(t)}warn(t){console.warn(t)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();var nD=new A("");function rr(e){return!!e&&typeof e.then=="function"}function rD(e){return!!e&&typeof e.subscribe=="function"}var qr=new A(""),iD=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((t,r)=>{this.resolve=t,this.reject=r}),this.appInits=v(qr,{optional:!0})??[]}runInitializers(){if(this.initialized)return;let t=[];for(let i of this.appInits){let o=i();if(rr(o))t.push(o);else if(rD(o)){let s=new Promise((a,c)=>{o.subscribe({complete:a,error:c})});t.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(t).then(()=>{r()}).catch(i=>{this.reject(i)}),t.length===0&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Fi=new A("");function aS(){Tg(()=>{throw new R(600,!1)})}function cS(e){return e.isBoundToModule}var lS=10;function uS(e,n,t){try{let r=t();return rr(r)?r.catch(i=>{throw n.runOutsideAngular(()=>e.handleError(i)),i}):r}catch(r){throw n.runOutsideAngular(()=>e.handleError(r)),r}}var At=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=v(_w),this.afterRenderManager=v(Vy),this.zonelessEnabled=v(cd),this.dirtyFlags=0,this.deferredDirtyFlags=0,this.externalTestViews=new Set,this.beforeRender=new ae,this.afterTick=new ae,this.componentTypes=[],this.components=[],this.isStable=v(Wt).hasPendingTasks.pipe(B(t=>!t)),this._injector=v(ue)}get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}whenStable(){let t;return new Promise(r=>{t=this.isStable.subscribe({next:i=>{i&&r()}})}).finally(()=>{t.unsubscribe()})}get injector(){return this._injector}bootstrap(t,r){let i=t instanceof Os;if(!this._injector.get(iD).done){let p=!i&&Lm(t),h=!1;throw new R(405,h)}let s;i?s=t:s=this._injector.get(Lr).resolveComponentFactory(t),this.componentTypes.push(s.componentType);let a=cS(s)?void 0:this._injector.get(mn),c=r||s.selector,l=s.create(le.NULL,[],c,a),d=l.location.nativeElement,f=l.injector.get(nD,null);return f?.registerApplication(d),l.onDestroy(()=>{this.detachView(l.hostView),gs(this.components,l),f?.unregisterApplication(d)}),this._loadComponent(l),l}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){if(this._runningTick)throw new R(101,!1);let t=Q(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,Q(t),this.afterTick.next()}}synchronize(){let t=null;this._injector.destroyed||(t=this._injector.get(Vr,null,{optional:!0})),this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0;let r=0;for(;this.dirtyFlags!==0&&r++<lS;)this.synchronizeOnce(t)}synchronizeOnce(t){if(this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0,this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8,this.beforeRender.next(r);for(let{_lView:i,notifyErrorHandler:o}of this._views)dS(i,o,r,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&7)return}else t?.begin?.(),t?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:t})=>Ys(t))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(t){let r=t;this._views.push(r),r.attachToAppRef(this)}detachView(t){let r=t;gs(this._views,r),r.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView),this.tick(),this.components.push(t);let r=this._injector.get(Fi,[]);[...this._bootstrapListeners,...r].forEach(i=>i(t))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(t=>t()),this._views.slice().forEach(t=>t.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(t){return this._destroyListeners.push(t),()=>gs(this._destroyListeners,t)}destroy(){if(this._destroyed)throw new R(406,!1);let t=this._injector;t.destroy&&!t.destroyed&&t.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function gs(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function dS(e,n,t,r){if(!t&&!Ys(e))return;Ey(e,n,t&&!r?0:1)}var vu=class{constructor(n,t){this.ngModuleFactory=n,this.componentFactories=t}},ma=(()=>{class e{compileModuleSync(t){return new lu(t)}compileModuleAsync(t){return Promise.resolve(this.compileModuleSync(t))}compileModuleAndAllComponentsSync(t){let r=this.compileModuleSync(t),i=Vm(t),o=qv(i.declarations).reduce((s,a)=>{let c=Vt(a);return c&&s.push(new Jn(c)),s},[]);return new vu(r,o)}compileModuleAndAllComponentsAsync(t){return Promise.resolve(this.compileModuleAndAllComponentsSync(t))}clearCache(){}clearCacheFor(t){}getModuleId(t){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var fS=(()=>{class e{constructor(){this.zone=v(g),this.changeDetectionScheduler=v(jr),this.applicationRef=v(At)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function hS({ngZoneFactory:e,ignoreChangesOutsideZone:n,scheduleInRootZone:t}){return e??=()=>new g(V(b({},pS()),{scheduleInRootZone:t})),[{provide:g,useFactory:e},{provide:Nr,multi:!0,useFactory:()=>{let r=v(fS,{optional:!0});return()=>r.initialize()}},{provide:Nr,multi:!0,useFactory:()=>{let r=v(gS);return()=>{r.initialize()}}},n===!0?{provide:Ty,useValue:!0}:[],{provide:xy,useValue:t??Tv}]}function pS(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var gS=(()=>{class e{constructor(){this.subscription=new fe,this.initialized=!1,this.zone=v(g),this.pendingTasks=v(Wt)}initialize(){if(this.initialized)return;this.initialized=!0;let t=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(t=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{g.assertNotInAngularZone(),queueMicrotask(()=>{t!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(t),t=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{g.assertInAngularZone(),t??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var mS=(()=>{class e{constructor(){this.appRef=v(At),this.taskService=v(Wt),this.ngZone=v(g),this.zonelessEnabled=v(cd),this.disableScheduling=v(Ty,{optional:!0})??!1,this.zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run,this.schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}],this.subscriptions=new fe,this.angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(xs):null,this.scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(v(xy,{optional:!0})??!1),this.cancelScheduledCallback=null,this.useMicrotaskScheduler=!1,this.runningTick=!1,this.pendingRenderTaskId=null,this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Bl||!this.zoneIsDefined)}notify(t){if(!this.zonelessEnabled&&t===5)return;switch(t){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 7:{this.appRef.deferredDirtyFlags|=8;break}case 9:case 8:case 6:case 10:default:this.appRef.dirtyFlags|=8}if(!this.shouldScheduleTick())return;let r=this.useMicrotaskScheduler?Zg:xv;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>r(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>r(()=>this.tick()))}shouldScheduleTick(){return!(this.disableScheduling||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(xs+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let t=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(t),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Zg(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(t)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let t=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(t)}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function vS(){return typeof $localize<"u"&&$localize.locale||Vs}var va=new A("",{providedIn:"root",factory:()=>v(va,z.Optional|z.SkipSelf)||vS()});var yu=new A("");function us(e){return!e.moduleRef}function yS(e){let n=us(e)?e.r3Injector:e.moduleRef.injector,t=n.get(g);return t.run(()=>{us(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=n.get(Ut,null),i;if(t.runOutsideAngular(()=>{i=t.onError.subscribe({next:o=>{r.handleError(o)}})}),us(e)){let o=()=>n.destroy(),s=e.platformInjector.get(yu);s.add(o),n.onDestroy(()=>{i.unsubscribe(),s.delete(o)})}else{let o=()=>e.moduleRef.destroy(),s=e.platformInjector.get(yu);s.add(o),e.moduleRef.onDestroy(()=>{gs(e.allPlatformModules,e.moduleRef),i.unsubscribe(),s.delete(o)})}return uS(r,t,()=>{let o=n.get(iD);return o.runInitializers(),o.donePromise.then(()=>{let s=n.get(va,Vs);if(U_(s||Vs),us(e)){let a=n.get(At);return e.rootComponent!==void 0&&a.bootstrap(e.rootComponent),a}else return DS(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function DS(e,n){let t=e.injector.get(At);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>t.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(t);else throw new R(-403,!1);n.push(e)}var ms=null;function IS(e=[],n){return le.create({name:n,providers:[{provide:Gs,useValue:"platform"},{provide:yu,useValue:new Set([()=>ms=null])},...e]})}function CS(e=[]){if(ms)return ms;let n=IS(e);return ms=n,aS(),bS(n),n}function bS(e){e.get(zu,null)?.forEach(t=>t())}var D=(()=>{class e{static{this.__NG_ELEMENT_ID__=wS}}return e})();function wS(e){return ES(Re(),q(),(e&16)===16)}function ES(e,n,t){if(Zs(e)&&!t){let r=yn(e.index,n);return new Xn(r,r)}else if(e.type&175){let r=n[Je];return new Xn(r,n)}return null}var Du=class{constructor(){}supports(n){return Fy(n)}create(n){return new Iu(n)}},MS=(e,n)=>n,Iu=class{constructor(n){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=n||MS}forEachItem(n){let t;for(t=this._itHead;t!==null;t=t._next)n(t)}forEachOperation(n){let t=this._itHead,r=this._removalsHead,i=0,o=null;for(;t||r;){let s=!r||t&&t.currentIndex<mm(r,i,o)?t:r,a=mm(s,i,o),c=s.currentIndex;if(s===r)i--,r=r._nextRemoved;else if(t=t._next,s.previousIndex==null)i++;else{o||(o=[]);let l=a-i,d=c-i;if(l!=d){for(let p=0;p<l;p++){let h=p<o.length?o[p]:o[p]=0,y=h+p;d<=y&&y<l&&(o[p]=h+1)}let f=s.previousIndex;o[f]=d-l}}a!==c&&n(s,a,c)}}forEachPreviousItem(n){let t;for(t=this._previousItHead;t!==null;t=t._nextPrevious)n(t)}forEachAddedItem(n){let t;for(t=this._additionsHead;t!==null;t=t._nextAdded)n(t)}forEachMovedItem(n){let t;for(t=this._movesHead;t!==null;t=t._nextMoved)n(t)}forEachRemovedItem(n){let t;for(t=this._removalsHead;t!==null;t=t._nextRemoved)n(t)}forEachIdentityChange(n){let t;for(t=this._identityChangesHead;t!==null;t=t._nextIdentityChange)n(t)}diff(n){if(n==null&&(n=[]),!Fy(n))throw new R(900,!1);return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let t=this._itHead,r=!1,i,o,s;if(Array.isArray(n)){this.length=n.length;for(let a=0;a<this.length;a++)o=n[a],s=this._trackByFn(a,o),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,o,s,a),r=!0):(r&&(t=this._verifyReinsertion(t,o,s,a)),Object.is(t.item,o)||this._addIdentityChange(t,o)),t=t._next}else i=0,n_(n,a=>{s=this._trackByFn(i,a),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,a,s,i),r=!0):(r&&(t=this._verifyReinsertion(t,a,s,i)),Object.is(t.item,a)||this._addIdentityChange(t,a)),t=t._next,i++}),this.length=i;return this._truncate(t),this.collection=n,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let n;for(n=this._previousItHead=this._itHead;n!==null;n=n._next)n._nextPrevious=n._next;for(n=this._additionsHead;n!==null;n=n._nextAdded)n.previousIndex=n.currentIndex;for(this._additionsHead=this._additionsTail=null,n=this._movesHead;n!==null;n=n._nextMoved)n.previousIndex=n.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(n,t,r,i){let o;return n===null?o=this._itTail:(o=n._prev,this._remove(n)),n=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._reinsertAfter(n,o,i)):(n=this._linkedRecords===null?null:this._linkedRecords.get(r,i),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._moveAfter(n,o,i)):n=this._addAfter(new Cu(t,r),o,i)),n}_verifyReinsertion(n,t,r,i){let o=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return o!==null?n=this._reinsertAfter(o,n._prev,i):n.currentIndex!=i&&(n.currentIndex=i,this._addToMoves(n,i)),n}_truncate(n){for(;n!==null;){let t=n._next;this._addToRemovals(this._unlink(n)),n=t}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(n,t,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(n);let i=n._prevRemoved,o=n._nextRemoved;return i===null?this._removalsHead=o:i._nextRemoved=o,o===null?this._removalsTail=i:o._prevRemoved=i,this._insertAfter(n,t,r),this._addToMoves(n,r),n}_moveAfter(n,t,r){return this._unlink(n),this._insertAfter(n,t,r),this._addToMoves(n,r),n}_addAfter(n,t,r){return this._insertAfter(n,t,r),this._additionsTail===null?this._additionsTail=this._additionsHead=n:this._additionsTail=this._additionsTail._nextAdded=n,n}_insertAfter(n,t,r){let i=t===null?this._itHead:t._next;return n._next=i,n._prev=t,i===null?this._itTail=n:i._prev=n,t===null?this._itHead=n:t._next=n,this._linkedRecords===null&&(this._linkedRecords=new Bs),this._linkedRecords.put(n),n.currentIndex=r,n}_remove(n){return this._addToRemovals(this._unlink(n))}_unlink(n){this._linkedRecords!==null&&this._linkedRecords.remove(n);let t=n._prev,r=n._next;return t===null?this._itHead=r:t._next=r,r===null?this._itTail=t:r._prev=t,n}_addToMoves(n,t){return n.previousIndex===t||(this._movesTail===null?this._movesTail=this._movesHead=n:this._movesTail=this._movesTail._nextMoved=n),n}_addToRemovals(n){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Bs),this._unlinkedRecords.put(n),n.currentIndex=null,n._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=n,n._prevRemoved=null):(n._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=n),n}_addIdentityChange(n,t){return n.item=t,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=n:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=n,n}},Cu=class{constructor(n,t){this.item=n,this.trackById=t,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},bu=class{constructor(){this._head=null,this._tail=null}add(n){this._head===null?(this._head=this._tail=n,n._nextDup=null,n._prevDup=null):(this._tail._nextDup=n,n._prevDup=this._tail,n._nextDup=null,this._tail=n)}get(n,t){let r;for(r=this._head;r!==null;r=r._nextDup)if((t===null||t<=r.currentIndex)&&Object.is(r.trackById,n))return r;return null}remove(n){let t=n._prevDup,r=n._nextDup;return t===null?this._head=r:t._nextDup=r,r===null?this._tail=t:r._prevDup=t,this._head===null}},Bs=class{constructor(){this.map=new Map}put(n){let t=n.trackById,r=this.map.get(t);r||(r=new bu,this.map.set(t,r)),r.add(n)}get(n,t){let r=n,i=this.map.get(r);return i?i.get(n,t):null}remove(n){let t=n.trackById;return this.map.get(t).remove(n)&&this.map.delete(t),n}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function mm(e,n,t){let r=e.previousIndex;if(r===null)return r;let i=0;return t&&r<t.length&&(i=t[r]),r+n+i}function vm(){return new hd([new Du])}var hd=(()=>{class e{static{this.\u0275prov=x({token:e,providedIn:"root",factory:vm})}constructor(t){this.factories=t}static create(t,r){if(r!=null){let i=r.factories.slice();t=t.concat(i)}return new e(t)}static extend(t){return{provide:e,useFactory:r=>e.create(t,r||vm()),deps:[[e,new Eu,new zs]]}}find(t){let r=this.factories.find(i=>i.supports(t));if(r!=null)return r;throw new R(901,!1)}}return e})();function oD(e){try{let{rootComponent:n,appProviders:t,platformProviders:r}=e,i=CS(r),o=[hS({}),{provide:jr,useExisting:mS},...t||[]],s=new js({providers:o,parent:i,debugName:"",runEnvironmentInitializers:!1});return yS({r3Injector:s.injector,platformInjector:i,rootComponent:n})}catch(n){return Promise.reject(n)}}function wn(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function ji(e,n){zr("NgSignals");let t=Mg(e);return n?.equal&&(t[jt].equal=n.equal),t}function Zt(e){let n=Q(null);try{return e()}finally{Q(n)}}function sD(e,n){let t=Vt(e),r=n.elementInjector||Ws();return new Jn(t).create(r,n.projectableNodes,n.hostElement,n.environmentInjector)}function ya(e){let n=Vt(e);if(!n)return null;let t=new Jn(n);return{get selector(){return t.selector},get type(){return t.componentType},get inputs(){return t.inputs},get outputs(){return t.outputs},get ngContentSelectors(){return t.ngContentSelectors},get isStandalone(){return n.standalone},get isSignal(){return n.signals}}}var hD=null;function Xt(){return hD}function pD(e){hD??=e}var Sa=class{};var Ie=new A(""),wd=(()=>{class e{historyGo(t){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>v(SS),providedIn:"platform"})}}return e})(),gD=new A(""),SS=(()=>{class e extends wd{constructor(){super(),this._doc=v(Ie),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Xt().getBaseHref(this._doc)}onPopState(t){let r=Xt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",t,!1),()=>r.removeEventListener("popstate",t)}onHashChange(t){let r=Xt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",t,!1),()=>r.removeEventListener("hashchange",t)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(t){this._location.pathname=t}pushState(t,r,i){this._history.pushState(t,r,i)}replaceState(t,r,i){this._history.replaceState(t,r,i)}forward(){this._history.forward()}back(){this._history.back()}historyGo(t=0){this._history.go(t)}getState(){return this._history.state}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>new e,providedIn:"platform"})}}return e})();function Ed(e,n){if(e.length==0)return n;if(n.length==0)return e;let t=0;return e.endsWith("/")&&t++,n.startsWith("/")&&t++,t==2?e+n.substring(1):t==1?e+n:e+"/"+n}function aD(e){let n=e.match(/#|\?|$/),t=n&&n.index||e.length,r=t-(e[t-1]==="/"?1:0);return e.slice(0,r)+e.slice(t)}function Yt(e){return e&&e[0]!=="?"?"?"+e:e}var Qe=(()=>{class e{historyGo(t){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>v(Md),providedIn:"root"})}}return e})(),mD=new A(""),Md=(()=>{class e extends Qe{constructor(t,r){super(),this._platformLocation=t,this._removeListenerFns=[],this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??v(Ie).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return Ed(this._baseHref,t)}path(t=!1){let r=this._platformLocation.pathname+Yt(this._platformLocation.search),i=this._platformLocation.hash;return i&&t?`${r}${i}`:r}pushState(t,r,i,o){let s=this.prepareExternalUrl(i+Yt(o));this._platformLocation.pushState(t,r,s)}replaceState(t,r,i,o){let s=this.prepareExternalUrl(i+Yt(o));this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static{this.\u0275fac=function(r){return new(r||e)(N(wd),N(mD,8))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),_d=(()=>{class e extends Qe{constructor(t,r){super(),this._platformLocation=t,this._baseHref="",this._removeListenerFns=[],r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}path(t=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(t){let r=Ed(this._baseHref,t);return r.length>0?"#"+r:r}pushState(t,r,i,o){let s=this.prepareExternalUrl(i+Yt(o));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.pushState(t,r,s)}replaceState(t,r,i,o){let s=this.prepareExternalUrl(i+Yt(o));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static{this.\u0275fac=function(r){return new(r||e)(N(wd),N(mD,8))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),Dt=(()=>{class e{constructor(t){this._subject=new ee,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=t;let r=this._locationStrategy.getBaseHref();this._basePath=AS(aD(cD(r))),this._locationStrategy.onPopState(i=>{this._subject.emit({url:this.path(!0),pop:!0,state:i.state,type:i.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(t=!1){return this.normalize(this._locationStrategy.path(t))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(t,r=""){return this.path()==this.normalize(t+Yt(r))}normalize(t){return e.stripTrailingSlash(xS(this._basePath,cD(t)))}prepareExternalUrl(t){return t&&t[0]!=="/"&&(t="/"+t),this._locationStrategy.prepareExternalUrl(t)}go(t,r="",i=null){this._locationStrategy.pushState(i,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+Yt(r)),i)}replaceState(t,r="",i=null){this._locationStrategy.replaceState(i,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+Yt(r)),i)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(t=0){this._locationStrategy.historyGo?.(t)}onUrlChange(t){return this._urlChangeListeners.push(t),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(t);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(t="",r){this._urlChangeListeners.forEach(i=>i(t,r))}subscribe(t,r,i){return this._subject.subscribe({next:t,error:r,complete:i})}static{this.normalizeQueryParams=Yt}static{this.joinWithSlash=Ed}static{this.stripTrailingSlash=aD}static{this.\u0275fac=function(r){return new(r||e)(N(Qe))}}static{this.\u0275prov=x({token:e,factory:()=>TS(),providedIn:"root"})}}return e})();function TS(){return new Dt(N(Qe))}function xS(e,n){if(!e||!n.startsWith(e))return n;let t=n.substring(e.length);return t===""||["/",";","?","#"].includes(t[0])?t:n}function cD(e){return e.replace(/\/index.html$/,"")}function AS(e){if(new RegExp("^(https?:)?//").test(e)){let[,t]=e.split(/\/\/[^\/]+/);return t}return e}var je=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(je||{}),ne=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(ne||{}),Ze=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Ze||{}),En={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function RS(e){return ot(e)[ge.LocaleId]}function NS(e,n,t){let r=ot(e),i=[r[ge.DayPeriodsFormat],r[ge.DayPeriodsStandalone]],o=st(i,n);return st(o,t)}function OS(e,n,t){let r=ot(e),i=[r[ge.DaysFormat],r[ge.DaysStandalone]],o=st(i,n);return st(o,t)}function kS(e,n,t){let r=ot(e),i=[r[ge.MonthsFormat],r[ge.MonthsStandalone]],o=st(i,n);return st(o,t)}function PS(e,n){let r=ot(e)[ge.Eras];return st(r,n)}function Da(e,n){let t=ot(e);return st(t[ge.DateFormat],n)}function Ia(e,n){let t=ot(e);return st(t[ge.TimeFormat],n)}function Ca(e,n){let r=ot(e)[ge.DateTimeFormat];return st(r,n)}function xa(e,n){let t=ot(e),r=t[ge.NumberSymbols][n];if(typeof r>"u"){if(n===En.CurrencyDecimal)return t[ge.NumberSymbols][En.Decimal];if(n===En.CurrencyGroup)return t[ge.NumberSymbols][En.Group]}return r}function vD(e){if(!e[ge.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[ge.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function FS(e){let n=ot(e);return vD(n),(n[ge.ExtraData][2]||[]).map(r=>typeof r=="string"?pd(r):[pd(r[0]),pd(r[1])])}function jS(e,n,t){let r=ot(e);vD(r);let i=[r[ge.ExtraData][0],r[ge.ExtraData][1]],o=st(i,n)||[];return st(o,t)||[]}function st(e,n){for(let t=n;t>-1;t--)if(typeof e[t]<"u")return e[t];throw new Error("Locale data API: locale data undefined")}function pd(e){let[n,t]=e.split(":");return{hours:+n,minutes:+t}}var LS=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,ba={},VS=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/,Kt=function(e){return e[e.Short=0]="Short",e[e.ShortGMT=1]="ShortGMT",e[e.Long=2]="Long",e[e.Extended=3]="Extended",e}(Kt||{}),X=function(e){return e[e.FullYear=0]="FullYear",e[e.Month=1]="Month",e[e.Date=2]="Date",e[e.Hours=3]="Hours",e[e.Minutes=4]="Minutes",e[e.Seconds=5]="Seconds",e[e.FractionalSeconds=6]="FractionalSeconds",e[e.Day=7]="Day",e}(X||{}),K=function(e){return e[e.DayPeriods=0]="DayPeriods",e[e.Days=1]="Days",e[e.Months=2]="Months",e[e.Eras=3]="Eras",e}(K||{});function BS(e,n,t,r){let i=QS(e);n=Qt(t,n)||n;let s=[],a;for(;n;)if(a=VS.exec(n),a){s=s.concat(a.slice(1));let d=s.pop();if(!d)break;n=d}else{s.push(n);break}let c=i.getTimezoneOffset();r&&(c=DD(r,c),i=ZS(i,r,!0));let l="";return s.forEach(d=>{let f=WS(d);l+=f?f(i,t,c):d==="''"?"'":d.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),l}function Ta(e,n,t){let r=new Date(0);return r.setFullYear(e,n,t),r.setHours(0,0,0),r}function Qt(e,n){let t=RS(e);if(ba[t]??={},ba[t][n])return ba[t][n];let r="";switch(n){case"shortDate":r=Da(e,Ze.Short);break;case"mediumDate":r=Da(e,Ze.Medium);break;case"longDate":r=Da(e,Ze.Long);break;case"fullDate":r=Da(e,Ze.Full);break;case"shortTime":r=Ia(e,Ze.Short);break;case"mediumTime":r=Ia(e,Ze.Medium);break;case"longTime":r=Ia(e,Ze.Long);break;case"fullTime":r=Ia(e,Ze.Full);break;case"short":let i=Qt(e,"shortTime"),o=Qt(e,"shortDate");r=wa(Ca(e,Ze.Short),[i,o]);break;case"medium":let s=Qt(e,"mediumTime"),a=Qt(e,"mediumDate");r=wa(Ca(e,Ze.Medium),[s,a]);break;case"long":let c=Qt(e,"longTime"),l=Qt(e,"longDate");r=wa(Ca(e,Ze.Long),[c,l]);break;case"full":let d=Qt(e,"fullTime"),f=Qt(e,"fullDate");r=wa(Ca(e,Ze.Full),[d,f]);break}return r&&(ba[t][n]=r),r}function wa(e,n){return n&&(e=e.replace(/\{([^}]+)}/g,function(t,r){return n!=null&&r in n?n[r]:t})),e}function yt(e,n,t="-",r,i){let o="";(e<0||i&&e<=0)&&(i?e=-e+1:(e=-e,o=t));let s=String(e);for(;s.length<n;)s="0"+s;return r&&(s=s.slice(s.length-n)),o+s}function US(e,n){return yt(e,3).substring(0,n)}function De(e,n,t=0,r=!1,i=!1){return function(o,s){let a=$S(e,o);if((t>0||a>-t)&&(a+=t),e===X.Hours)a===0&&t===-12&&(a=12);else if(e===X.FractionalSeconds)return US(a,n);let c=xa(s,En.MinusSign);return yt(a,n,c,r,i)}}function $S(e,n){switch(e){case X.FullYear:return n.getFullYear();case X.Month:return n.getMonth();case X.Date:return n.getDate();case X.Hours:return n.getHours();case X.Minutes:return n.getMinutes();case X.Seconds:return n.getSeconds();case X.FractionalSeconds:return n.getMilliseconds();case X.Day:return n.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function se(e,n,t=je.Format,r=!1){return function(i,o){return HS(i,o,e,n,t,r)}}function HS(e,n,t,r,i,o){switch(t){case K.Months:return kS(n,i,r)[e.getMonth()];case K.Days:return OS(n,i,r)[e.getDay()];case K.DayPeriods:let s=e.getHours(),a=e.getMinutes();if(o){let l=FS(n),d=jS(n,i,r),f=l.findIndex(p=>{if(Array.isArray(p)){let[h,y]=p,M=s>=h.hours&&a>=h.minutes,T=s<y.hours||s===y.hours&&a<y.minutes;if(h.hours<y.hours){if(M&&T)return!0}else if(M||T)return!0}else if(p.hours===s&&p.minutes===a)return!0;return!1});if(f!==-1)return d[f]}return NS(n,i,r)[s<12?0:1];case K.Eras:return PS(n,r)[e.getFullYear()<=0?0:1];default:let c=t;throw new Error(`unexpected translation type ${c}`)}}function Ea(e){return function(n,t,r){let i=-1*r,o=xa(t,En.MinusSign),s=i>0?Math.floor(i/60):Math.ceil(i/60);switch(e){case Kt.Short:return(i>=0?"+":"")+yt(s,2,o)+yt(Math.abs(i%60),2,o);case Kt.ShortGMT:return"GMT"+(i>=0?"+":"")+yt(s,1,o);case Kt.Long:return"GMT"+(i>=0?"+":"")+yt(s,2,o)+":"+yt(Math.abs(i%60),2,o);case Kt.Extended:return r===0?"Z":(i>=0?"+":"")+yt(s,2,o)+":"+yt(Math.abs(i%60),2,o);default:throw new Error(`Unknown zone width "${e}"`)}}}var zS=0,_a=4;function GS(e){let n=Ta(e,zS,1).getDay();return Ta(e,0,1+(n<=_a?_a:_a+7)-n)}function yD(e){let n=e.getDay(),t=n===0?-3:_a-n;return Ta(e.getFullYear(),e.getMonth(),e.getDate()+t)}function gd(e,n=!1){return function(t,r){let i;if(n){let o=new Date(t.getFullYear(),t.getMonth(),1).getDay()-1,s=t.getDate();i=1+Math.floor((s+o)/7)}else{let o=yD(t),s=GS(o.getFullYear()),a=o.getTime()-s.getTime();i=1+Math.round(a/6048e5)}return yt(i,e,xa(r,En.MinusSign))}}function Ma(e,n=!1){return function(t,r){let o=yD(t).getFullYear();return yt(o,e,xa(r,En.MinusSign),n)}}var md={};function WS(e){if(md[e])return md[e];let n;switch(e){case"G":case"GG":case"GGG":n=se(K.Eras,ne.Abbreviated);break;case"GGGG":n=se(K.Eras,ne.Wide);break;case"GGGGG":n=se(K.Eras,ne.Narrow);break;case"y":n=De(X.FullYear,1,0,!1,!0);break;case"yy":n=De(X.FullYear,2,0,!0,!0);break;case"yyy":n=De(X.FullYear,3,0,!1,!0);break;case"yyyy":n=De(X.FullYear,4,0,!1,!0);break;case"Y":n=Ma(1);break;case"YY":n=Ma(2,!0);break;case"YYY":n=Ma(3);break;case"YYYY":n=Ma(4);break;case"M":case"L":n=De(X.Month,1,1);break;case"MM":case"LL":n=De(X.Month,2,1);break;case"MMM":n=se(K.Months,ne.Abbreviated);break;case"MMMM":n=se(K.Months,ne.Wide);break;case"MMMMM":n=se(K.Months,ne.Narrow);break;case"LLL":n=se(K.Months,ne.Abbreviated,je.Standalone);break;case"LLLL":n=se(K.Months,ne.Wide,je.Standalone);break;case"LLLLL":n=se(K.Months,ne.Narrow,je.Standalone);break;case"w":n=gd(1);break;case"ww":n=gd(2);break;case"W":n=gd(1,!0);break;case"d":n=De(X.Date,1);break;case"dd":n=De(X.Date,2);break;case"c":case"cc":n=De(X.Day,1);break;case"ccc":n=se(K.Days,ne.Abbreviated,je.Standalone);break;case"cccc":n=se(K.Days,ne.Wide,je.Standalone);break;case"ccccc":n=se(K.Days,ne.Narrow,je.Standalone);break;case"cccccc":n=se(K.Days,ne.Short,je.Standalone);break;case"E":case"EE":case"EEE":n=se(K.Days,ne.Abbreviated);break;case"EEEE":n=se(K.Days,ne.Wide);break;case"EEEEE":n=se(K.Days,ne.Narrow);break;case"EEEEEE":n=se(K.Days,ne.Short);break;case"a":case"aa":case"aaa":n=se(K.DayPeriods,ne.Abbreviated);break;case"aaaa":n=se(K.DayPeriods,ne.Wide);break;case"aaaaa":n=se(K.DayPeriods,ne.Narrow);break;case"b":case"bb":case"bbb":n=se(K.DayPeriods,ne.Abbreviated,je.Standalone,!0);break;case"bbbb":n=se(K.DayPeriods,ne.Wide,je.Standalone,!0);break;case"bbbbb":n=se(K.DayPeriods,ne.Narrow,je.Standalone,!0);break;case"B":case"BB":case"BBB":n=se(K.DayPeriods,ne.Abbreviated,je.Format,!0);break;case"BBBB":n=se(K.DayPeriods,ne.Wide,je.Format,!0);break;case"BBBBB":n=se(K.DayPeriods,ne.Narrow,je.Format,!0);break;case"h":n=De(X.Hours,1,-12);break;case"hh":n=De(X.Hours,2,-12);break;case"H":n=De(X.Hours,1);break;case"HH":n=De(X.Hours,2);break;case"m":n=De(X.Minutes,1);break;case"mm":n=De(X.Minutes,2);break;case"s":n=De(X.Seconds,1);break;case"ss":n=De(X.Seconds,2);break;case"S":n=De(X.FractionalSeconds,1);break;case"SS":n=De(X.FractionalSeconds,2);break;case"SSS":n=De(X.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":n=Ea(Kt.Short);break;case"ZZZZZ":n=Ea(Kt.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":n=Ea(Kt.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":n=Ea(Kt.Long);break;default:return null}return md[e]=n,n}function DD(e,n){e=e.replace(/:/g,"");let t=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(t)?n:t}function qS(e,n){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+n),e}function ZS(e,n,t){let r=t?-1:1,i=e.getTimezoneOffset(),o=DD(n,i);return qS(e,r*(o-i))}function QS(e){if(lD(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[i,o=1,s=1]=e.split("-").map(a=>+a);return Ta(i,o-1,s)}let t=parseFloat(e);if(!isNaN(e-t))return new Date(t);let r;if(r=e.match(LS))return YS(r)}let n=new Date(e);if(!lD(n))throw new Error(`Unable to convert "${e}" into a date`);return n}function YS(e){let n=new Date(0),t=0,r=0,i=e[8]?n.setUTCFullYear:n.setFullYear,o=e[8]?n.setUTCHours:n.setHours;e[9]&&(t=Number(e[9]+e[10]),r=Number(e[9]+e[11])),i.call(n,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-t,a=Number(e[5]||0)-r,c=Number(e[6]||0),l=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return o.call(n,s,a,c,l),n}function lD(e){return e instanceof Date&&!isNaN(e.valueOf())}function Aa(e,n){n=encodeURIComponent(n);for(let t of e.split(";")){let r=t.indexOf("="),[i,o]=r==-1?[t,""]:[t.slice(0,r),t.slice(r+1)];if(i.trim()===n)return decodeURIComponent(o)}return null}var vd=/\s+/,uD=[],v2=(()=>{class e{constructor(t,r){this._ngEl=t,this._renderer=r,this.initialClasses=uD,this.stateMap=new Map}set klass(t){this.initialClasses=t!=null?t.trim().split(vd):uD}set ngClass(t){this.rawClass=typeof t=="string"?t.trim().split(vd):t}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let t=this.rawClass;if(Array.isArray(t)||t instanceof Set)for(let r of t)this._updateState(r,!0);else if(t!=null)for(let r of Object.keys(t))this._updateState(r,!!t[r]);this._applyStateDiff()}_updateState(t,r){let i=this.stateMap.get(t);i!==void 0?(i.enabled!==r&&(i.changed=!0,i.enabled=r),i.touched=!0):this.stateMap.set(t,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let t of this.stateMap){let r=t[0],i=t[1];i.changed?(this._toggleClass(r,i.enabled),i.changed=!1):i.touched||(i.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),i.touched=!1}}_toggleClass(t,r){t=t.trim(),t.length>0&&t.split(vd).forEach(i=>{r?this._renderer.addClass(this._ngEl.nativeElement,i):this._renderer.removeClass(this._ngEl.nativeElement,i)})}static{this.\u0275fac=function(r){return new(r||e)(u(m),u(In))}}static{this.\u0275dir=H({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"},standalone:!0})}}return e})();var yd=class{constructor(n,t,r,i){this.$implicit=n,this.ngForOf=t,this.index=r,this.count=i}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},y2=(()=>{class e{set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}constructor(t,r,i){this._viewContainer=t,this._template=r,this._differs=i,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let t=this._ngForOf;if(!this._differ&&t)if(0)try{}catch{}else this._differ=this._differs.find(t).create(this.ngForTrackBy)}if(this._differ){let t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){let r=this._viewContainer;t.forEachOperation((i,o,s)=>{if(i.previousIndex==null)r.createEmbeddedView(this._template,new yd(i.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(o===null?void 0:o);else if(o!==null){let a=r.get(o);r.move(a,s),dD(a,i)}});for(let i=0,o=r.length;i<o;i++){let a=r.get(i).context;a.index=i,a.count=o,a.ngForOf=this._ngForOf}t.forEachIdentityChange(i=>{let o=r.get(i.currentIndex);dD(o,i)})}static ngTemplateContextGuard(t,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(u(qe),u(St),u(hd))}}static{this.\u0275dir=H({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return e})();function dD(e,n){e.context.$implicit=n.item}var Ra=(()=>{class e{constructor(t,r){this._viewContainer=t,this._context=new Dd,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=r}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){fD("ngIfThen",t),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){fD("ngIfElse",t),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(t,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(u(qe),u(St))}}static{this.\u0275dir=H({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return e})(),Dd=class{constructor(){this.$implicit=null,this.ngIf=null}};function fD(e,n){if(!!!(!n||n.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${Fe(n)}'.`)}var Sd=(()=>{class e{constructor(t){this._viewContainerRef=t,this._viewRef=null,this.ngTemplateOutletContext=null,this.ngTemplateOutlet=null,this.ngTemplateOutletInjector=null}ngOnChanges(t){if(this._shouldRecreateView(t)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let i=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,i,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(t){return!!t.ngTemplateOutlet||!!t.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(t,r,i)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,i):!1,get:(t,r,i)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,i)}})}static{this.\u0275fac=function(r){return new(r||e)(u(qe))}}static{this.\u0275dir=H({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},standalone:!0,features:[nt]})}}return e})();function KS(e,n){return new R(2100,!1)}var XS="mediumDate",JS=new A(""),eT=new A(""),D2=(()=>{class e{constructor(t,r,i){this.locale=t,this.defaultTimezone=r,this.defaultOptions=i}transform(t,r,i,o){if(t==null||t===""||t!==t)return null;try{let s=r??this.defaultOptions?.dateFormat??XS,a=i??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return BS(t,s,o||this.locale,a)}catch(s){throw KS(e,s.message)}}static{this.\u0275fac=function(r){return new(r||e)(u(va,16),u(JS,24),u(eT,24))}}static{this.\u0275pipe=Pm({name:"date",type:e,pure:!0,standalone:!0})}}return e})();var Td=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=gt({type:e})}static{this.\u0275inj=pt({})}}return e})(),xd="browser",tT="server";function nT(e){return e===xd}function Na(e){return e===tT}var ID=(()=>{class e{static{this.\u0275prov=x({token:e,providedIn:"root",factory:()=>nT(v(Tt))?new Id(v(Ie),window):new Cd})}}return e})(),Id=class{constructor(n,t){this.document=n,this.window=t,this.offset=()=>[0,0]}setOffset(n){Array.isArray(n)?this.offset=()=>n:this.offset=n}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(n){this.window.scrollTo(n[0],n[1])}scrollToAnchor(n){let t=rT(this.document,n);t&&(this.scrollToElement(t),t.focus())}setHistoryScrollRestoration(n){this.window.history.scrollRestoration=n}scrollToElement(n){let t=n.getBoundingClientRect(),r=t.left+this.window.pageXOffset,i=t.top+this.window.pageYOffset,o=this.offset();this.window.scrollTo(r-o[0],i-o[1])}};function rT(e,n){let t=e.getElementById(n)||e.getElementsByName(n)[0];if(t)return t;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),i=r.currentNode;for(;i;){let o=i.shadowRoot;if(o){let s=o.getElementById(n)||o.querySelector(`[name="${n}"]`);if(s)return s}i=r.nextNode()}}return null}var Cd=class{setOffset(n){}getScrollPosition(){return[0,0]}scrollToPosition(n){}scrollToAnchor(n){}setHistoryScrollRestoration(n){}},Zr=class{};var Vi=class{},Pa=class{},Jt=class e{constructor(n){this.normalizedNames=new Map,this.lazyUpdate=null,n?typeof n=="string"?this.lazyInit=()=>{this.headers=new Map,n.split(`
`).forEach(t=>{let r=t.indexOf(":");if(r>0){let i=t.slice(0,r),o=i.toLowerCase(),s=t.slice(r+1).trim();this.maybeSetNormalizedName(i,o),this.headers.has(o)?this.headers.get(o).push(s):this.headers.set(o,[s])}})}:typeof Headers<"u"&&n instanceof Headers?(this.headers=new Map,n.forEach((t,r)=>{this.setHeaderEntries(r,t)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(n).forEach(([t,r])=>{this.setHeaderEntries(t,r)})}:this.headers=new Map}has(n){return this.init(),this.headers.has(n.toLowerCase())}get(n){this.init();let t=this.headers.get(n.toLowerCase());return t&&t.length>0?t[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(n){return this.init(),this.headers.get(n.toLowerCase())||null}append(n,t){return this.clone({name:n,value:t,op:"a"})}set(n,t){return this.clone({name:n,value:t,op:"s"})}delete(n,t){return this.clone({name:n,value:t,op:"d"})}maybeSetNormalizedName(n,t){this.normalizedNames.has(t)||this.normalizedNames.set(t,n)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(n=>this.applyUpdate(n)),this.lazyUpdate=null))}copyFrom(n){n.init(),Array.from(n.headers.keys()).forEach(t=>{this.headers.set(t,n.headers.get(t)),this.normalizedNames.set(t,n.normalizedNames.get(t))})}clone(n){let t=new e;return t.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,t.lazyUpdate=(this.lazyUpdate||[]).concat([n]),t}applyUpdate(n){let t=n.name.toLowerCase();switch(n.op){case"a":case"s":let r=n.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(n.name,t);let i=(n.op==="a"?this.headers.get(t):void 0)||[];i.push(...r),this.headers.set(t,i);break;case"d":let o=n.value;if(!o)this.headers.delete(t),this.normalizedNames.delete(t);else{let s=this.headers.get(t);if(!s)return;s=s.filter(a=>o.indexOf(a)===-1),s.length===0?(this.headers.delete(t),this.normalizedNames.delete(t)):this.headers.set(t,s)}break}}setHeaderEntries(n,t){let r=(Array.isArray(t)?t:[t]).map(o=>o.toString()),i=n.toLowerCase();this.headers.set(i,r),this.maybeSetNormalizedName(n,i)}forEach(n){this.init(),Array.from(this.normalizedNames.keys()).forEach(t=>n(this.normalizedNames.get(t),this.headers.get(t)))}};var Rd=class{encodeKey(n){return CD(n)}encodeValue(n){return CD(n)}decodeKey(n){return decodeURIComponent(n)}decodeValue(n){return decodeURIComponent(n)}};function iT(e,n){let t=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(i=>{let o=i.indexOf("="),[s,a]=o==-1?[n.decodeKey(i),""]:[n.decodeKey(i.slice(0,o)),n.decodeValue(i.slice(o+1))],c=t.get(s)||[];c.push(a),t.set(s,c)}),t}var oT=/%(\d[a-f0-9])/gi,sT={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function CD(e){return encodeURIComponent(e).replace(oT,(n,t)=>sT[t]??n)}function ka(e){return`${e}`}var _n=class e{constructor(n={}){if(this.updates=null,this.cloneFrom=null,this.encoder=n.encoder||new Rd,n.fromString){if(n.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=iT(n.fromString,this.encoder)}else n.fromObject?(this.map=new Map,Object.keys(n.fromObject).forEach(t=>{let r=n.fromObject[t],i=Array.isArray(r)?r.map(ka):[ka(r)];this.map.set(t,i)})):this.map=null}has(n){return this.init(),this.map.has(n)}get(n){this.init();let t=this.map.get(n);return t?t[0]:null}getAll(n){return this.init(),this.map.get(n)||null}keys(){return this.init(),Array.from(this.map.keys())}append(n,t){return this.clone({param:n,value:t,op:"a"})}appendAll(n){let t=[];return Object.keys(n).forEach(r=>{let i=n[r];Array.isArray(i)?i.forEach(o=>{t.push({param:r,value:o,op:"a"})}):t.push({param:r,value:i,op:"a"})}),this.clone(t)}set(n,t){return this.clone({param:n,value:t,op:"s"})}delete(n,t){return this.clone({param:n,value:t,op:"d"})}toString(){return this.init(),this.keys().map(n=>{let t=this.encoder.encodeKey(n);return this.map.get(n).map(r=>t+"="+this.encoder.encodeValue(r)).join("&")}).filter(n=>n!=="").join("&")}clone(n){let t=new e({encoder:this.encoder});return t.cloneFrom=this.cloneFrom||this,t.updates=(this.updates||[]).concat(n),t}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(n=>this.map.set(n,this.cloneFrom.map.get(n))),this.updates.forEach(n=>{switch(n.op){case"a":case"s":let t=(n.op==="a"?this.map.get(n.param):void 0)||[];t.push(ka(n.value)),this.map.set(n.param,t);break;case"d":if(n.value!==void 0){let r=this.map.get(n.param)||[],i=r.indexOf(ka(n.value));i!==-1&&r.splice(i,1),r.length>0?this.map.set(n.param,r):this.map.delete(n.param)}else{this.map.delete(n.param);break}}}),this.cloneFrom=this.updates=null)}};var Nd=class{constructor(){this.map=new Map}set(n,t){return this.map.set(n,t),this}get(n){return this.map.has(n)||this.map.set(n,n.defaultValue()),this.map.get(n)}delete(n){return this.map.delete(n),this}has(n){return this.map.has(n)}keys(){return this.map.keys()}};function aT(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function bD(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function wD(e){return typeof Blob<"u"&&e instanceof Blob}function ED(e){return typeof FormData<"u"&&e instanceof FormData}function cT(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Li=class e{constructor(n,t,r,i){this.url=t,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=n.toUpperCase();let o;if(aT(this.method)||i?(this.body=r!==void 0?r:null,o=i):o=r,o&&(this.reportProgress=!!o.reportProgress,this.withCredentials=!!o.withCredentials,o.responseType&&(this.responseType=o.responseType),o.headers&&(this.headers=o.headers),o.context&&(this.context=o.context),o.params&&(this.params=o.params),this.transferCache=o.transferCache),this.headers??=new Jt,this.context??=new Nd,!this.params)this.params=new _n,this.urlWithParams=t;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=t;else{let a=t.indexOf("?"),c=a===-1?"?":a<t.length-1?"&":"";this.urlWithParams=t+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||bD(this.body)||wD(this.body)||ED(this.body)||cT(this.body)?this.body:this.body instanceof _n?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||ED(this.body)?null:wD(this.body)?this.body.type||null:bD(this.body)?null:typeof this.body=="string"?"text/plain":this.body instanceof _n?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?"application/json":null}clone(n={}){let t=n.method||this.method,r=n.url||this.url,i=n.responseType||this.responseType,o=n.transferCache??this.transferCache,s=n.body!==void 0?n.body:this.body,a=n.withCredentials??this.withCredentials,c=n.reportProgress??this.reportProgress,l=n.headers||this.headers,d=n.params||this.params,f=n.context??this.context;return n.setHeaders!==void 0&&(l=Object.keys(n.setHeaders).reduce((p,h)=>p.set(h,n.setHeaders[h]),l)),n.setParams&&(d=Object.keys(n.setParams).reduce((p,h)=>p.set(h,n.setParams[h]),d)),new e(t,r,s,{params:d,headers:l,context:f,reportProgress:c,responseType:i,withCredentials:a,transferCache:o})}},Sn=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Sn||{}),Bi=class{constructor(n,t=200,r="OK"){this.headers=n.headers||new Jt,this.status=n.status!==void 0?n.status:t,this.statusText=n.statusText||r,this.url=n.url||null,this.ok=this.status>=200&&this.status<300}},Fa=class e extends Bi{constructor(n={}){super(n),this.type=Sn.ResponseHeader}clone(n={}){return new e({headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},Ui=class e extends Bi{constructor(n={}){super(n),this.type=Sn.Response,this.body=n.body!==void 0?n.body:null}clone(n={}){return new e({body:n.body!==void 0?n.body:this.body,headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},Mn=class extends Bi{constructor(n){super(n,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${n.url||"(unknown url)"}`:this.message=`Http failure response for ${n.url||"(unknown url)"}: ${n.status} ${n.statusText}`,this.error=n.error||null}},xD=200,lT=204;function Ad(e,n){return{body:n,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var uT=(()=>{class e{constructor(t){this.handler=t}request(t,r,i={}){let o;if(t instanceof Li)o=t;else{let c;i.headers instanceof Jt?c=i.headers:c=new Jt(i.headers);let l;i.params&&(i.params instanceof _n?l=i.params:l=new _n({fromObject:i.params})),o=new Li(t,r,i.body!==void 0?i.body:null,{headers:c,context:i.context,params:l,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache})}let s=O(o).pipe(Pt(c=>this.handler.handle(c)));if(t instanceof Li||i.observe==="events")return s;let a=s.pipe(Ae(c=>c instanceof Ui));switch(i.observe||"body"){case"body":switch(o.responseType){case"arraybuffer":return a.pipe(B(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return c.body}));case"blob":return a.pipe(B(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new Error("Response is not a Blob.");return c.body}));case"text":return a.pipe(B(c=>{if(c.body!==null&&typeof c.body!="string")throw new Error("Response is not a string.");return c.body}));case"json":default:return a.pipe(B(c=>c.body))}case"response":return a;default:throw new Error(`Unreachable: unhandled observe type ${i.observe}}`)}}delete(t,r={}){return this.request("DELETE",t,r)}get(t,r={}){return this.request("GET",t,r)}head(t,r={}){return this.request("HEAD",t,r)}jsonp(t,r){return this.request("JSONP",t,{params:new _n().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(t,r={}){return this.request("OPTIONS",t,r)}patch(t,r,i={}){return this.request("PATCH",t,Ad(i,r))}post(t,r,i={}){return this.request("POST",t,Ad(i,r))}put(t,r,i={}){return this.request("PUT",t,Ad(i,r))}static{this.\u0275fac=function(r){return new(r||e)(N(Vi))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),dT=/^\)\]\}',?\n/,fT="X-Request-URL";function MD(e){if(e.url)return e.url;let n=fT.toLocaleLowerCase();return e.headers.get(n)}var hT=(()=>{class e{constructor(){this.fetchImpl=v(Od,{optional:!0})?.fetch??((...t)=>globalThis.fetch(...t)),this.ngZone=v(g)}handle(t){return new W(r=>{let i=new AbortController;return this.doRequest(t,i.signal,r).then(kd,o=>r.error(new Mn({error:o}))),()=>i.abort()})}doRequest(t,r,i){return ve(this,null,function*(){let o=this.createRequestInit(t),s;try{let h=this.ngZone.runOutsideAngular(()=>this.fetchImpl(t.urlWithParams,b({signal:r},o)));pT(h),i.next({type:Sn.Sent}),s=yield h}catch(h){i.error(new Mn({error:h,status:h.status??0,statusText:h.statusText,url:t.urlWithParams,headers:h.headers}));return}let a=new Jt(s.headers),c=s.statusText,l=MD(s)??t.urlWithParams,d=s.status,f=null;if(t.reportProgress&&i.next(new Fa({headers:a,status:d,statusText:c,url:l})),s.body){let h=s.headers.get("content-length"),y=[],M=s.body.getReader(),T=0,F,re,Z=typeof Zone<"u"&&Zone.current;yield this.ngZone.runOutsideAngular(()=>ve(this,null,function*(){for(;;){let{done:Te,value:Ce}=yield M.read();if(Te)break;if(y.push(Ce),T+=Ce.length,t.reportProgress){re=t.responseType==="text"?(re??"")+(F??=new TextDecoder).decode(Ce,{stream:!0}):void 0;let Ot=()=>i.next({type:Sn.DownloadProgress,total:h?+h:void 0,loaded:T,partialText:re});Z?Z.run(Ot):Ot()}}}));let me=this.concatChunks(y,T);try{let Te=s.headers.get("Content-Type")??"";f=this.parseBody(t,me,Te)}catch(Te){i.error(new Mn({error:Te,headers:new Jt(s.headers),status:s.status,statusText:s.statusText,url:MD(s)??t.urlWithParams}));return}}d===0&&(d=f?xD:0),d>=200&&d<300?(i.next(new Ui({body:f,headers:a,status:d,statusText:c,url:l})),i.complete()):i.error(new Mn({error:f,headers:a,status:d,statusText:c,url:l}))})}parseBody(t,r,i){switch(t.responseType){case"json":let o=new TextDecoder().decode(r).replace(dT,"");return o===""?null:JSON.parse(o);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:i});case"arraybuffer":return r.buffer}}createRequestInit(t){let r={},i=t.withCredentials?"include":void 0;if(t.headers.forEach((o,s)=>r[o]=s.join(",")),t.headers.has("Accept")||(r.Accept="application/json, text/plain, */*"),!t.headers.has("Content-Type")){let o=t.detectContentTypeHeader();o!==null&&(r["Content-Type"]=o)}return{body:t.serializeBody(),method:t.method,headers:r,credentials:i}}concatChunks(t,r){let i=new Uint8Array(r),o=0;for(let s of t)i.set(s,o),o+=s.length;return i}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),Od=class{};function kd(){}function pT(e){e.then(kd,kd)}function AD(e,n){return n(e)}function gT(e,n){return(t,r)=>n.intercept(t,{handle:i=>e(i,r)})}function mT(e,n,t){return(r,i)=>tt(t,()=>n(r,o=>e(o,i)))}var vT=new A(""),Pd=new A(""),yT=new A(""),RD=new A("",{providedIn:"root",factory:()=>!0});function DT(){let e=null;return(n,t)=>{e===null&&(e=(v(vT,{optional:!0})??[]).reduceRight(gT,AD));let r=v(Wt);if(v(RD)){let o=r.add();return e(n,t).pipe(ln(()=>r.remove(o)))}else return e(n,t)}}var _D=(()=>{class e extends Vi{constructor(t,r){super(),this.backend=t,this.injector=r,this.chain=null,this.pendingTasks=v(Wt),this.contributeToStability=v(RD)}handle(t){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Pd),...this.injector.get(yT,[])]));this.chain=r.reduceRight((i,o)=>mT(i,o,this.injector),AD)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(t,i=>this.backend.handle(i)).pipe(ln(()=>this.pendingTasks.remove(r)))}else return this.chain(t,r=>this.backend.handle(r))}static{this.\u0275fac=function(r){return new(r||e)(N(Pa),N(ue))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})();var IT=/^\)\]\}',?\n/;function CT(e){return"responseURL"in e&&e.responseURL?e.responseURL:/^X-Request-URL:/m.test(e.getAllResponseHeaders())?e.getResponseHeader("X-Request-URL"):null}var SD=(()=>{class e{constructor(t){this.xhrFactory=t}handle(t){if(t.method==="JSONP")throw new R(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?ce(r.\u0275loadImpl()):O(null)).pipe(we(()=>new W(o=>{let s=r.build();if(s.open(t.method,t.urlWithParams),t.withCredentials&&(s.withCredentials=!0),t.headers.forEach((M,T)=>s.setRequestHeader(M,T.join(","))),t.headers.has("Accept")||s.setRequestHeader("Accept","application/json, text/plain, */*"),!t.headers.has("Content-Type")){let M=t.detectContentTypeHeader();M!==null&&s.setRequestHeader("Content-Type",M)}if(t.responseType){let M=t.responseType.toLowerCase();s.responseType=M!=="json"?M:"text"}let a=t.serializeBody(),c=null,l=()=>{if(c!==null)return c;let M=s.statusText||"OK",T=new Jt(s.getAllResponseHeaders()),F=CT(s)||t.url;return c=new Fa({headers:T,status:s.status,statusText:M,url:F}),c},d=()=>{let{headers:M,status:T,statusText:F,url:re}=l(),Z=null;T!==lT&&(Z=typeof s.response>"u"?s.responseText:s.response),T===0&&(T=Z?xD:0);let me=T>=200&&T<300;if(t.responseType==="json"&&typeof Z=="string"){let Te=Z;Z=Z.replace(IT,"");try{Z=Z!==""?JSON.parse(Z):null}catch(Ce){Z=Te,me&&(me=!1,Z={error:Ce,text:Z})}}me?(o.next(new Ui({body:Z,headers:M,status:T,statusText:F,url:re||void 0})),o.complete()):o.error(new Mn({error:Z,headers:M,status:T,statusText:F,url:re||void 0}))},f=M=>{let{url:T}=l(),F=new Mn({error:M,status:s.status||0,statusText:s.statusText||"Unknown Error",url:T||void 0});o.error(F)},p=!1,h=M=>{p||(o.next(l()),p=!0);let T={type:Sn.DownloadProgress,loaded:M.loaded};M.lengthComputable&&(T.total=M.total),t.responseType==="text"&&s.responseText&&(T.partialText=s.responseText),o.next(T)},y=M=>{let T={type:Sn.UploadProgress,loaded:M.loaded};M.lengthComputable&&(T.total=M.total),o.next(T)};return s.addEventListener("load",d),s.addEventListener("error",f),s.addEventListener("timeout",f),s.addEventListener("abort",f),t.reportProgress&&(s.addEventListener("progress",h),a!==null&&s.upload&&s.upload.addEventListener("progress",y)),s.send(a),o.next({type:Sn.Sent}),()=>{s.removeEventListener("error",f),s.removeEventListener("abort",f),s.removeEventListener("load",d),s.removeEventListener("timeout",f),t.reportProgress&&(s.removeEventListener("progress",h),a!==null&&s.upload&&s.upload.removeEventListener("progress",y)),s.readyState!==s.DONE&&s.abort()}})))}static{this.\u0275fac=function(r){return new(r||e)(N(Zr))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),ND=new A(""),bT="XSRF-TOKEN",wT=new A("",{providedIn:"root",factory:()=>bT}),ET="X-XSRF-TOKEN",MT=new A("",{providedIn:"root",factory:()=>ET}),ja=class{},_T=(()=>{class e{constructor(t,r,i){this.doc=t,this.platform=r,this.cookieName=i,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if(this.platform==="server")return null;let t=this.doc.cookie||"";return t!==this.lastCookieString&&(this.parseCount++,this.lastToken=Aa(t,this.cookieName),this.lastCookieString=t),this.lastToken}static{this.\u0275fac=function(r){return new(r||e)(N(Ie),N(Tt),N(wT))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})();function ST(e,n){let t=e.url.toLowerCase();if(!v(ND)||e.method==="GET"||e.method==="HEAD"||t.startsWith("http://")||t.startsWith("https://"))return n(e);let r=v(ja).getToken(),i=v(MT);return r!=null&&!e.headers.has(i)&&(e=e.clone({headers:e.headers.set(i,r)})),n(e)}var OD=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(OD||{});function TT(e,n){return{\u0275kind:e,\u0275providers:n}}function N2(...e){let n=[uT,SD,_D,{provide:Vi,useExisting:_D},{provide:Pa,useFactory:()=>v(hT,{optional:!0})??v(SD)},{provide:Pd,useValue:ST,multi:!0},{provide:ND,useValue:!0},{provide:ja,useClass:_T}];for(let t of e)n.push(...t.\u0275providers);return xi(n)}var TD=new A("");function O2(){return TT(OD.LegacyInterceptors,[{provide:TD,useFactory:DT},{provide:Pd,useExisting:TD,multi:!0}])}var Ld=class extends Sa{constructor(){super(...arguments),this.supportsDOMEvents=!0}},Vd=class e extends Ld{static makeCurrent(){pD(new e)}onAndCancel(n,t,r){return n.addEventListener(t,r),()=>{n.removeEventListener(t,r)}}dispatchEvent(n,t){n.dispatchEvent(t)}remove(n){n.remove()}createElement(n,t){return t=t||this.getDefaultDocument(),t.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,t){return t==="window"?window:t==="document"?n:t==="body"?n.body:null}getBaseHref(n){let t=xT();return t==null?null:AT(t)}resetBaseElement(){$i=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return Aa(document.cookie,n)}},$i=null;function xT(){return $i=$i||document.querySelector("base"),$i?$i.getAttribute("href"):null}function AT(e){return new URL(e,document.baseURI).pathname}var RT=(()=>{class e{build(){return new XMLHttpRequest}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),Bd=new A(""),jD=(()=>{class e{constructor(t,r){this._zone=r,this._eventNameToPlugin=new Map,t.forEach(i=>{i.manager=this}),this._plugins=t.slice().reverse()}addEventListener(t,r,i){return this._findPluginFor(r).addEventListener(t,r,i)}getZone(){return this._zone}_findPluginFor(t){let r=this._eventNameToPlugin.get(t);if(r)return r;if(r=this._plugins.find(o=>o.supports(t)),!r)throw new R(5101,!1);return this._eventNameToPlugin.set(t,r),r}static{this.\u0275fac=function(r){return new(r||e)(N(Bd),N(g))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),La=class{constructor(n){this._doc=n}},Fd="ng-app-id",LD=(()=>{class e{constructor(t,r,i,o={}){this.doc=t,this.appId=r,this.nonce=i,this.platformId=o,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=Na(o),this.resetHostNodes()}addStyles(t){for(let r of t)this.changeUsageCount(r,1)===1&&this.onStyleAdded(r)}removeStyles(t){for(let r of t)this.changeUsageCount(r,-1)<=0&&this.onStyleRemoved(r)}ngOnDestroy(){let t=this.styleNodesInDOM;t&&(t.forEach(r=>r.remove()),t.clear());for(let r of this.getAllStyles())this.onStyleRemoved(r);this.resetHostNodes()}addHost(t){this.hostNodes.add(t);for(let r of this.getAllStyles())this.addStyleToHost(t,r)}removeHost(t){this.hostNodes.delete(t)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(t){for(let r of this.hostNodes)this.addStyleToHost(r,t)}onStyleRemoved(t){let r=this.styleRef;r.get(t)?.elements?.forEach(i=>i.remove()),r.delete(t)}collectServerRenderedStyles(){let t=this.doc.head?.querySelectorAll(`style[${Fd}="${this.appId}"]`);if(t?.length){let r=new Map;return t.forEach(i=>{i.textContent!=null&&r.set(i.textContent,i)}),r}return null}changeUsageCount(t,r){let i=this.styleRef;if(i.has(t)){let o=i.get(t);return o.usage+=r,o.usage}return i.set(t,{usage:r,elements:[]}),r}getStyleElement(t,r){let i=this.styleNodesInDOM,o=i?.get(r);if(o?.parentNode===t)return i.delete(r),o.removeAttribute(Fd),o;{let s=this.doc.createElement("style");return this.nonce&&s.setAttribute("nonce",this.nonce),s.textContent=r,this.platformIsServer&&s.setAttribute(Fd,this.appId),t.appendChild(s),s}}addStyleToHost(t,r){let i=this.getStyleElement(t,r),o=this.styleRef,s=o.get(r)?.elements;s?s.push(i):o.set(r,{elements:[i],usage:1})}resetHostNodes(){let t=this.hostNodes;t.clear(),t.add(this.doc.head)}static{this.\u0275fac=function(r){return new(r||e)(N(Ie),N(Hu),N(Gu,8),N(Tt))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),jd={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},$d=/%COMP%/g,VD="%COMP%",NT=`_nghost-${VD}`,OT=`_ngcontent-${VD}`,kT=!0,PT=new A("",{providedIn:"root",factory:()=>kT});function FT(e){return OT.replace($d,e)}function jT(e){return NT.replace($d,e)}function BD(e,n){return n.map(t=>t.replace($d,e))}var kD=(()=>{class e{constructor(t,r,i,o,s,a,c,l=null){this.eventManager=t,this.sharedStylesHost=r,this.appId=i,this.removeStylesOnCompDestroy=o,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=l,this.rendererByCompId=new Map,this.platformIsServer=Na(a),this.defaultRenderer=new Hi(t,s,c,this.platformIsServer)}createRenderer(t,r){if(!t||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===Et.ShadowDom&&(r=V(b({},r),{encapsulation:Et.Emulated}));let i=this.getOrCreateRenderer(t,r);return i instanceof Va?i.applyToHost(t):i instanceof zi&&i.applyStyles(),i}getOrCreateRenderer(t,r){let i=this.rendererByCompId,o=i.get(r.id);if(!o){let s=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,d=this.removeStylesOnCompDestroy,f=this.platformIsServer;switch(r.encapsulation){case Et.Emulated:o=new Va(c,l,r,this.appId,d,s,a,f);break;case Et.ShadowDom:return new Ud(c,l,t,r,s,a,this.nonce,f);default:o=new zi(c,l,r,d,s,a,f);break}i.set(r.id,o)}return o}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(r){return new(r||e)(N(jD),N(LD),N(Hu),N(PT),N(Ie),N(Tt),N(g),N(Gu))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),Hi=class{constructor(n,t,r,i){this.eventManager=n,this.doc=t,this.ngZone=r,this.platformIsServer=i,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(n,t){return t?this.doc.createElementNS(jd[t]||t,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,t){(PD(n)?n.content:n).appendChild(t)}insertBefore(n,t,r){n&&(PD(n)?n.content:n).insertBefore(t,r)}removeChild(n,t){t.remove()}selectRootElement(n,t){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new R(-5104,!1);return t||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,t,r,i){if(i){t=i+":"+t;let o=jd[i];o?n.setAttributeNS(o,t,r):n.setAttribute(t,r)}else n.setAttribute(t,r)}removeAttribute(n,t,r){if(r){let i=jd[r];i?n.removeAttributeNS(i,t):n.removeAttribute(`${r}:${t}`)}else n.removeAttribute(t)}addClass(n,t){n.classList.add(t)}removeClass(n,t){n.classList.remove(t)}setStyle(n,t,r,i){i&($t.DashCase|$t.Important)?n.style.setProperty(t,r,i&$t.Important?"important":""):n.style[t]=r}removeStyle(n,t,r){r&$t.DashCase?n.style.removeProperty(t):n.style[t]=""}setProperty(n,t,r){n!=null&&(n[t]=r)}setValue(n,t){n.nodeValue=t}listen(n,t,r){if(typeof n=="string"&&(n=Xt().getGlobalEventTarget(this.doc,n),!n))throw new Error(`Unsupported event target ${n} for event ${t}`);return this.eventManager.addEventListener(n,t,this.decoratePreventDefault(r))}decoratePreventDefault(n){return t=>{if(t==="__ngUnwrap__")return n;(this.platformIsServer?this.ngZone.runGuarded(()=>n(t)):n(t))===!1&&t.preventDefault()}}};function PD(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var Ud=class extends Hi{constructor(n,t,r,i,o,s,a,c){super(n,o,s,c),this.sharedStylesHost=t,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=BD(i.id,i.styles);for(let d of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=d,this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,t){return super.appendChild(this.nodeOrShadowRoot(n),t)}insertBefore(n,t,r){return super.insertBefore(this.nodeOrShadowRoot(n),t,r)}removeChild(n,t){return super.removeChild(null,t)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},zi=class extends Hi{constructor(n,t,r,i,o,s,a,c){super(n,o,s,a),this.sharedStylesHost=t,this.removeStylesOnCompDestroy=i,this.styles=c?BD(c,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}},Va=class extends zi{constructor(n,t,r,i,o,s,a,c){let l=i+"-"+r.id;super(n,t,r,o,s,a,c,l),this.contentAttr=FT(l),this.hostAttr=jT(l)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,t){let r=super.createElement(n,t);return super.setAttribute(r,this.contentAttr,""),r}},LT=(()=>{class e extends La{constructor(t){super(t)}supports(t){return!0}addEventListener(t,r,i){return t.addEventListener(r,i,!1),()=>this.removeEventListener(t,r,i)}removeEventListener(t,r,i){return t.removeEventListener(r,i)}static{this.\u0275fac=function(r){return new(r||e)(N(Ie))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),FD=["alt","control","meta","shift"],VT={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},BT={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},UT=(()=>{class e extends La{constructor(t){super(t)}supports(t){return e.parseEventName(t)!=null}addEventListener(t,r,i){let o=e.parseEventName(r),s=e.eventCallback(o.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Xt().onAndCancel(t,o.domEventName,s))}static parseEventName(t){let r=t.toLowerCase().split("."),i=r.shift();if(r.length===0||!(i==="keydown"||i==="keyup"))return null;let o=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),FD.forEach(l=>{let d=r.indexOf(l);d>-1&&(r.splice(d,1),s+=l+".")}),s+=o,r.length!=0||o.length===0)return null;let c={};return c.domEventName=i,c.fullKey=s,c}static matchEventFullKeyCode(t,r){let i=VT[t.key]||t.key,o="";return r.indexOf("code.")>-1&&(i=t.code,o="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),FD.forEach(s=>{if(s!==i){let a=BT[s];a(t)&&(o+=s+".")}}),o+=i,o===r)}static eventCallback(t,r,i){return o=>{e.matchEventFullKeyCode(o,t)&&i.runGuarded(()=>r(o))}}static _normalizeKey(t){return t==="esc"?"escape":t}static{this.\u0275fac=function(r){return new(r||e)(N(Ie))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})();function iU(e,n){return oD(b({rootComponent:e},$T(n)))}function $T(e){return{appProviders:[...qT,...e?.providers??[]],platformProviders:WT}}function HT(){Vd.makeCurrent()}function zT(){return new Ut}function GT(){return Fv(document),document}var WT=[{provide:Tt,useValue:xd},{provide:zu,useValue:HT,multi:!0},{provide:Ie,useFactory:GT,deps:[]}];var qT=[{provide:Gs,useValue:"root"},{provide:Ut,useFactory:zT,deps:[]},{provide:Bd,useClass:LT,multi:!0,deps:[Ie,g,Tt]},{provide:Bd,useClass:UT,multi:!0,deps:[Ie]},kD,LD,jD,{provide:Vr,useExisting:kD},{provide:Zr,useClass:RT,deps:[]},[]];var UD=(()=>{class e{constructor(t){this._doc=t}getTitle(){return this._doc.title}setTitle(t){this._doc.title=t||""}static{this.\u0275fac=function(r){return new(r||e)(N(Ie))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var j="primary",oo=Symbol("RouteTitle"),qd=class{constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t[0]:t}return null}getAll(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t:[t]}return[]}get keys(){return Object.keys(this.params)}};function ei(e){return new qd(e)}function QT(e,n,t){let r=t.path.split("/");if(r.length>e.length||t.pathMatch==="full"&&(n.hasChildren()||r.length<e.length))return null;let i={};for(let o=0;o<r.length;o++){let s=r[o],a=e[o];if(s[0]===":")i[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:i}}function YT(e,n){if(e.length!==n.length)return!1;for(let t=0;t<e.length;++t)if(!Rt(e[t],n[t]))return!1;return!0}function Rt(e,n){let t=e?Zd(e):void 0,r=n?Zd(n):void 0;if(!t||!r||t.length!=r.length)return!1;let i;for(let o=0;o<t.length;o++)if(i=t[o],!XD(e[i],n[i]))return!1;return!0}function Zd(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function XD(e,n){if(Array.isArray(e)&&Array.isArray(n)){if(e.length!==n.length)return!1;let t=[...e].sort(),r=[...n].sort();return t.every((i,o)=>r[o]===i)}else return e===n}function JD(e){return e.length>0?e[e.length-1]:null}function An(e){return Wc(e)?e:rr(e)?ce(Promise.resolve(e)):O(e)}var KT={exact:tI,subset:nI},eI={exact:XT,subset:JT,ignored:()=>!0};function $D(e,n,t){return KT[t.paths](e.root,n.root,t.matrixParams)&&eI[t.queryParams](e.queryParams,n.queryParams)&&!(t.fragment==="exact"&&e.fragment!==n.fragment)}function XT(e,n){return Rt(e,n)}function tI(e,n,t){if(!sr(e.segments,n.segments)||!$a(e.segments,n.segments,t)||e.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!e.children[r]||!tI(e.children[r],n.children[r],t))return!1;return!0}function JT(e,n){return Object.keys(n).length<=Object.keys(e).length&&Object.keys(n).every(t=>XD(e[t],n[t]))}function nI(e,n,t){return rI(e,n,n.segments,t)}function rI(e,n,t,r){if(e.segments.length>t.length){let i=e.segments.slice(0,t.length);return!(!sr(i,t)||n.hasChildren()||!$a(i,t,r))}else if(e.segments.length===t.length){if(!sr(e.segments,t)||!$a(e.segments,t,r))return!1;for(let i in n.children)if(!e.children[i]||!nI(e.children[i],n.children[i],r))return!1;return!0}else{let i=t.slice(0,e.segments.length),o=t.slice(e.segments.length);return!sr(e.segments,i)||!$a(e.segments,i,r)||!e.children[j]?!1:rI(e.children[j],n,o,r)}}function $a(e,n,t){return n.every((r,i)=>eI[t](e[i].parameters,r.parameters))}var tn=class{constructor(n=new J([],{}),t={},r=null){this.root=n,this.queryParams=t,this.fragment=r}get queryParamMap(){return this._queryParamMap??=ei(this.queryParams),this._queryParamMap}toString(){return nx.serialize(this)}},J=class{constructor(n,t){this.segments=n,this.children=t,this.parent=null,Object.values(t).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Ha(this)}},or=class{constructor(n,t){this.path=n,this.parameters=t}get parameterMap(){return this._parameterMap??=ei(this.parameters),this._parameterMap}toString(){return oI(this)}};function ex(e,n){return sr(e,n)&&e.every((t,r)=>Rt(t.parameters,n[r].parameters))}function sr(e,n){return e.length!==n.length?!1:e.every((t,r)=>t.path===n[r].path)}function tx(e,n){let t=[];return Object.entries(e.children).forEach(([r,i])=>{r===j&&(t=t.concat(n(i,r)))}),Object.entries(e.children).forEach(([r,i])=>{r!==j&&(t=t.concat(n(i,r)))}),t}var cr=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>new ti,providedIn:"root"})}}return e})(),ti=class{parse(n){let t=new Yd(n);return new tn(t.parseRootSegment(),t.parseQueryParams(),t.parseFragment())}serialize(n){let t=`/${Gi(n.root,!0)}`,r=ox(n.queryParams),i=typeof n.fragment=="string"?`#${rx(n.fragment)}`:"";return`${t}${r}${i}`}},nx=new ti;function Ha(e){return e.segments.map(n=>oI(n)).join("/")}function Gi(e,n){if(!e.hasChildren())return Ha(e);if(n){let t=e.children[j]?Gi(e.children[j],!1):"",r=[];return Object.entries(e.children).forEach(([i,o])=>{i!==j&&r.push(`${i}:${Gi(o,!1)}`)}),r.length>0?`${t}(${r.join("//")})`:t}else{let t=tx(e,(r,i)=>i===j?[Gi(e.children[j],!1)]:[`${i}:${Gi(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[j]!=null?`${Ha(e)}/${t[0]}`:`${Ha(e)}/(${t.join("//")})`}}function iI(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Ba(e){return iI(e).replace(/%3B/gi,";")}function rx(e){return encodeURI(e)}function Qd(e){return iI(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function za(e){return decodeURIComponent(e)}function HD(e){return za(e.replace(/\+/g,"%20"))}function oI(e){return`${Qd(e.path)}${ix(e.parameters)}`}function ix(e){return Object.entries(e).map(([n,t])=>`;${Qd(n)}=${Qd(t)}`).join("")}function ox(e){let n=Object.entries(e).map(([t,r])=>Array.isArray(r)?r.map(i=>`${Ba(t)}=${Ba(i)}`).join("&"):`${Ba(t)}=${Ba(r)}`).filter(t=>t);return n.length?`?${n.join("&")}`:""}var sx=/^[^\/()?;#]+/;function Hd(e){let n=e.match(sx);return n?n[0]:""}var ax=/^[^\/()?;=#]+/;function cx(e){let n=e.match(ax);return n?n[0]:""}var lx=/^[^=?&#]+/;function ux(e){let n=e.match(lx);return n?n[0]:""}var dx=/^[^&#]+/;function fx(e){let n=e.match(dx);return n?n[0]:""}var Yd=class{constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new J([],{}):new J([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let t={};this.peekStartsWith("/(")&&(this.capture("/"),t=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(t).length>0)&&(r[j]=new J(n,t)),r}parseSegment(){let n=Hd(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new R(4009,!1);return this.capture(n),new or(za(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let t=cx(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let i=Hd(this.remaining);i&&(r=i,this.capture(r))}n[za(t)]=za(r)}parseQueryParam(n){let t=ux(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let s=fx(this.remaining);s&&(r=s,this.capture(r))}let i=HD(t),o=HD(r);if(n.hasOwnProperty(i)){let s=n[i];Array.isArray(s)||(s=[s],n[i]=s),s.push(o)}else n[i]=o}parseParens(n){let t={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Hd(this.remaining),i=this.remaining[r.length];if(i!=="/"&&i!==")"&&i!==";")throw new R(4010,!1);let o;r.indexOf(":")>-1?(o=r.slice(0,r.indexOf(":")),this.capture(o),this.capture(":")):n&&(o=j);let s=this.parseChildren();t[o]=Object.keys(s).length===1?s[j]:new J([],s),this.consumeOptional("//")}return t}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new R(4011,!1)}};function sI(e){return e.segments.length>0?new J([],{[j]:e}):e}function aI(e){let n={};for(let[r,i]of Object.entries(e.children)){let o=aI(i);if(r===j&&o.segments.length===0&&o.hasChildren())for(let[s,a]of Object.entries(o.children))n[s]=a;else(o.segments.length>0||o.hasChildren())&&(n[r]=o)}let t=new J(e.segments,n);return hx(t)}function hx(e){if(e.numberOfChildren===1&&e.children[j]){let n=e.children[j];return new J(e.segments.concat(n.segments),n.children)}return e}function ar(e){return e instanceof tn}function px(e,n,t=null,r=null){let i=cI(e);return lI(i,n,t,r)}function cI(e){let n;function t(o){let s={};for(let c of o.children){let l=t(c);s[c.outlet]=l}let a=new J(o.url,s);return o===e&&(n=a),a}let r=t(e.root),i=sI(r);return n??i}function lI(e,n,t,r){let i=e;for(;i.parent;)i=i.parent;if(n.length===0)return zd(i,i,i,t,r);let o=gx(n);if(o.toRoot())return zd(i,i,new J([],{}),t,r);let s=mx(o,i,e),a=s.processChildren?Zi(s.segmentGroup,s.index,o.commands):dI(s.segmentGroup,s.index,o.commands);return zd(i,s.segmentGroup,a,t,r)}function Ga(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Ki(e){return typeof e=="object"&&e!=null&&e.outlets}function zd(e,n,t,r,i){let o={};r&&Object.entries(r).forEach(([c,l])=>{o[c]=Array.isArray(l)?l.map(d=>`${d}`):`${l}`});let s;e===n?s=t:s=uI(e,n,t);let a=sI(aI(s));return new tn(a,o,i)}function uI(e,n,t){let r={};return Object.entries(e.children).forEach(([i,o])=>{o===n?r[i]=t:r[i]=uI(o,n,t)}),new J(e.segments,r)}var Wa=class{constructor(n,t,r){if(this.isAbsolute=n,this.numberOfDoubleDots=t,this.commands=r,n&&r.length>0&&Ga(r[0]))throw new R(4003,!1);let i=r.find(Ki);if(i&&i!==JD(r))throw new R(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function gx(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new Wa(!0,0,e);let n=0,t=!1,r=e.reduce((i,o,s)=>{if(typeof o=="object"&&o!=null){if(o.outlets){let a={};return Object.entries(o.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...i,{outlets:a}]}if(o.segmentPath)return[...i,o.segmentPath]}return typeof o!="string"?[...i,o]:s===0?(o.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?t=!0:a===".."?n++:a!=""&&i.push(a))}),i):[...i,o]},[]);return new Wa(t,n,r)}var Kr=class{constructor(n,t,r){this.segmentGroup=n,this.processChildren=t,this.index=r}};function mx(e,n,t){if(e.isAbsolute)return new Kr(n,!0,0);if(!t)return new Kr(n,!1,NaN);if(t.parent===null)return new Kr(t,!0,0);let r=Ga(e.commands[0])?0:1,i=t.segments.length-1+r;return vx(t,i,e.numberOfDoubleDots)}function vx(e,n,t){let r=e,i=n,o=t;for(;o>i;){if(o-=i,r=r.parent,!r)throw new R(4005,!1);i=r.segments.length}return new Kr(r,!1,i-o)}function yx(e){return Ki(e[0])?e[0].outlets:{[j]:e}}function dI(e,n,t){if(e??=new J([],{}),e.segments.length===0&&e.hasChildren())return Zi(e,n,t);let r=Dx(e,n,t),i=t.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let o=new J(e.segments.slice(0,r.pathIndex),{});return o.children[j]=new J(e.segments.slice(r.pathIndex),e.children),Zi(o,0,i)}else return r.match&&i.length===0?new J(e.segments,{}):r.match&&!e.hasChildren()?Kd(e,n,t):r.match?Zi(e,0,i):Kd(e,n,t)}function Zi(e,n,t){if(t.length===0)return new J(e.segments,{});{let r=yx(t),i={};if(Object.keys(r).some(o=>o!==j)&&e.children[j]&&e.numberOfChildren===1&&e.children[j].segments.length===0){let o=Zi(e.children[j],n,t);return new J(e.segments,o.children)}return Object.entries(r).forEach(([o,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(i[o]=dI(e.children[o],n,s))}),Object.entries(e.children).forEach(([o,s])=>{r[o]===void 0&&(i[o]=s)}),new J(e.segments,i)}}function Dx(e,n,t){let r=0,i=n,o={match:!1,pathIndex:0,commandIndex:0};for(;i<e.segments.length;){if(r>=t.length)return o;let s=e.segments[i],a=t[r];if(Ki(a))break;let c=`${a}`,l=r<t.length-1?t[r+1]:null;if(i>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!GD(c,l,s))return o;r+=2}else{if(!GD(c,{},s))return o;r++}i++}return{match:!0,pathIndex:i,commandIndex:r}}function Kd(e,n,t){let r=e.segments.slice(0,n),i=0;for(;i<t.length;){let o=t[i];if(Ki(o)){let c=Ix(o.outlets);return new J(r,c)}if(i===0&&Ga(t[0])){let c=e.segments[n];r.push(new or(c.path,zD(t[0]))),i++;continue}let s=Ki(o)?o.outlets[j]:`${o}`,a=i<t.length-1?t[i+1]:null;s&&a&&Ga(a)?(r.push(new or(s,zD(a))),i+=2):(r.push(new or(s,{})),i++)}return new J(r,{})}function Ix(e){let n={};return Object.entries(e).forEach(([t,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[t]=Kd(new J([],{}),0,r))}),n}function zD(e){let n={};return Object.entries(e).forEach(([t,r])=>n[t]=`${r}`),n}function GD(e,n,t){return e==t.path&&Rt(n,t.parameters)}var Qi="imperative",Se=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(Se||{}),at=class{constructor(n,t){this.id=n,this.url=t}},Tn=class extends at{constructor(n,t,r="imperative",i=null){super(n,t),this.type=Se.NavigationStart,this.navigationTrigger=r,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Nt=class extends at{constructor(n,t,r){super(n,t),this.urlAfterRedirects=r,this.type=Se.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Ke=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(Ke||{}),qa=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(qa||{}),en=class extends at{constructor(n,t,r,i){super(n,t),this.reason=r,this.code=i,this.type=Se.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},xn=class extends at{constructor(n,t,r,i){super(n,t),this.reason=r,this.code=i,this.type=Se.NavigationSkipped}},Xi=class extends at{constructor(n,t,r,i){super(n,t),this.error=r,this.target=i,this.type=Se.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Za=class extends at{constructor(n,t,r,i){super(n,t),this.urlAfterRedirects=r,this.state=i,this.type=Se.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Xd=class extends at{constructor(n,t,r,i){super(n,t),this.urlAfterRedirects=r,this.state=i,this.type=Se.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Jd=class extends at{constructor(n,t,r,i,o){super(n,t),this.urlAfterRedirects=r,this.state=i,this.shouldActivate=o,this.type=Se.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},ef=class extends at{constructor(n,t,r,i){super(n,t),this.urlAfterRedirects=r,this.state=i,this.type=Se.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},tf=class extends at{constructor(n,t,r,i){super(n,t),this.urlAfterRedirects=r,this.state=i,this.type=Se.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},nf=class{constructor(n){this.route=n,this.type=Se.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},rf=class{constructor(n){this.route=n,this.type=Se.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},of=class{constructor(n){this.snapshot=n,this.type=Se.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},sf=class{constructor(n){this.snapshot=n,this.type=Se.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},af=class{constructor(n){this.snapshot=n,this.type=Se.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},cf=class{constructor(n){this.snapshot=n,this.type=Se.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Qa=class{constructor(n,t,r){this.routerEvent=n,this.position=t,this.anchor=r,this.type=Se.Scroll}toString(){let n=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${n}')`}},Ji=class{},ni=class{constructor(n,t){this.url=n,this.navigationBehaviorOptions=t}};function Cx(e,n){return e.providers&&!e._injector&&(e._injector=ua(e.providers,n,`Route: ${e.path}`)),e._injector??n}function It(e){return e.outlet||j}function bx(e,n){let t=e.filter(r=>It(r)===n);return t.push(...e.filter(r=>It(r)!==n)),t}function so(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let n=e.parent;n;n=n.parent){let t=n.routeConfig;if(t?._loadedInjector)return t._loadedInjector;if(t?._injector)return t._injector}return null}var lf=class{get injector(){return so(this.route?.snapshot)??this.rootInjector}set injector(n){}constructor(n){this.rootInjector=n,this.outlet=null,this.route=null,this.children=new Rn(this.rootInjector),this.attachRef=null}},Rn=(()=>{class e{constructor(t){this.rootInjector=t,this.contexts=new Map}onChildOutletCreated(t,r){let i=this.getOrCreateContext(t);i.outlet=r,this.contexts.set(t,i)}onChildOutletDestroyed(t){let r=this.getContext(t);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let r=this.getContext(t);return r||(r=new lf(this.rootInjector),this.contexts.set(t,r)),r}getContext(t){return this.contexts.get(t)||null}static{this.\u0275fac=function(r){return new(r||e)(N(ue))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Ya=class{constructor(n){this._root=n}get root(){return this._root.value}parent(n){let t=this.pathFromRoot(n);return t.length>1?t[t.length-2]:null}children(n){let t=uf(n,this._root);return t?t.children.map(r=>r.value):[]}firstChild(n){let t=uf(n,this._root);return t&&t.children.length>0?t.children[0].value:null}siblings(n){let t=df(n,this._root);return t.length<2?[]:t[t.length-2].children.map(i=>i.value).filter(i=>i!==n)}pathFromRoot(n){return df(n,this._root).map(t=>t.value)}};function uf(e,n){if(e===n.value)return n;for(let t of n.children){let r=uf(e,t);if(r)return r}return null}function df(e,n){if(e===n.value)return[n];for(let t of n.children){let r=df(e,t);if(r.length)return r.unshift(n),r}return[]}var Ye=class{constructor(n,t){this.value=n,this.children=t}toString(){return`TreeNode(${this.value})`}};function Yr(e){let n={};return e&&e.children.forEach(t=>n[t.value.outlet]=t),n}var Ka=class extends Ya{constructor(n,t){super(n),this.snapshot=t,If(this,n)}toString(){return this.snapshot.toString()}};function fI(e){let n=wx(e),t=new ye([new or("",{})]),r=new ye({}),i=new ye({}),o=new ye({}),s=new ye(""),a=new Le(t,r,o,s,i,j,e,n.root);return a.snapshot=n.root,new Ka(new Ye(a,[]),n)}function wx(e){let n={},t={},r={},i="",o=new Xr([],n,r,i,t,j,e,null,{});return new Ja("",new Ye(o,[]))}var Le=class{constructor(n,t,r,i,o,s,a,c){this.urlSubject=n,this.paramsSubject=t,this.queryParamsSubject=r,this.fragmentSubject=i,this.dataSubject=o,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(B(l=>l[oo]))??O(void 0),this.url=n,this.params=t,this.queryParams=r,this.fragment=i,this.data=o}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(B(n=>ei(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(B(n=>ei(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Xa(e,n,t="emptyOnly"){let r,{routeConfig:i}=e;return n!==null&&(t==="always"||i?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:b(b({},n.params),e.params),data:b(b({},n.data),e.data),resolve:b(b(b(b({},e.data),n.data),i?.data),e._resolvedData)}:r={params:b({},e.params),data:b({},e.data),resolve:b(b({},e.data),e._resolvedData??{})},i&&pI(i)&&(r.resolve[oo]=i.title),r}var Xr=class{get title(){return this.data?.[oo]}constructor(n,t,r,i,o,s,a,c,l){this.url=n,this.params=t,this.queryParams=r,this.fragment=i,this.data=o,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=ei(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=ei(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),t=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${t}')`}},Ja=class extends Ya{constructor(n,t){super(t),this.url=n,If(this,t)}toString(){return hI(this._root)}};function If(e,n){n.value._routerState=e,n.children.forEach(t=>If(e,t))}function hI(e){let n=e.children.length>0?` { ${e.children.map(hI).join(", ")} } `:"";return`${e.value}${n}`}function Gd(e){if(e.snapshot){let n=e.snapshot,t=e._futureSnapshot;e.snapshot=t,Rt(n.queryParams,t.queryParams)||e.queryParamsSubject.next(t.queryParams),n.fragment!==t.fragment&&e.fragmentSubject.next(t.fragment),Rt(n.params,t.params)||e.paramsSubject.next(t.params),YT(n.url,t.url)||e.urlSubject.next(t.url),Rt(n.data,t.data)||e.dataSubject.next(t.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function ff(e,n){let t=Rt(e.params,n.params)&&ex(e.url,n.url),r=!e.parent!=!n.parent;return t&&!r&&(!e.parent||ff(e.parent,n.parent))}function pI(e){return typeof e.title=="string"||e.title===null}var Ex=(()=>{class e{constructor(){this.activated=null,this._activatedRoute=null,this.name=j,this.activateEvents=new ee,this.deactivateEvents=new ee,this.attachEvents=new ee,this.detachEvents=new ee,this.parentContexts=v(Rn),this.location=v(qe),this.changeDetector=v(D),this.inputBinder=v(rc,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(t){if(t.name){let{firstChange:r,previousValue:i}=t.name;if(r)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(t){return this.parentContexts.getContext(t)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let t=this.parentContexts.getContext(this.name);t?.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new R(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new R(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new R(4012,!1);this.location.detach();let t=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(t.instance),t}attach(t,r){this.activated=t,this._activatedRoute=r,this.location.insert(t.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(t.instance)}deactivate(){if(this.activated){let t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new R(4013,!1);this._activatedRoute=t;let i=this.location,s=t.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new hf(t,a,i.injector);this.activated=i.createComponent(s,{index:i.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=H({type:e,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[nt]})}}return e})(),hf=class e{__ngOutletInjector(n){return new e(this.route,this.childContexts,n)}constructor(n,t,r){this.route=n,this.childContexts=t,this.parent=r}get(n,t){return n===Le?this.route:n===Rn?this.childContexts:this.parent.get(n,t)}},rc=new A(""),WD=(()=>{class e{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,i=Bn([r.queryParams,r.params,r.data]).pipe(we(([o,s,a],c)=>(a=b(b(b({},o),s),a),c===0?O(a):Promise.resolve(a)))).subscribe(o=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=ya(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,o[a])});this.outletDataSubscriptions.set(t,i)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})();function Mx(e,n,t){let r=eo(e,n._root,t?t._root:void 0);return new Ka(r,n)}function eo(e,n,t){if(t&&e.shouldReuseRoute(n.value,t.value.snapshot)){let r=t.value;r._futureSnapshot=n.value;let i=_x(e,n,t);return new Ye(r,i)}else{if(e.shouldAttach(n.value)){let o=e.retrieve(n.value);if(o!==null){let s=o.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>eo(e,a)),s}}let r=Sx(n.value),i=n.children.map(o=>eo(e,o));return new Ye(r,i)}}function _x(e,n,t){return n.children.map(r=>{for(let i of t.children)if(e.shouldReuseRoute(r.value,i.value.snapshot))return eo(e,r,i);return eo(e,r)})}function Sx(e){return new Le(new ye(e.url),new ye(e.params),new ye(e.queryParams),new ye(e.fragment),new ye(e.data),e.outlet,e.component,e)}var to=class{constructor(n,t){this.redirectTo=n,this.navigationBehaviorOptions=t}},gI="ngNavigationCancelingError";function ec(e,n){let{redirectTo:t,navigationBehaviorOptions:r}=ar(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,i=mI(!1,Ke.Redirect);return i.url=t,i.navigationBehaviorOptions=r,i}function mI(e,n){let t=new Error(`NavigationCancelingError: ${e||""}`);return t[gI]=!0,t.cancellationCode=n,t}function Tx(e){return vI(e)&&ar(e.url)}function vI(e){return!!e&&e[gI]}var xx=(e,n,t,r)=>B(i=>(new pf(n,i.targetRouterState,i.currentRouterState,t,r).activate(e),i)),pf=class{constructor(n,t,r,i,o){this.routeReuseStrategy=n,this.futureState=t,this.currState=r,this.forwardEvent=i,this.inputBindingEnabled=o}activate(n){let t=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(t,r,n),Gd(this.futureState.root),this.activateChildRoutes(t,r,n)}deactivateChildRoutes(n,t,r){let i=Yr(t);n.children.forEach(o=>{let s=o.value.outlet;this.deactivateRoutes(o,i[s],r),delete i[s]}),Object.values(i).forEach(o=>{this.deactivateRouteAndItsChildren(o,r)})}deactivateRoutes(n,t,r){let i=n.value,o=t?t.value:null;if(i===o)if(i.component){let s=r.getContext(i.outlet);s&&this.deactivateChildRoutes(n,t,s.children)}else this.deactivateChildRoutes(n,t,r);else o&&this.deactivateRouteAndItsChildren(t,r)}deactivateRouteAndItsChildren(n,t){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,t):this.deactivateRouteAndOutlet(n,t)}detachAndStoreRouteSubtree(n,t){let r=t.getContext(n.value.outlet),i=r&&n.value.component?r.children:t,o=Yr(n);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,t){let r=t.getContext(n.value.outlet),i=r&&n.value.component?r.children:t,o=Yr(n);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,t,r){let i=Yr(t);n.children.forEach(o=>{this.activateRoutes(o,i[o.value.outlet],r),this.forwardEvent(new cf(o.value.snapshot))}),n.children.length&&this.forwardEvent(new sf(n.value.snapshot))}activateRoutes(n,t,r){let i=n.value,o=t?t.value:null;if(Gd(i),i===o)if(i.component){let s=r.getOrCreateContext(i.outlet);this.activateChildRoutes(n,t,s.children)}else this.activateChildRoutes(n,t,r);else if(i.component){let s=r.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let a=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Gd(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=i,s.outlet&&s.outlet.activateWith(i,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}},tc=class{constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},Jr=class{constructor(n,t){this.component=n,this.route=t}};function Ax(e,n,t){let r=e._root,i=n?n._root:null;return Wi(r,i,t,[r.value])}function Rx(e){let n=e.routeConfig?e.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:e,guards:n}}function ii(e,n){let t=Symbol(),r=n.get(e,t);return r===t?typeof e=="function"&&!Cm(e)?e:n.get(e):r}function Wi(e,n,t,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=Yr(n);return e.children.forEach(s=>{Nx(s,o[s.value.outlet],t,r.concat([s.value]),i),delete o[s.value.outlet]}),Object.entries(o).forEach(([s,a])=>Yi(a,t.getContext(s),i)),i}function Nx(e,n,t,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=e.value,s=n?n.value:null,a=t?t.getContext(e.value.outlet):null;if(s&&o.routeConfig===s.routeConfig){let c=Ox(s,o,o.routeConfig.runGuardsAndResolvers);c?i.canActivateChecks.push(new tc(r)):(o.data=s.data,o._resolvedData=s._resolvedData),o.component?Wi(e,n,a?a.children:null,r,i):Wi(e,n,t,r,i),c&&a&&a.outlet&&a.outlet.isActivated&&i.canDeactivateChecks.push(new Jr(a.outlet.component,s))}else s&&Yi(n,a,i),i.canActivateChecks.push(new tc(r)),o.component?Wi(e,null,a?a.children:null,r,i):Wi(e,null,t,r,i);return i}function Ox(e,n,t){if(typeof t=="function")return t(e,n);switch(t){case"pathParamsChange":return!sr(e.url,n.url);case"pathParamsOrQueryParamsChange":return!sr(e.url,n.url)||!Rt(e.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!ff(e,n)||!Rt(e.queryParams,n.queryParams);case"paramsChange":default:return!ff(e,n)}}function Yi(e,n,t){let r=Yr(e),i=e.value;Object.entries(r).forEach(([o,s])=>{i.component?n?Yi(s,n.children.getContext(o),t):Yi(s,null,t):Yi(s,n,t)}),i.component?n&&n.outlet&&n.outlet.isActivated?t.canDeactivateChecks.push(new Jr(n.outlet.component,i)):t.canDeactivateChecks.push(new Jr(null,i)):t.canDeactivateChecks.push(new Jr(null,i))}function ao(e){return typeof e=="function"}function kx(e){return typeof e=="boolean"}function Px(e){return e&&ao(e.canLoad)}function Fx(e){return e&&ao(e.canActivate)}function jx(e){return e&&ao(e.canActivateChild)}function Lx(e){return e&&ao(e.canDeactivate)}function Vx(e){return e&&ao(e.canMatch)}function yI(e){return e instanceof ct||e?.name==="EmptyError"}var Ua=Symbol("INITIAL_VALUE");function ri(){return we(e=>Bn(e.map(n=>n.pipe(Ft(1),Xc(Ua)))).pipe(B(n=>{for(let t of n)if(t!==!0){if(t===Ua)return Ua;if(t===!1||Bx(t))return t}return!0}),Ae(n=>n!==Ua),Ft(1)))}function Bx(e){return ar(e)||e instanceof to}function Ux(e,n){return he(t=>{let{targetSnapshot:r,currentSnapshot:i,guards:{canActivateChecks:o,canDeactivateChecks:s}}=t;return s.length===0&&o.length===0?O(V(b({},t),{guardsResult:!0})):$x(s,r,i,e).pipe(he(a=>a&&kx(a)?Hx(r,o,e,n):O(a)),B(a=>V(b({},t),{guardsResult:a})))})}function $x(e,n,t,r){return ce(e).pipe(he(i=>Zx(i.component,i.route,t,n,r)),Ct(i=>i!==!0,!0))}function Hx(e,n,t,r){return ce(n).pipe(Pt(i=>wr(Gx(i.route.parent,r),zx(i.route,r),qx(e,i.path,t),Wx(e,i.route,t))),Ct(i=>i!==!0,!0))}function zx(e,n){return e!==null&&n&&n(new af(e)),O(!0)}function Gx(e,n){return e!==null&&n&&n(new of(e)),O(!0)}function Wx(e,n,t){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return O(!0);let i=r.map(o=>Yo(()=>{let s=so(n)??t,a=ii(o,s),c=Fx(a)?a.canActivate(n,e):tt(s,()=>a(n,e));return An(c).pipe(Ct())}));return O(i).pipe(ri())}function qx(e,n,t){let r=n[n.length-1],o=n.slice(0,n.length-1).reverse().map(s=>Rx(s)).filter(s=>s!==null).map(s=>Yo(()=>{let a=s.guards.map(c=>{let l=so(s.node)??t,d=ii(c,l),f=jx(d)?d.canActivateChild(r,e):tt(l,()=>d(r,e));return An(f).pipe(Ct())});return O(a).pipe(ri())}));return O(o).pipe(ri())}function Zx(e,n,t,r,i){let o=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!o||o.length===0)return O(!0);let s=o.map(a=>{let c=so(n)??i,l=ii(a,c),d=Lx(l)?l.canDeactivate(e,n,t,r):tt(c,()=>l(e,n,t,r));return An(d).pipe(Ct())});return O(s).pipe(ri())}function Qx(e,n,t,r){let i=n.canLoad;if(i===void 0||i.length===0)return O(!0);let o=i.map(s=>{let a=ii(s,e),c=Px(a)?a.canLoad(n,t):tt(e,()=>a(n,t));return An(c)});return O(o).pipe(ri(),DI(r))}function DI(e){return $c(Ee(n=>{if(typeof n!="boolean")throw ec(e,n)}),B(n=>n===!0))}function Yx(e,n,t,r){let i=n.canMatch;if(!i||i.length===0)return O(!0);let o=i.map(s=>{let a=ii(s,e),c=Vx(a)?a.canMatch(n,t):tt(e,()=>a(n,t));return An(c)});return O(o).pipe(ri(),DI(r))}var no=class{constructor(n){this.segmentGroup=n||null}},ro=class extends Error{constructor(n){super(),this.urlTree=n}};function Qr(e){return Ir(new no(e))}function Kx(e){return Ir(new R(4e3,!1))}function Xx(e){return Ir(mI(!1,Ke.GuardRejected))}var gf=class{constructor(n,t){this.urlSerializer=n,this.urlTree=t}lineralizeSegments(n,t){let r=[],i=t.root;for(;;){if(r=r.concat(i.segments),i.numberOfChildren===0)return O(r);if(i.numberOfChildren>1||!i.children[j])return Kx(`${n.redirectTo}`);i=i.children[j]}}applyRedirectCommands(n,t,r,i,o){if(typeof t!="string"){let a=t,{queryParams:c,fragment:l,routeConfig:d,url:f,outlet:p,params:h,data:y,title:M}=i,T=tt(o,()=>a({params:h,data:y,queryParams:c,fragment:l,routeConfig:d,url:f,outlet:p,title:M}));if(T instanceof tn)throw new ro(T);t=T}let s=this.applyRedirectCreateUrlTree(t,this.urlSerializer.parse(t),n,r);if(t[0]==="/")throw new ro(s);return s}applyRedirectCreateUrlTree(n,t,r,i){let o=this.createSegmentGroup(n,t.root,r,i);return new tn(o,this.createQueryParams(t.queryParams,this.urlTree.queryParams),t.fragment)}createQueryParams(n,t){let r={};return Object.entries(n).forEach(([i,o])=>{if(typeof o=="string"&&o[0]===":"){let a=o.substring(1);r[i]=t[a]}else r[i]=o}),r}createSegmentGroup(n,t,r,i){let o=this.createSegments(n,t.segments,r,i),s={};return Object.entries(t.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(n,c,r,i)}),new J(o,s)}createSegments(n,t,r,i){return t.map(o=>o.path[0]===":"?this.findPosParam(n,o,i):this.findOrReturn(o,r))}findPosParam(n,t,r){let i=r[t.path.substring(1)];if(!i)throw new R(4001,!1);return i}findOrReturn(n,t){let r=0;for(let i of t){if(i.path===n.path)return t.splice(r),i;r++}return n}},mf={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Jx(e,n,t,r,i){let o=II(e,n,t);return o.matched?(r=Cx(n,r),Yx(r,n,t,i).pipe(B(s=>s===!0?o:b({},mf)))):O(o)}function II(e,n,t){if(n.path==="**")return eA(t);if(n.path==="")return n.pathMatch==="full"&&(e.hasChildren()||t.length>0)?b({},mf):{matched:!0,consumedSegments:[],remainingSegments:t,parameters:{},positionalParamSegments:{}};let i=(n.matcher||QT)(t,e,n);if(!i)return b({},mf);let o={};Object.entries(i.posParams??{}).forEach(([a,c])=>{o[a]=c.path});let s=i.consumed.length>0?b(b({},o),i.consumed[i.consumed.length-1].parameters):o;return{matched:!0,consumedSegments:i.consumed,remainingSegments:t.slice(i.consumed.length),parameters:s,positionalParamSegments:i.posParams??{}}}function eA(e){return{matched:!0,parameters:e.length>0?JD(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function qD(e,n,t,r){return t.length>0&&rA(e,t,r)?{segmentGroup:new J(n,nA(r,new J(t,e.children))),slicedSegments:[]}:t.length===0&&iA(e,t,r)?{segmentGroup:new J(e.segments,tA(e,t,r,e.children)),slicedSegments:t}:{segmentGroup:new J(e.segments,e.children),slicedSegments:t}}function tA(e,n,t,r){let i={};for(let o of t)if(ic(e,n,o)&&!r[It(o)]){let s=new J([],{});i[It(o)]=s}return b(b({},r),i)}function nA(e,n){let t={};t[j]=n;for(let r of e)if(r.path===""&&It(r)!==j){let i=new J([],{});t[It(r)]=i}return t}function rA(e,n,t){return t.some(r=>ic(e,n,r)&&It(r)!==j)}function iA(e,n,t){return t.some(r=>ic(e,n,r))}function ic(e,n,t){return(e.hasChildren()||n.length>0)&&t.pathMatch==="full"?!1:t.path===""}function oA(e,n,t){return n.length===0&&!e.children[t]}var vf=class{};function sA(e,n,t,r,i,o,s="emptyOnly"){return new yf(e,n,t,r,i,s,o).recognize()}var aA=31,yf=class{constructor(n,t,r,i,o,s,a){this.injector=n,this.configLoader=t,this.rootComponentType=r,this.config=i,this.urlTree=o,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new gf(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(n){return new R(4002,`'${n.segmentGroup}'`)}recognize(){let n=qD(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(B(({children:t,rootSnapshot:r})=>{let i=new Ye(r,t),o=new Ja("",i),s=px(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,o.url=this.urlSerializer.serialize(s),{state:o,tree:s}}))}match(n){let t=new Xr([],Object.freeze({}),Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),j,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,j,t).pipe(B(r=>({children:r,rootSnapshot:t})),kt(r=>{if(r instanceof ro)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof no?this.noMatchError(r):r}))}processSegmentGroup(n,t,r,i,o){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,t,r,o):this.processSegment(n,t,r,r.segments,i,!0,o).pipe(B(s=>s instanceof Ye?[s]:[]))}processChildren(n,t,r,i){let o=[];for(let s of Object.keys(r.children))s==="primary"?o.unshift(s):o.push(s);return ce(o).pipe(Pt(s=>{let a=r.children[s],c=bx(t,s);return this.processSegmentGroup(n,c,a,s,i)}),Kc((s,a)=>(s.push(...a),s)),cn(null),Yc(),he(s=>{if(s===null)return Qr(r);let a=CI(s);return cA(a),O(a)}))}processSegment(n,t,r,i,o,s,a){return ce(t).pipe(Pt(c=>this.processSegmentAgainstRoute(c._injector??n,t,c,r,i,o,s,a).pipe(kt(l=>{if(l instanceof no)return O(null);throw l}))),Ct(c=>!!c),kt(c=>{if(yI(c))return oA(r,i,o)?O(new vf):Qr(r);throw c}))}processSegmentAgainstRoute(n,t,r,i,o,s,a,c){return It(r)!==s&&(s===j||!ic(i,o,r))?Qr(i):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,i,r,o,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,i,t,r,o,s,c):Qr(i)}expandSegmentAgainstRouteUsingRedirect(n,t,r,i,o,s,a){let{matched:c,parameters:l,consumedSegments:d,positionalParamSegments:f,remainingSegments:p}=II(t,i,o);if(!c)return Qr(t);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>aA&&(this.allowRedirects=!1));let h=new Xr(o,l,Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,ZD(i),It(i),i.component??i._loadedComponent??null,i,QD(i)),y=Xa(h,a,this.paramsInheritanceStrategy);h.params=Object.freeze(y.params),h.data=Object.freeze(y.data);let M=this.applyRedirects.applyRedirectCommands(d,i.redirectTo,f,h,n);return this.applyRedirects.lineralizeSegments(i,M).pipe(he(T=>this.processSegment(n,r,t,T.concat(p),s,!1,a)))}matchSegmentAgainstRoute(n,t,r,i,o,s){let a=Jx(t,r,i,n,this.urlSerializer);return r.path==="**"&&(t.children={}),a.pipe(we(c=>c.matched?(n=r._injector??n,this.getChildConfig(n,r,i).pipe(we(({routes:l})=>{let d=r._loadedInjector??n,{parameters:f,consumedSegments:p,remainingSegments:h}=c,y=new Xr(p,f,Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,ZD(r),It(r),r.component??r._loadedComponent??null,r,QD(r)),M=Xa(y,s,this.paramsInheritanceStrategy);y.params=Object.freeze(M.params),y.data=Object.freeze(M.data);let{segmentGroup:T,slicedSegments:F}=qD(t,p,h,l);if(F.length===0&&T.hasChildren())return this.processChildren(d,l,T,y).pipe(B(Z=>new Ye(y,Z)));if(l.length===0&&F.length===0)return O(new Ye(y,[]));let re=It(r)===o;return this.processSegment(d,l,T,F,re?j:o,!0,y).pipe(B(Z=>new Ye(y,Z instanceof Ye?[Z]:[])))}))):Qr(t)))}getChildConfig(n,t,r){return t.children?O({routes:t.children,injector:n}):t.loadChildren?t._loadedRoutes!==void 0?O({routes:t._loadedRoutes,injector:t._loadedInjector}):Qx(n,t,r,this.urlSerializer).pipe(he(i=>i?this.configLoader.loadChildren(n,t).pipe(Ee(o=>{t._loadedRoutes=o.routes,t._loadedInjector=o.injector})):Xx(t))):O({routes:[],injector:n})}};function cA(e){e.sort((n,t)=>n.value.outlet===j?-1:t.value.outlet===j?1:n.value.outlet.localeCompare(t.value.outlet))}function lA(e){let n=e.value.routeConfig;return n&&n.path===""}function CI(e){let n=[],t=new Set;for(let r of e){if(!lA(r)){n.push(r);continue}let i=n.find(o=>r.value.routeConfig===o.value.routeConfig);i!==void 0?(i.children.push(...r.children),t.add(i)):n.push(r)}for(let r of t){let i=CI(r.children);n.push(new Ye(r.value,i))}return n.filter(r=>!t.has(r))}function ZD(e){return e.data||{}}function QD(e){return e.resolve||{}}function uA(e,n,t,r,i,o){return he(s=>sA(e,n,t,r,s.extractedUrl,i,o).pipe(B(({state:a,tree:c})=>V(b({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function dA(e,n){return he(t=>{let{targetSnapshot:r,guards:{canActivateChecks:i}}=t;if(!i.length)return O(t);let o=new Set(i.map(c=>c.route)),s=new Set;for(let c of o)if(!s.has(c))for(let l of bI(c))s.add(l);let a=0;return ce(s).pipe(Pt(c=>o.has(c)?fA(c,r,e,n):(c.data=Xa(c,c.parent,e).resolve,O(void 0))),Ee(()=>a++),Er(1),he(c=>a===s.size?O(t):ze))})}function bI(e){let n=e.children.map(t=>bI(t)).flat();return[e,...n]}function fA(e,n,t,r){let i=e.routeConfig,o=e._resolve;return i?.title!==void 0&&!pI(i)&&(o[oo]=i.title),hA(o,e,n,r).pipe(B(s=>(e._resolvedData=s,e.data=Xa(e,e.parent,t).resolve,null)))}function hA(e,n,t,r){let i=Zd(e);if(i.length===0)return O({});let o={};return ce(i).pipe(he(s=>pA(e[s],n,t,r).pipe(Ct(),Ee(a=>{if(a instanceof to)throw ec(new ti,a);o[s]=a}))),Er(1),Zc(o),kt(s=>yI(s)?ze:Ir(s)))}function pA(e,n,t,r){let i=so(n)??r,o=ii(e,i),s=o.resolve?o.resolve(n,t):tt(i,()=>o(n,t));return An(s)}function Wd(e){return we(n=>{let t=e(n);return t?ce(t).pipe(B(()=>n)):O(n)})}var wI=(()=>{class e{buildTitle(t){let r,i=t.root;for(;i!==void 0;)r=this.getResolvedTitleForRoute(i)??r,i=i.children.find(o=>o.outlet===j);return r}getResolvedTitleForRoute(t){return t.data[oo]}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>v(gA),providedIn:"root"})}}return e})(),gA=(()=>{class e extends wI{constructor(t){super(),this.title=t}updateTitle(t){let r=this.buildTitle(t);r!==void 0&&this.title.setTitle(r)}static{this.\u0275fac=function(r){return new(r||e)(N(UD))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),co=new A("",{providedIn:"root",factory:()=>({})}),mA=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275cmp=I({type:e,selectors:[["ng-component"]],standalone:!0,features:[eD],decls:1,vars:0,template:function(r,i){r&1&&fd(0,"router-outlet")},dependencies:[Ex],encapsulation:2})}}return e})();function Cf(e){let n=e.children&&e.children.map(Cf),t=n?V(b({},e),{children:n}):b({},e);return!t.component&&!t.loadComponent&&(n||t.loadChildren)&&t.outlet&&t.outlet!==j&&(t.component=mA),t}var io=new A(""),bf=(()=>{class e{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=v(ma)}loadComponent(t){if(this.componentLoaders.get(t))return this.componentLoaders.get(t);if(t._loadedComponent)return O(t._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(t);let r=An(t.loadComponent()).pipe(B(EI),Ee(o=>{this.onLoadEndListener&&this.onLoadEndListener(t),t._loadedComponent=o}),ln(()=>{this.componentLoaders.delete(t)})),i=new yr(r,()=>new ae).pipe(vr());return this.componentLoaders.set(t,i),i}loadChildren(t,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return O({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let o=vA(r,this.compiler,t,this.onLoadEndListener).pipe(ln(()=>{this.childrenLoaders.delete(r)})),s=new yr(o,()=>new ae).pipe(vr());return this.childrenLoaders.set(r,s),s}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function vA(e,n,t,r){return An(e.loadChildren()).pipe(B(EI),he(i=>i instanceof Mi||Array.isArray(i)?O(i):ce(n.compileModuleAsync(i))),B(i=>{r&&r(e);let o,s,a=!1;return Array.isArray(i)?(s=i,a=!0):(o=i.create(t).injector,s=o.get(io,[],{optional:!0,self:!0}).flat()),{routes:s.map(Cf),injector:o}}))}function yA(e){return e&&typeof e=="object"&&"default"in e}function EI(e){return yA(e)?e.default:e}var wf=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>v(DA),providedIn:"root"})}}return e})(),DA=(()=>{class e{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,r){return t}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),MI=new A(""),_I=new A("");function IA(e,n,t){let r=e.get(_I),i=e.get(Ie);return e.get(g).runOutsideAngular(()=>{if(!i.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(l=>setTimeout(l));let o,s=new Promise(l=>{o=l}),a=i.startViewTransition(()=>(o(),CA(e))),{onViewTransitionCreated:c}=r;return c&&tt(e,()=>c({transition:a,from:n,to:t})),s})}function CA(e){return new Promise(n=>{dd({read:()=>setTimeout(n)},{injector:e})})}var bA=new A(""),Ef=(()=>{class e{get hasRequestedNavigation(){return this.navigationId!==0}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new ae,this.transitionAbortSubject=new ae,this.configLoader=v(bf),this.environmentInjector=v(ue),this.urlSerializer=v(cr),this.rootContexts=v(Rn),this.location=v(Dt),this.inputBindingEnabled=v(rc,{optional:!0})!==null,this.titleStrategy=v(wI),this.options=v(co,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=v(wf),this.createViewTransition=v(MI,{optional:!0}),this.navigationErrorHandler=v(bA,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>O(void 0),this.rootComponentType=null;let t=i=>this.events.next(new nf(i)),r=i=>this.events.next(new rf(i));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=t}complete(){this.transitions?.complete()}handleNavigationRequest(t){let r=++this.navigationId;this.transitions?.next(V(b(b({},this.transitions.value),t),{id:r}))}setupNavigations(t,r,i){return this.transitions=new ye({id:0,currentUrlTree:r,currentRawUrl:r,extractedUrl:this.urlHandlingStrategy.extract(r),urlAfterRedirects:this.urlHandlingStrategy.extract(r),rawUrl:r,extras:{},resolve:()=>{},reject:()=>{},promise:Promise.resolve(!0),source:Qi,restoredState:null,currentSnapshot:i.snapshot,targetSnapshot:null,currentRouterState:i,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(Ae(o=>o.id!==0),B(o=>V(b({},o),{extractedUrl:this.urlHandlingStrategy.extract(o.rawUrl)})),we(o=>{let s=!1,a=!1;return O(o).pipe(we(c=>{if(this.navigationId>o.id)return this.cancelNavigationTransition(o,"",Ke.SupersededByNewNavigation),ze;this.currentTransition=o,this.currentNavigation={id:c.id,initialUrl:c.rawUrl,extractedUrl:c.extractedUrl,targetBrowserUrl:typeof c.extras.browserUrl=="string"?this.urlSerializer.parse(c.extras.browserUrl):c.extras.browserUrl,trigger:c.source,extras:c.extras,previousNavigation:this.lastSuccessfulNavigation?V(b({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let l=!t.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),d=c.extras.onSameUrlNavigation??t.onSameUrlNavigation;if(!l&&d!=="reload"){let f="";return this.events.next(new xn(c.id,this.urlSerializer.serialize(c.rawUrl),f,qa.IgnoredSameUrlNavigation)),c.resolve(!1),ze}if(this.urlHandlingStrategy.shouldProcessUrl(c.rawUrl))return O(c).pipe(we(f=>{let p=this.transitions?.getValue();return this.events.next(new Tn(f.id,this.urlSerializer.serialize(f.extractedUrl),f.source,f.restoredState)),p!==this.transitions?.getValue()?ze:Promise.resolve(f)}),uA(this.environmentInjector,this.configLoader,this.rootComponentType,t.config,this.urlSerializer,this.paramsInheritanceStrategy),Ee(f=>{o.targetSnapshot=f.targetSnapshot,o.urlAfterRedirects=f.urlAfterRedirects,this.currentNavigation=V(b({},this.currentNavigation),{finalUrl:f.urlAfterRedirects});let p=new Za(f.id,this.urlSerializer.serialize(f.extractedUrl),this.urlSerializer.serialize(f.urlAfterRedirects),f.targetSnapshot);this.events.next(p)}));if(l&&this.urlHandlingStrategy.shouldProcessUrl(c.currentRawUrl)){let{id:f,extractedUrl:p,source:h,restoredState:y,extras:M}=c,T=new Tn(f,this.urlSerializer.serialize(p),h,y);this.events.next(T);let F=fI(this.rootComponentType).snapshot;return this.currentTransition=o=V(b({},c),{targetSnapshot:F,urlAfterRedirects:p,extras:V(b({},M),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=p,O(o)}else{let f="";return this.events.next(new xn(c.id,this.urlSerializer.serialize(c.extractedUrl),f,qa.IgnoredByUrlHandlingStrategy)),c.resolve(!1),ze}}),Ee(c=>{let l=new Xd(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}),B(c=>(this.currentTransition=o=V(b({},c),{guards:Ax(c.targetSnapshot,c.currentSnapshot,this.rootContexts)}),o)),Ux(this.environmentInjector,c=>this.events.next(c)),Ee(c=>{if(o.guardsResult=c.guardsResult,c.guardsResult&&typeof c.guardsResult!="boolean")throw ec(this.urlSerializer,c.guardsResult);let l=new Jd(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot,!!c.guardsResult);this.events.next(l)}),Ae(c=>c.guardsResult?!0:(this.cancelNavigationTransition(c,"",Ke.GuardRejected),!1)),Wd(c=>{if(c.guards.canActivateChecks.length)return O(c).pipe(Ee(l=>{let d=new ef(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(d)}),we(l=>{let d=!1;return O(l).pipe(dA(this.paramsInheritanceStrategy,this.environmentInjector),Ee({next:()=>d=!0,complete:()=>{d||this.cancelNavigationTransition(l,"",Ke.NoDataFromResolver)}}))}),Ee(l=>{let d=new tf(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(d)}))}),Wd(c=>{let l=d=>{let f=[];d.routeConfig?.loadComponent&&!d.routeConfig._loadedComponent&&f.push(this.configLoader.loadComponent(d.routeConfig).pipe(Ee(p=>{d.component=p}),B(()=>{})));for(let p of d.children)f.push(...l(p));return f};return Bn(l(c.targetSnapshot.root)).pipe(cn(null),Ft(1))}),Wd(()=>this.afterPreactivation()),we(()=>{let{currentSnapshot:c,targetSnapshot:l}=o,d=this.createViewTransition?.(this.environmentInjector,c.root,l.root);return d?ce(d).pipe(B(()=>o)):O(o)}),B(c=>{let l=Mx(t.routeReuseStrategy,c.targetSnapshot,c.currentRouterState);return this.currentTransition=o=V(b({},c),{targetRouterState:l}),this.currentNavigation.targetRouterState=l,o}),Ee(()=>{this.events.next(new Ji)}),xx(this.rootContexts,t.routeReuseStrategy,c=>this.events.next(c),this.inputBindingEnabled),Ft(1),Ee({next:c=>{s=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Nt(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects))),this.titleStrategy?.updateTitle(c.targetRouterState.snapshot),c.resolve(!0)},complete:()=>{s=!0}}),Jc(this.transitionAbortSubject.pipe(Ee(c=>{throw c}))),ln(()=>{!s&&!a&&this.cancelNavigationTransition(o,"",Ke.SupersededByNewNavigation),this.currentTransition?.id===o.id&&(this.currentNavigation=null,this.currentTransition=null)}),kt(c=>{if(a=!0,vI(c))this.events.next(new en(o.id,this.urlSerializer.serialize(o.extractedUrl),c.message,c.cancellationCode)),Tx(c)?this.events.next(new ni(c.url,c.navigationBehaviorOptions)):o.resolve(!1);else{let l=new Xi(o.id,this.urlSerializer.serialize(o.extractedUrl),c,o.targetSnapshot??void 0);try{let d=tt(this.environmentInjector,()=>this.navigationErrorHandler?.(l));if(d instanceof to){let{message:f,cancellationCode:p}=ec(this.urlSerializer,d);this.events.next(new en(o.id,this.urlSerializer.serialize(o.extractedUrl),f,p)),this.events.next(new ni(d.redirectTo,d.navigationBehaviorOptions))}else{this.events.next(l);let f=t.errorHandler(c);o.resolve(!!f)}}catch(d){this.options.resolveNavigationPromiseOnError?o.resolve(!1):o.reject(d)}}return ze}))}))}cancelNavigationTransition(t,r,i){let o=new en(t.id,this.urlSerializer.serialize(t.extractedUrl),r,i);this.events.next(o),t.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let t=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return t.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function wA(e){return e!==Qi}var EA=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>v(MA),providedIn:"root"})}}return e})(),Df=class{shouldDetach(n){return!1}store(n,t){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,t){return n.routeConfig===t.routeConfig}},MA=(()=>{class e extends Df{static{this.\u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})()}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),SI=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>v(_A),providedIn:"root"})}}return e})(),_A=(()=>{class e extends SI{constructor(){super(...arguments),this.location=v(Dt),this.urlSerializer=v(cr),this.options=v(co,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=v(wf),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new tn,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=fI(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(t){return this.location.subscribe(r=>{r.type==="popstate"&&t(r.url,r.state)})}handleRouterEvent(t,r){if(t instanceof Tn)this.stateMemento=this.createStateMemento();else if(t instanceof xn)this.rawUrlTree=r.initialUrl;else if(t instanceof Za){if(this.urlUpdateStrategy==="eager"&&!r.extras.skipLocationChange){let i=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl);this.setBrowserUrl(r.targetBrowserUrl??i,r)}}else t instanceof Ji?(this.currentUrlTree=r.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl),this.routerState=r.targetRouterState,this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(r.targetBrowserUrl??this.rawUrlTree,r)):t instanceof en&&(t.code===Ke.GuardRejected||t.code===Ke.NoDataFromResolver)?this.restoreHistory(r):t instanceof Xi?this.restoreHistory(r,!0):t instanceof Nt&&(this.lastSuccessfulId=t.id,this.currentPageId=this.browserPageId)}setBrowserUrl(t,r){let i=t instanceof tn?this.urlSerializer.serialize(t):t;if(this.location.isCurrentPathEqualTo(i)||r.extras.replaceUrl){let o=this.browserPageId,s=b(b({},r.extras.state),this.generateNgRouterState(r.id,o));this.location.replaceState(i,"",s)}else{let o=b(b({},r.extras.state),this.generateNgRouterState(r.id,this.browserPageId+1));this.location.go(i,"",o)}}restoreHistory(t,r=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,o=this.currentPageId-i;o!==0?this.location.historyGo(o):this.currentUrlTree===t.finalUrl&&o===0&&(this.resetState(t),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetState(t),this.resetUrlToCurrentUrlTree())}resetState(t){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(t,r){return this.canceledNavigationResolution==="computed"?{navigationId:t,\u0275routerPageId:r}:{navigationId:t}}static{this.\u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})()}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),qi=function(e){return e[e.COMPLETE=0]="COMPLETE",e[e.FAILED=1]="FAILED",e[e.REDIRECTING=2]="REDIRECTING",e}(qi||{});function TI(e,n){e.events.pipe(Ae(t=>t instanceof Nt||t instanceof en||t instanceof Xi||t instanceof xn),B(t=>t instanceof Nt||t instanceof xn?qi.COMPLETE:(t instanceof en?t.code===Ke.Redirect||t.code===Ke.SupersededByNewNavigation:!1)?qi.REDIRECTING:qi.FAILED),Ae(t=>t!==qi.REDIRECTING),Ft(1)).subscribe(()=>{n()})}function SA(e){throw e}var TA={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},xA={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},_e=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){this.disposed=!1,this.console=v(ga),this.stateManager=v(SI),this.options=v(co,{optional:!0})||{},this.pendingTasks=v(Wt),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=v(Ef),this.urlSerializer=v(cr),this.location=v(Dt),this.urlHandlingStrategy=v(wf),this._events=new ae,this.errorHandler=this.options.errorHandler||SA,this.navigated=!1,this.routeReuseStrategy=v(EA),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=v(io,{optional:!0})?.flat()??[],this.componentInputBindingEnabled=!!v(rc,{optional:!0}),this.eventsSubscription=new fe,this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:t=>{this.console.warn(t)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){let t=this.navigationTransitions.events.subscribe(r=>{try{let i=this.navigationTransitions.currentTransition,o=this.navigationTransitions.currentNavigation;if(i!==null&&o!==null){if(this.stateManager.handleRouterEvent(r,o),r instanceof en&&r.code!==Ke.Redirect&&r.code!==Ke.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Nt)this.navigated=!0;else if(r instanceof ni){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,i.currentRawUrl),c=b({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||wA(i.source)},s);this.scheduleNavigation(a,Qi,null,c,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}RA(r)&&this._events.next(r)}catch(i){this.navigationTransitions.transitionAbortSubject.next(i)}});this.eventsSubscription.add(t)}resetRootComponentType(t){this.routerState.root.component=t,this.navigationTransitions.rootComponentType=t}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Qi,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((t,r)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(t,"popstate",r)},0)})}navigateToSyncWithBrowser(t,r,i){let o={replaceUrl:!0},s=i?.navigationId?i:null;if(i){let c=b({},i);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(o.state=c)}let a=this.parseUrl(t);this.scheduleNavigation(a,r,s,o)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(t){this.config=t.map(Cf),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(t,r={}){let{relativeTo:i,queryParams:o,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,l=c?this.currentUrlTree.fragment:s,d=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":d=b(b({},this.currentUrlTree.queryParams),o);break;case"preserve":d=this.currentUrlTree.queryParams;break;default:d=o||null}d!==null&&(d=this.removeEmptyProps(d));let f;try{let p=i?i.snapshot:this.routerState.snapshot.root;f=cI(p)}catch{(typeof t[0]!="string"||t[0][0]!=="/")&&(t=[]),f=this.currentUrlTree.root}return lI(f,t,d,l??null)}navigateByUrl(t,r={skipLocationChange:!1}){let i=ar(t)?t:this.parseUrl(t),o=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(o,Qi,null,r)}navigate(t,r={skipLocationChange:!1}){return AA(t),this.navigateByUrl(this.createUrlTree(t,r),r)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){try{return this.urlSerializer.parse(t)}catch{return this.urlSerializer.parse("/")}}isActive(t,r){let i;if(r===!0?i=b({},TA):r===!1?i=b({},xA):i=r,ar(t))return $D(this.currentUrlTree,t,i);let o=this.parseUrl(t);return $D(this.currentUrlTree,o,i)}removeEmptyProps(t){return Object.entries(t).reduce((r,[i,o])=>(o!=null&&(r[i]=o),r),{})}scheduleNavigation(t,r,i,o,s){if(this.disposed)return Promise.resolve(!1);let a,c,l;s?(a=s.resolve,c=s.reject,l=s.promise):l=new Promise((f,p)=>{a=f,c=p});let d=this.pendingTasks.add();return TI(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(d))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:t,extras:o,resolve:a,reject:c,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(f=>Promise.reject(f))}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function AA(e){for(let n=0;n<e.length;n++)if(e[n]==null)throw new R(4008,!1)}function RA(e){return!(e instanceof Ji)&&!(e instanceof ni)}var Mf=(()=>{class e{constructor(t,r,i,o,s,a){this.router=t,this.route=r,this.tabIndexAttribute=i,this.renderer=o,this.el=s,this.locationStrategy=a,this.href=null,this.onChanges=new ae,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1,this.routerLinkInput=null;let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=t.events.subscribe(l=>{l instanceof Nt&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(t){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",t)}ngOnChanges(t){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(t){t==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(ar(t)?this.routerLinkInput=t:this.routerLinkInput=Array.isArray(t)?t:[t],this.setTabIndexIfNotOnNativeEl("0"))}onClick(t,r,i,o,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(t!==0||r||i||o||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let t=this.urlTree;this.href=t!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(t)):null;let r=this.href===null?null:Wv(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(t,r){let i=this.renderer,o=this.el.nativeElement;r!==null?i.setAttribute(o,t,r):i.removeAttribute(o,t)}get urlTree(){return this.routerLinkInput===null?null:ar(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static{this.\u0275fac=function(r){return new(r||e)(u(_e),u(Le),Gt("tabindex"),u(In),u(m),u(Qe))}}static{this.\u0275dir=H({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,i){r&1&&Me("click",function(s){return i.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&xt("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",wn],skipLocationChange:[2,"skipLocationChange","skipLocationChange",wn],replaceUrl:[2,"replaceUrl","replaceUrl",wn],routerLink:"routerLink"},standalone:!0,features:[ud,nt]})}}return e})();var nc=class{},bU=(()=>{class e{preload(t,r){return r().pipe(kt(()=>O(null)))}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var NA=(()=>{class e{constructor(t,r,i,o,s){this.router=t,this.injector=i,this.preloadingStrategy=o,this.loader=s}setUpPreloading(){this.subscription=this.router.events.pipe(Ae(t=>t instanceof Nt),Pt(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(t,r){let i=[];for(let o of r){o.providers&&!o._injector&&(o._injector=ua(o.providers,t,`Route: ${o.path}`));let s=o._injector??t,a=o._loadedInjector??s;(o.loadChildren&&!o._loadedRoutes&&o.canLoad===void 0||o.loadComponent&&!o._loadedComponent)&&i.push(this.preloadConfig(s,o)),(o.children||o._loadedRoutes)&&i.push(this.processRoutes(a,o.children??o._loadedRoutes))}return ce(i).pipe(br())}preloadConfig(t,r){return this.preloadingStrategy.preload(r,()=>{let i;r.loadChildren&&r.canLoad===void 0?i=this.loader.loadChildren(t,r):i=O(null);let o=i.pipe(he(s=>s===null?O(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??t,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return ce([o,s]).pipe(br())}else return o})}static{this.\u0275fac=function(r){return new(r||e)(N(_e),N(ma),N(ue),N(nc),N(bf))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),xI=new A(""),OA=(()=>{class e{constructor(t,r,i,o,s={}){this.urlSerializer=t,this.transitions=r,this.viewportScroller=i,this.zone=o,this.options=s,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(t=>{t instanceof Tn?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=t.navigationTrigger,this.restoredId=t.restoredState?t.restoredState.navigationId:0):t instanceof Nt?(this.lastId=t.id,this.scheduleScrollEvent(t,this.urlSerializer.parse(t.urlAfterRedirects).fragment)):t instanceof xn&&t.code===qa.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(t,this.urlSerializer.parse(t.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(t=>{t instanceof Qa&&(t.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(t.position):t.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(t.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(t,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new Qa(t,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static{this.\u0275fac=function(r){ay()}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})();function wU(e,...n){return xi([{provide:io,multi:!0,useValue:e},[],{provide:Le,useFactory:AI,deps:[_e]},{provide:Fi,multi:!0,useFactory:RI},n.map(t=>t.\u0275providers)])}function AI(e){return e.routerState.root}function oi(e,n){return{\u0275kind:e,\u0275providers:n}}function RI(){let e=v(le);return n=>{let t=e.get(At);if(n!==t.components[0])return;let r=e.get(_e),i=e.get(NI);e.get(_f)===1&&r.initialNavigation(),e.get(OI,null,z.Optional)?.setUpPreloading(),e.get(xI,null,z.Optional)?.init(),r.resetRootComponentType(t.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var NI=new A("",{factory:()=>new ae}),_f=new A("",{providedIn:"root",factory:()=>1});function kA(){return oi(2,[{provide:_f,useValue:0},{provide:qr,multi:!0,deps:[le],useFactory:n=>{let t=n.get(gD,Promise.resolve());return()=>t.then(()=>new Promise(r=>{let i=n.get(_e),o=n.get(NI);TI(i,()=>{r(!0)}),n.get(Ef).afterPreactivation=()=>(r(!0),o.closed?O(void 0):o),i.initialNavigation()}))}}])}function PA(){return oi(3,[{provide:qr,multi:!0,useFactory:()=>{let n=v(_e);return()=>{n.setUpLocationChangeListener()}}},{provide:_f,useValue:2}])}var OI=new A("");function FA(e){return oi(0,[{provide:OI,useExisting:NA},{provide:nc,useExisting:e}])}function EU(){return oi(6,[{provide:Qe,useClass:_d}])}function jA(){return oi(8,[WD,{provide:rc,useExisting:WD}])}function LA(e){let n=[{provide:MI,useValue:IA},{provide:_I,useValue:b({skipNextTransition:!!e?.skipInitialTransition},e)}];return oi(9,n)}var YD=new A("ROUTER_FORROOT_GUARD"),VA=[Dt,{provide:cr,useClass:ti},_e,Rn,{provide:Le,useFactory:AI,deps:[_e]},bf,[]],MU=(()=>{class e{constructor(t){}static forRoot(t,r){return{ngModule:e,providers:[VA,[],{provide:io,multi:!0,useValue:t},{provide:YD,useFactory:HA,deps:[[_e,new zs,new Eu]]},{provide:co,useValue:r||{}},r?.useHash?UA():$A(),BA(),r?.preloadingStrategy?FA(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?zA(r):[],r?.bindToComponentInputs?jA().\u0275providers:[],r?.enableViewTransitions?LA().\u0275providers:[],GA()]}}static forChild(t){return{ngModule:e,providers:[{provide:io,multi:!0,useValue:t}]}}static{this.\u0275fac=function(r){return new(r||e)(N(YD,8))}}static{this.\u0275mod=gt({type:e})}static{this.\u0275inj=pt({})}}return e})();function BA(){return{provide:xI,useFactory:()=>{let e=v(ID),n=v(g),t=v(co),r=v(Ef),i=v(cr);return t.scrollOffset&&e.setOffset(t.scrollOffset),new OA(i,r,e,n,t)}}}function UA(){return{provide:Qe,useClass:_d}}function $A(){return{provide:Qe,useClass:Md}}function HA(e){return"guarded"}function zA(e){return[e.initialNavigation==="disabled"?PA().\u0275providers:[],e.initialNavigation==="enabledBlocking"?kA().\u0275providers:[]]}var KD=new A("");function GA(){return[{provide:KD,useFactory:RI},{provide:Fi,multi:!0,useExisting:KD}]}var HI=(()=>{class e{constructor(t,r){this._renderer=t,this._elementRef=r,this.onChange=i=>{},this.onTouched=()=>{}}setProperty(t,r){this._renderer.setProperty(this._elementRef.nativeElement,t,r)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}static{this.\u0275fac=function(r){return new(r||e)(u(In),u(m))}}static{this.\u0275dir=H({type:e})}}return e})(),WA=(()=>{class e extends HI{static{this.\u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})()}static{this.\u0275dir=H({type:e,features:[oe]})}}return e})(),ur=new A("");var qA={provide:ur,useExisting:et(()=>zI),multi:!0};function ZA(){let e=Xt()?Xt().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var QA=new A(""),zI=(()=>{class e extends HI{constructor(t,r,i){super(t,r),this._compositionMode=i,this._composing=!1,this._compositionMode==null&&(this._compositionMode=!ZA())}writeValue(t){let r=t??"";this.setProperty("value",r)}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}static{this.\u0275fac=function(r){return new(r||e)(u(In),u(m),u(QA,8))}}static{this.\u0275dir=H({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,i){r&1&&Me("input",function(s){return i._handleInput(s.target.value)})("blur",function(){return i.onTouched()})("compositionstart",function(){return i._compositionStart()})("compositionend",function(s){return i._compositionEnd(s.target.value)})},features:[$e([qA]),oe]})}}return e})();function go(e){return e==null||(typeof e=="string"||Array.isArray(e))&&e.length===0}var Nn=new A(""),GI=new A("");function YA(e){return n=>{if(go(n.value)||go(e))return null;let t=parseFloat(n.value);return!isNaN(t)&&t<e?{min:{min:e,actual:n.value}}:null}}function KA(e){return n=>{if(go(n.value)||go(e))return null;let t=parseFloat(n.value);return!isNaN(t)&&t>e?{max:{max:e,actual:n.value}}:null}}function XA(e){return go(e.value)?{required:!0}:null}function PI(e){return null}function WI(e){return e!=null}function qI(e){return rr(e)?ce(e):e}function ZI(e){let n={};return e.forEach(t=>{n=t!=null?b(b({},n),t):n}),Object.keys(n).length===0?null:n}function QI(e,n){return n.map(t=>t(e))}function JA(e){return!e.validate}function YI(e){return e.map(n=>JA(n)?n:t=>n.validate(t))}function eR(e){if(!e)return null;let n=e.filter(WI);return n.length==0?null:function(t){return ZI(QI(t,n))}}function Tf(e){return e!=null?eR(YI(e)):null}function tR(e){if(!e)return null;let n=e.filter(WI);return n.length==0?null:function(t){let r=QI(t,n).map(qI);return qc(r).pipe(B(ZI))}}function xf(e){return e!=null?tR(YI(e)):null}function FI(e,n){return e===null?[n]:Array.isArray(e)?[...e,n]:[e,n]}function nR(e){return e._rawValidators}function rR(e){return e._rawAsyncValidators}function Sf(e){return e?Array.isArray(e)?e:[e]:[]}function sc(e,n){return Array.isArray(e)?e.includes(n):e===n}function jI(e,n){let t=Sf(n);return Sf(e).forEach(i=>{sc(t,i)||t.push(i)}),t}function LI(e,n){return Sf(n).filter(t=>!sc(e,t))}var ac=class{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(n){this._rawValidators=n||[],this._composedValidatorFn=Tf(this._rawValidators)}_setAsyncValidators(n){this._rawAsyncValidators=n||[],this._composedAsyncValidatorFn=xf(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(n){this._onDestroyCallbacks.push(n)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(n=>n()),this._onDestroyCallbacks=[]}reset(n=void 0){this.control&&this.control.reset(n)}hasError(n,t){return this.control?this.control.hasError(n,t):!1}getError(n,t){return this.control?this.control.getError(n,t):null}},ci=class extends ac{get formDirective(){return null}get path(){return null}},lr=class extends ac{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}},cc=class{constructor(n){this._cd=n}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},iR={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},$U=V(b({},iR),{"[class.ng-submitted]":"isSubmitted"}),HU=(()=>{class e extends cc{constructor(t){super(t)}static{this.\u0275fac=function(r){return new(r||e)(u(lr,2))}}static{this.\u0275dir=H({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,i){r&2&&da("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)},features:[oe]})}}return e})(),zU=(()=>{class e extends cc{constructor(t){super(t)}static{this.\u0275fac=function(r){return new(r||e)(u(ci,10))}}static{this.\u0275dir=H({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,i){r&2&&da("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)("ng-submitted",i.isSubmitted)},features:[oe]})}}return e})();var lo="VALID",oc="INVALID",si="PENDING",uo="DISABLED",li=class{},lc=class extends li{constructor(n,t){super(),this.value=n,this.source=t}},ho=class extends li{constructor(n,t){super(),this.pristine=n,this.source=t}},po=class extends li{constructor(n,t){super(),this.touched=n,this.source=t}},ai=class extends li{constructor(n,t){super(),this.status=n,this.source=t}};function KI(e){return(fc(e)?e.validators:e)||null}function oR(e){return Array.isArray(e)?Tf(e):e||null}function XI(e,n){return(fc(n)?n.asyncValidators:e)||null}function sR(e){return Array.isArray(e)?xf(e):e||null}function fc(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function aR(e,n,t){let r=e.controls;if(!(n?Object.keys(r):r).length)throw new R(1e3,"");if(!r[t])throw new R(1001,"")}function cR(e,n,t){e._forEachChild((r,i)=>{if(t[i]===void 0)throw new R(1002,"")})}var uc=class{constructor(n,t){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=null,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this._status=ji(()=>this.statusReactive()),this.statusReactive=Ni(void 0),this._pristine=ji(()=>this.pristineReactive()),this.pristineReactive=Ni(!0),this._touched=ji(()=>this.touchedReactive()),this.touchedReactive=Ni(!1),this._events=new ae,this.events=this._events.asObservable(),this._onDisabledChange=[],this._assignValidators(n),this._assignAsyncValidators(t)}get validator(){return this._composedValidatorFn}set validator(n){this._rawValidators=this._composedValidatorFn=n}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(n){this._rawAsyncValidators=this._composedAsyncValidatorFn=n}get parent(){return this._parent}get status(){return Zt(this.statusReactive)}set status(n){Zt(()=>this.statusReactive.set(n))}get valid(){return this.status===lo}get invalid(){return this.status===oc}get pending(){return this.status==si}get disabled(){return this.status===uo}get enabled(){return this.status!==uo}get pristine(){return Zt(this.pristineReactive)}set pristine(n){Zt(()=>this.pristineReactive.set(n))}get dirty(){return!this.pristine}get touched(){return Zt(this.touchedReactive)}set touched(n){Zt(()=>this.touchedReactive.set(n))}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(n){this._assignValidators(n)}setAsyncValidators(n){this._assignAsyncValidators(n)}addValidators(n){this.setValidators(jI(n,this._rawValidators))}addAsyncValidators(n){this.setAsyncValidators(jI(n,this._rawAsyncValidators))}removeValidators(n){this.setValidators(LI(n,this._rawValidators))}removeAsyncValidators(n){this.setAsyncValidators(LI(n,this._rawAsyncValidators))}hasValidator(n){return sc(this._rawValidators,n)}hasAsyncValidator(n){return sc(this._rawAsyncValidators,n)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(n={}){let t=this.touched===!1;this.touched=!0;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsTouched(V(b({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new po(!0,r))}markAllAsTouched(n={}){this.markAsTouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsTouched(n))}markAsUntouched(n={}){let t=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=n.sourceControl??this;this._forEachChild(i=>{i.markAsUntouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:r})}),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,r),t&&n.emitEvent!==!1&&this._events.next(new po(!1,r))}markAsDirty(n={}){let t=this.pristine===!0;this.pristine=!1;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsDirty(V(b({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new ho(!1,r))}markAsPristine(n={}){let t=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=n.sourceControl??this;this._forEachChild(i=>{i.markAsPristine({onlySelf:!0,emitEvent:n.emitEvent})}),this._parent&&!n.onlySelf&&this._parent._updatePristine(n,r),t&&n.emitEvent!==!1&&this._events.next(new ho(!0,r))}markAsPending(n={}){this.status=si;let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new ai(this.status,t)),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.markAsPending(V(b({},n),{sourceControl:t}))}disable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=uo,this.errors=null,this._forEachChild(i=>{i.disable(V(b({},n),{onlySelf:!0}))}),this._updateValue();let r=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new lc(this.value,r)),this._events.next(new ai(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(V(b({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(i=>i(!0))}enable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=lo,this._forEachChild(r=>{r.enable(V(b({},n),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent}),this._updateAncestors(V(b({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(n,t){this._parent&&!n.onlySelf&&(this._parent.updateValueAndValidity(n),n.skipPristineCheck||this._parent._updatePristine({},t),this._parent._updateTouched({},t))}setParent(n){this._parent=n}getRawValue(){return this.value}updateValueAndValidity(n={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===lo||this.status===si)&&this._runAsyncValidator(r,n.emitEvent)}let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new lc(this.value,t)),this._events.next(new ai(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.updateValueAndValidity(V(b({},n),{sourceControl:t}))}_updateTreeValidity(n={emitEvent:!0}){this._forEachChild(t=>t._updateTreeValidity(n)),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?uo:lo}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(n,t){if(this.asyncValidator){this.status=si,this._hasOwnPendingAsyncValidator={emitEvent:t!==!1};let r=qI(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(i=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(i,{emitEvent:t,shouldHaveEmitted:n})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let n=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(n,t={}){this.errors=n,this._updateControlsErrors(t.emitEvent!==!1,this,t.shouldHaveEmitted)}get(n){let t=n;return t==null||(Array.isArray(t)||(t=t.split(".")),t.length===0)?null:t.reduce((r,i)=>r&&r._find(i),this)}getError(n,t){let r=t?this.get(t):this;return r&&r.errors?r.errors[n]:null}hasError(n,t){return!!this.getError(n,t)}get root(){let n=this;for(;n._parent;)n=n._parent;return n}_updateControlsErrors(n,t,r){this.status=this._calculateStatus(),n&&this.statusChanges.emit(this.status),(n||r)&&this._events.next(new ai(this.status,t)),this._parent&&this._parent._updateControlsErrors(n,t,r)}_initObservables(){this.valueChanges=new ee,this.statusChanges=new ee}_calculateStatus(){return this._allControlsDisabled()?uo:this.errors?oc:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(si)?si:this._anyControlsHaveStatus(oc)?oc:lo}_anyControlsHaveStatus(n){return this._anyControls(t=>t.status===n)}_anyControlsDirty(){return this._anyControls(n=>n.dirty)}_anyControlsTouched(){return this._anyControls(n=>n.touched)}_updatePristine(n,t){let r=!this._anyControlsDirty(),i=this.pristine!==r;this.pristine=r,this._parent&&!n.onlySelf&&this._parent._updatePristine(n,t),i&&this._events.next(new ho(this.pristine,t))}_updateTouched(n={},t){this.touched=this._anyControlsTouched(),this._events.next(new po(this.touched,t)),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,t)}_registerOnCollectionChange(n){this._onCollectionChange=n}_setUpdateStrategy(n){fc(n)&&n.updateOn!=null&&(this._updateOn=n.updateOn)}_parentMarkedDirty(n){let t=this._parent&&this._parent.dirty;return!n&&!!t&&!this._parent._anyControlsDirty()}_find(n){return null}_assignValidators(n){this._rawValidators=Array.isArray(n)?n.slice():n,this._composedValidatorFn=oR(this._rawValidators)}_assignAsyncValidators(n){this._rawAsyncValidators=Array.isArray(n)?n.slice():n,this._composedAsyncValidatorFn=sR(this._rawAsyncValidators)}},dc=class extends uc{constructor(n,t,r){super(KI(t),XI(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(n,t){return this.controls[n]?this.controls[n]:(this.controls[n]=t,t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange),t)}addControl(n,t,r={}){this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(n,t={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}setControl(n,t,r={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],t&&this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(n){return this.controls.hasOwnProperty(n)&&this.controls[n].enabled}setValue(n,t={}){cR(this,!0,n),Object.keys(n).forEach(r=>{aR(this,!0,r),this.controls[r].setValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(Object.keys(n).forEach(r=>{let i=this.controls[r];i&&i.patchValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n={},t={}){this._forEachChild((r,i)=>{r.reset(n?n[i]:null,{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this._reduceChildren({},(n,t,r)=>(n[r]=t.getRawValue(),n))}_syncPendingControls(){let n=this._reduceChildren(!1,(t,r)=>r._syncPendingControls()?!0:t);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){Object.keys(this.controls).forEach(t=>{let r=this.controls[t];r&&n(r,t)})}_setUpControls(){this._forEachChild(n=>{n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(n){for(let[t,r]of Object.entries(this.controls))if(this.contains(t)&&n(r))return!0;return!1}_reduceValue(){let n={};return this._reduceChildren(n,(t,r,i)=>((r.enabled||this.disabled)&&(t[i]=r.value),t))}_reduceChildren(n,t){let r=n;return this._forEachChild((i,o)=>{r=t(r,i,o)}),r}_allControlsDisabled(){for(let n of Object.keys(this.controls))if(this.controls[n].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(n){return this.controls.hasOwnProperty(n)?this.controls[n]:null}};var Af=new A("CallSetDisabledState",{providedIn:"root",factory:()=>Rf}),Rf="always";function lR(e,n){return[...n.path,e]}function JI(e,n,t=Rf){e0(e,n),n.valueAccessor.writeValue(e.value),(e.disabled||t==="always")&&n.valueAccessor.setDisabledState?.(e.disabled),dR(e,n),hR(e,n),fR(e,n),uR(e,n)}function VI(e,n){e.forEach(t=>{t.registerOnValidatorChange&&t.registerOnValidatorChange(n)})}function uR(e,n){if(n.valueAccessor.setDisabledState){let t=r=>{n.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(t),n._registerOnDestroy(()=>{e._unregisterOnDisabledChange(t)})}}function e0(e,n){let t=nR(e);n.validator!==null?e.setValidators(FI(t,n.validator)):typeof t=="function"&&e.setValidators([t]);let r=rR(e);n.asyncValidator!==null?e.setAsyncValidators(FI(r,n.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let i=()=>e.updateValueAndValidity();VI(n._rawValidators,i),VI(n._rawAsyncValidators,i)}function dR(e,n){n.valueAccessor.registerOnChange(t=>{e._pendingValue=t,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&t0(e,n)})}function fR(e,n){n.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&t0(e,n),e.updateOn!=="submit"&&e.markAsTouched()})}function t0(e,n){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),n.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function hR(e,n){let t=(r,i)=>{n.valueAccessor.writeValue(r),i&&n.viewToModelUpdate(r)};e.registerOnChange(t),n._registerOnDestroy(()=>{e._unregisterOnChange(t)})}function pR(e,n){e==null,e0(e,n)}function gR(e,n){if(!e.hasOwnProperty("model"))return!1;let t=e.model;return t.isFirstChange()?!0:!Object.is(n,t.currentValue)}function mR(e){return Object.getPrototypeOf(e.constructor)===WA}function vR(e,n){e._syncPendingControls(),n.forEach(t=>{let r=t.control;r.updateOn==="submit"&&r._pendingChange&&(t.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function yR(e,n){if(!n)return null;Array.isArray(n);let t,r,i;return n.forEach(o=>{o.constructor===zI?t=o:mR(o)?r=o:i=o}),i||r||t||null}var DR={provide:ci,useExisting:et(()=>IR)},fo=Promise.resolve(),IR=(()=>{class e extends ci{get submitted(){return Zt(this.submittedReactive)}constructor(t,r,i){super(),this.callSetDisabledState=i,this._submitted=ji(()=>this.submittedReactive()),this.submittedReactive=Ni(!1),this._directives=new Set,this.ngSubmit=new ee,this.form=new dc({},Tf(t),xf(r))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(t){fo.then(()=>{let r=this._findContainer(t.path);t.control=r.registerControl(t.name,t.control),JI(t.control,t,this.callSetDisabledState),t.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(t)})}getControl(t){return this.form.get(t.path)}removeControl(t){fo.then(()=>{let r=this._findContainer(t.path);r&&r.removeControl(t.name),this._directives.delete(t)})}addFormGroup(t){fo.then(()=>{let r=this._findContainer(t.path),i=new dc({});pR(i,t),r.registerControl(t.name,i),i.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(t){fo.then(()=>{let r=this._findContainer(t.path);r&&r.removeControl(t.name)})}getFormGroup(t){return this.form.get(t.path)}updateModel(t,r){fo.then(()=>{this.form.get(t.path).setValue(r)})}setValue(t){this.control.setValue(t)}onSubmit(t){return this.submittedReactive.set(!0),vR(this.form,this._directives),this.ngSubmit.emit(t),t?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(t=void 0){this.form.reset(t),this.submittedReactive.set(!1)}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(t){return t.pop(),t.length?this.form.get(t):this.form}static{this.\u0275fac=function(r){return new(r||e)(u(Nn,10),u(GI,10),u(Af,8))}}static{this.\u0275dir=H({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(r,i){r&1&&Me("submit",function(s){return i.onSubmit(s)})("reset",function(){return i.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[$e([DR]),oe]})}}return e})();function BI(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function UI(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var CR=class extends uc{constructor(n=null,t,r){super(KI(t),XI(r,t)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(n),this._setUpdateStrategy(t),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),fc(t)&&(t.nonNullable||t.initialValueIsDefault)&&(UI(n)?this.defaultValue=n.value:this.defaultValue=n)}setValue(n,t={}){this.value=this._pendingValue=n,this._onChange.length&&t.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,t.emitViewToModelChange!==!1)),this.updateValueAndValidity(t)}patchValue(n,t={}){this.setValue(n,t)}reset(n=this.defaultValue,t={}){this._applyFormState(n),this.markAsPristine(t),this.markAsUntouched(t),this.setValue(this.value,t),this._pendingChange=!1}_updateValue(){}_anyControls(n){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(n){this._onChange.push(n)}_unregisterOnChange(n){BI(this._onChange,n)}registerOnDisabledChange(n){this._onDisabledChange.push(n)}_unregisterOnDisabledChange(n){BI(this._onDisabledChange,n)}_forEachChild(n){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(n){UI(n)?(this.value=this._pendingValue=n.value,n.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=n}};var bR={provide:lr,useExisting:et(()=>wR)},$I=Promise.resolve(),wR=(()=>{class e extends lr{constructor(t,r,i,o,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this.control=new CR,this._registered=!1,this.name="",this.update=new ee,this._parent=t,this._setValidators(r),this._setAsyncValidators(i),this.valueAccessor=yR(this,o)}ngOnChanges(t){if(this._checkForErrors(),!this._registered||"name"in t){if(this._registered&&(this._checkName(),this.formDirective)){let r=t.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in t&&this._updateDisabled(t),gR(t,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){JI(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(t){$I.then(()=>{this.control.setValue(t,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(t){let r=t.isDisabled.currentValue,i=r!==0&&wn(r);$I.then(()=>{i&&!this.control.disabled?this.control.disable():!i&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(t){return this._parent?lR(t,this._parent):[t]}static{this.\u0275fac=function(r){return new(r||e)(u(ci,9),u(Nn,10),u(GI,10),u(ur,10),u(D,8),u(Af,8))}}static{this.\u0275dir=H({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[$e([bR]),oe,nt]})}}return e})(),WU=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=H({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]})}}return e})();function n0(e){return typeof e=="number"?e:parseFloat(e)}var Nf=(()=>{class e{constructor(){this._validator=PI}ngOnChanges(t){if(this.inputName in t){let r=this.normalizeInput(t[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):PI,this._onChange&&this._onChange()}}validate(t){return this._validator(t)}registerOnValidatorChange(t){this._onChange=t}enabled(t){return t!=null}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=H({type:e,features:[nt]})}}return e})(),ER={provide:Nn,useExisting:et(()=>Of),multi:!0},Of=(()=>{class e extends Nf{constructor(){super(...arguments),this.inputName="max",this.normalizeInput=t=>n0(t),this.createValidator=t=>KA(t)}static{this.\u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})()}static{this.\u0275dir=H({type:e,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,i){r&2&&xt("max",i._enabled?i.max:null)},inputs:{max:"max"},features:[$e([ER]),oe]})}}return e})(),MR={provide:Nn,useExisting:et(()=>kf),multi:!0},kf=(()=>{class e extends Nf{constructor(){super(...arguments),this.inputName="min",this.normalizeInput=t=>n0(t),this.createValidator=t=>YA(t)}static{this.\u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})()}static{this.\u0275dir=H({type:e,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,i){r&2&&xt("min",i._enabled?i.min:null)},inputs:{min:"min"},features:[$e([MR]),oe]})}}return e})(),_R={provide:Nn,useExisting:et(()=>SR),multi:!0};var SR=(()=>{class e extends Nf{constructor(){super(...arguments),this.inputName="required",this.normalizeInput=wn,this.createValidator=t=>XA}enabled(t){return t}static{this.\u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})()}static{this.\u0275dir=H({type:e,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(r,i){r&2&&xt("required",i._enabled?"":null)},inputs:{required:"required"},features:[$e([_R]),oe]})}}return e})();var TR=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=gt({type:e})}static{this.\u0275inj=pt({})}}return e})();var qU=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:Af,useValue:t.callSetDisabledState??Rf}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=gt({type:e})}static{this.\u0275inj=pt({imports:[TR]})}}return e})();var YU=(e,n,t,r,i)=>AR(e[1],n[1],t[1],r[1],i).map(o=>xR(e[0],n[0],t[0],r[0],o)),xR=(e,n,t,r,i)=>{let o=3*n*Math.pow(i-1,2),s=-3*t*i+3*t+r*i,a=e*Math.pow(i-1,3);return i*(o+i*s)-a},AR=(e,n,t,r,i)=>(e-=i,n-=i,t-=i,r-=i,NR(r-3*t+3*n-e,3*t-6*n+3*e,3*n-3*e,e).filter(s=>s>=0&&s<=1)),RR=(e,n,t)=>{let r=n*n-4*e*t;return r<0?[]:[(-n+Math.sqrt(r))/(2*e),(-n-Math.sqrt(r))/(2*e)]},NR=(e,n,t,r)=>{if(e===0)return RR(n,t,r);n/=e,t/=e,r/=e;let i=(3*t-n*n)/3,o=(2*n*n*n-9*n*t+27*r)/27;if(i===0)return[Math.pow(-o,.3333333333333333)];if(o===0)return[Math.sqrt(-i),-Math.sqrt(-i)];let s=Math.pow(o/2,2)+Math.pow(i/3,3);if(s===0)return[Math.pow(o/2,.5)-n/3];if(s>0)return[Math.pow(-(o/2)+Math.sqrt(s),.3333333333333333)-Math.pow(o/2+Math.sqrt(s),.3333333333333333)-n/3];let a=Math.sqrt(Math.pow(-(i/3),3)),c=Math.acos(-(o/(2*Math.sqrt(Math.pow(-(i/3),3))))),l=2*Math.pow(a,1/3);return[l*Math.cos(c/3)-n/3,l*Math.cos((c+2*Math.PI)/3)-n/3,l*Math.cos((c+4*Math.PI)/3)-n/3]};var hc=e=>i0(e),di=(e,n)=>(typeof e=="string"&&(n=e,e=void 0),hc(e).includes(n)),i0=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let n=e.Ionic.platforms;return n==null&&(n=e.Ionic.platforms=OR(e),n.forEach(t=>e.document.documentElement.classList.add(`plt-${t}`))),n},OR=e=>{let n=He.get("platform");return Object.keys(r0).filter(t=>{let r=n?.[t];return typeof r=="function"?r(e):r0[t](e)})},kR=e=>pc(e)&&!s0(e),Pf=e=>!!(dr(e,/iPad/i)||dr(e,/Macintosh/i)&&pc(e)),PR=e=>dr(e,/iPhone/i),FR=e=>dr(e,/iPhone|iPod/i)||Pf(e),o0=e=>dr(e,/android|sink/i),jR=e=>o0(e)&&!dr(e,/mobile/i),LR=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),i=Math.max(n,t);return r>390&&r<520&&i>620&&i<800},VR=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),i=Math.max(n,t);return Pf(e)||jR(e)||r>460&&r<820&&i>780&&i<1400},pc=e=>HR(e,"(any-pointer:coarse)"),BR=e=>!pc(e),s0=e=>a0(e)||c0(e),a0=e=>!!(e.cordova||e.phonegap||e.PhoneGap),c0=e=>{let n=e.Capacitor;return!!(n?.isNative||n?.isNativePlatform&&n.isNativePlatform())},UR=e=>dr(e,/electron/i),$R=e=>{var n;return!!(!((n=e.matchMedia)===null||n===void 0)&&n.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},dr=(e,n)=>n.test(e.navigator.userAgent),HR=(e,n)=>{var t;return(t=e.matchMedia)===null||t===void 0?void 0:t.call(e,n).matches},r0={ipad:Pf,iphone:PR,ios:FR,android:o0,phablet:LR,tablet:VR,cordova:a0,capacitor:c0,electron:UR,pwa:$R,mobile:pc,mobileweb:kR,desktop:BR,hybrid:s0},ui,Ff=e=>e&&Fp(e)||ui,zR=(e={})=>{if(typeof window>"u")return;let n=window.document,t=window,r=t.Ionic=t.Ionic||{},i=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},jp(t)),{persistConfig:!1}),r.config),Vp(t)),e);He.reset(i),He.getBoolean("persistConfig")&&Lp(t,i),i0(t),r.config=He,r.mode=ui=He.get("mode",n.documentElement.getAttribute("mode")||(di(t,"ios")?"ios":"md")),He.set("mode",ui),n.documentElement.setAttribute("mode",ui),n.documentElement.classList.add(ui),He.getBoolean("_testing")&&He.set("animated",!1);let o=a=>{var c;return(c=a.tagName)===null||c===void 0?void 0:c.startsWith("ION-")},s=a=>["ios","md"].includes(a);Pp(a=>{for(;a;){let c=a.mode||a.getAttribute("mode");if(c){if(s(c))return c;o(a)&&Co('Invalid ionic mode: "'+c+'", expected: "ios" or "md"')}a=a.parentElement}return ui})};var n$=e=>{try{if(e instanceof Vf)return e.value;if(!GR()||typeof e!="string"||e==="")return e;if(e.includes("onload="))return"";let n=document.createDocumentFragment(),t=document.createElement("div");n.appendChild(t),t.innerHTML=e,qR.forEach(s=>{let a=n.querySelectorAll(s);for(let c=a.length-1;c>=0;c--){let l=a[c];l.parentNode?l.parentNode.removeChild(l):n.removeChild(l);let d=Lf(l);for(let f=0;f<d.length;f++)jf(d[f])}});let r=Lf(n);for(let s=0;s<r.length;s++)jf(r[s]);let i=document.createElement("div");i.appendChild(n);let o=i.querySelector("div");return o!==null?o.innerHTML:i.innerHTML}catch(n){return bo("sanitizeDOMString",n),""}},jf=e=>{if(e.nodeType&&e.nodeType!==1)return;if(typeof NamedNodeMap<"u"&&!(e.attributes instanceof NamedNodeMap)){e.remove();return}for(let t=e.attributes.length-1;t>=0;t--){let r=e.attributes.item(t),i=r.name;if(!WR.includes(i.toLowerCase())){e.removeAttribute(i);continue}let o=r.value,s=e[i];(o!=null&&o.toLowerCase().includes("javascript:")||s!=null&&s.toLowerCase().includes("javascript:"))&&e.removeAttribute(i)}let n=Lf(e);for(let t=0;t<n.length;t++)jf(n[t])},Lf=e=>e.children!=null?e.children:e.childNodes,GR=()=>{var e;let n=window,t=(e=n?.Ionic)===null||e===void 0?void 0:e.config;return t?t.get?t.get("sanitizerEnabled",!0):t.sanitizerEnabled===!0||t.sanitizerEnabled===void 0:!0},WR=["class","id","href","src","name","slot"],qR=["script","style","iframe","meta","link","object","embed"],Vf=class{constructor(n){this.value=n}};var r$=!1;var o$=(e,n)=>typeof e=="string"&&e.length>0?Object.assign({"ion-color":!0,[`ion-color-${e}`]:!0},n):n,ZR=e=>e!==void 0?(Array.isArray(e)?e:e.split(" ")).filter(t=>t!=null).map(t=>t.trim()).filter(t=>t!==""):[],s$=e=>{let n={};return ZR(e).forEach(t=>n[t]=!0),n};var u$=(e,n,t,r,i,o)=>ve(void 0,null,function*(){var s;if(e)return e.attachViewToDom(n,t,i,r);if(!o&&typeof t!="string"&&!(t instanceof HTMLElement))throw new Error("framework delegate is missing");let a=typeof t=="string"?(s=n.ownerDocument)===null||s===void 0?void 0:s.createElement(t):t;return r&&r.forEach(c=>a.classList.add(c)),i&&Object.assign(a,i),n.appendChild(a),yield new Promise(c=>rn(a,c)),a}),d$=(e,n)=>{if(n){if(e){let t=n.parentElement;return e.removeViewFromDom(t,n)}n.remove()}return Promise.resolve()},l0=()=>{let e,n;return{attachViewToDom:(c,l,...d)=>ve(void 0,[c,l,...d],function*(i,o,s={},a=[]){var f,p;e=i;let h;if(o){let M=typeof o=="string"?(f=e.ownerDocument)===null||f===void 0?void 0:f.createElement(o):o;a.forEach(T=>M.classList.add(T)),Object.assign(M,s),e.appendChild(M),h=M,yield new Promise(T=>rn(M,T))}else if(e.children.length>0&&(e.tagName==="ION-MODAL"||e.tagName==="ION-POPOVER")&&!(h=e.children[0]).classList.contains("ion-delegate-host")){let T=(p=e.ownerDocument)===null||p===void 0?void 0:p.createElement("div");T.classList.add("ion-delegate-host"),a.forEach(F=>T.classList.add(F)),T.append(...e.children),e.appendChild(T),h=T}let y=document.querySelector("ion-app")||document.body;return n=document.createComment("ionic teleport"),e.parentNode.insertBefore(n,e),y.appendChild(e),h??e}),removeViewFromDom:()=>(e&&n&&(n.parentNode.insertBefore(e,n),n.remove()),Promise.resolve())}};var vo='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',u0=(e,n)=>{let t=e.querySelector(vo);p0(t,n??e)},d0=(e,n)=>{let t=Array.from(e.querySelectorAll(vo)),r=t.length>0?t[t.length-1]:null;p0(r,n??e)},p0=(e,n)=>{let t=e,r=e?.shadowRoot;if(r&&(t=r.querySelector(vo)||e),t){let i=t.closest("ion-radio-group");i?i.setFocus():bc(t)}else n.focus()},Bf=0,QR=0,gc=new WeakMap,Uf=e=>({create(t){return JR(e,t)},dismiss(t,r,i){return rN(document,t,r,e,i)},getTop(){return ve(this,null,function*(){return mo(document,e)})}});var YR=Uf("ion-loading"),KR=Uf("ion-modal");var XR=Uf("ion-popover");var C$=e=>{typeof document<"u"&&nN(document);let n=Bf++;e.overlayIndex=n},b$=e=>(e.hasAttribute("id")||(e.id=`ion-overlay-${++QR}`),e.id),JR=(e,n)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(e).then(()=>{let t=document.createElement(e);return t.classList.add("overlay-hidden"),Object.assign(t,Object.assign(Object.assign({},n),{hasController:!0})),m0(document).appendChild(t),new Promise(r=>rn(t,r))}):Promise.resolve(),eN=e=>e.classList.contains("overlay-hidden"),f0=(e,n)=>{let t=e,r=e?.shadowRoot;r&&(t=r.querySelector(vo)||e),t?bc(t):n.focus()},tN=(e,n)=>{let t=mo(n,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),r=e.target;if(!t||!r||t.classList.contains(uN))return;let i=()=>{if(t===r)t.lastFocus=void 0;else if(r.tagName==="ION-TOAST")f0(t.lastFocus,t);else{let s=$p(t);if(!s.contains(r))return;let a=s.querySelector(".ion-overlay-wrapper");if(!a)return;if(a.contains(r)||r===s.querySelector("ion-backdrop"))t.lastFocus=r;else{let c=t.lastFocus;u0(a,t),c===n.activeElement&&d0(a,t),t.lastFocus=n.activeElement}}},o=()=>{if(t.contains(r))t.lastFocus=r;else if(r.tagName==="ION-TOAST")f0(t.lastFocus,t);else{let s=t.lastFocus;u0(t),s===n.activeElement&&d0(t),t.lastFocus=n.activeElement}};t.shadowRoot?o():i()},nN=e=>{Bf===0&&(Bf=1,e.addEventListener("focus",n=>{tN(n,e)},!0),e.addEventListener("ionBackButton",n=>{let t=mo(e);t?.backdropDismiss&&n.detail.register(zp,()=>{t.dismiss(void 0,h0)})}),Hp()||e.addEventListener("keydown",n=>{if(n.key==="Escape"){let t=mo(e);t?.backdropDismiss&&t.dismiss(void 0,h0)}}))},rN=(e,n,t,r,i)=>{let o=mo(e,r,i);return o?o.dismiss(n,t):Promise.reject("overlay does not exist")},iN=(e,n)=>(n===void 0&&(n="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(e.querySelectorAll(n)).filter(t=>t.overlayIndex>0)),mc=(e,n)=>iN(e,n).filter(t=>!eN(t)),mo=(e,n,t)=>{let r=mc(e,n);return t===void 0?r[r.length-1]:r.find(i=>i.id===t)},g0=(e=!1)=>{let t=m0(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");t&&(e?t.setAttribute("aria-hidden","true"):t.removeAttribute("aria-hidden"))},w$=(e,n,t,r,i)=>ve(void 0,null,function*(){var o,s;if(e.presented)return;e.el.tagName!=="ION-TOAST"&&(g0(!0),document.body.classList.add(Tc)),cN(e.el),y0(e.el),e.presented=!0,e.willPresent.emit(),(o=e.willPresentShorthand)===null||o===void 0||o.emit();let a=Ff(e),c=e.enterAnimation?e.enterAnimation:He.get(n,a==="ios"?t:r);(yield v0(e,c,e.el,i))&&(e.didPresent.emit(),(s=e.didPresentShorthand)===null||s===void 0||s.emit()),e.el.tagName!=="ION-TOAST"&&oN(e.el),e.keyboardClose&&(document.activeElement===null||!e.el.contains(document.activeElement))&&e.el.focus(),e.el.removeAttribute("aria-hidden")}),oN=e=>ve(void 0,null,function*(){let n=document.activeElement;if(!n)return;let t=n?.shadowRoot;t&&(n=t.querySelector(vo)||n),yield e.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&n.focus()}),E$=(e,n,t,r,i,o,s)=>ve(void 0,null,function*(){var a,c;if(!e.presented)return!1;let d=(nn!==void 0?mc(nn):[]).filter(p=>p.tagName!=="ION-TOAST");d.length===1&&d[0].id===e.el.id&&(g0(!1),document.body.classList.remove(Tc)),e.presented=!1;try{y0(e.el),e.el.style.setProperty("pointer-events","none"),e.willDismiss.emit({data:n,role:t}),(a=e.willDismissShorthand)===null||a===void 0||a.emit({data:n,role:t});let p=Ff(e),h=e.leaveAnimation?e.leaveAnimation:He.get(r,p==="ios"?i:o);t!==aN&&(yield v0(e,h,e.el,s)),e.didDismiss.emit({data:n,role:t}),(c=e.didDismissShorthand)===null||c===void 0||c.emit({data:n,role:t}),(gc.get(e)||[]).forEach(M=>M.destroy()),gc.delete(e),e.el.classList.add("overlay-hidden"),e.el.style.removeProperty("pointer-events"),e.el.lastFocus!==void 0&&(e.el.lastFocus=void 0)}catch(p){bo(`[${e.el.tagName.toLowerCase()}] - `,p)}return e.el.remove(),lN(),!0}),m0=e=>e.querySelector("ion-app")||e.body,v0=(e,n,t,r)=>ve(void 0,null,function*(){t.classList.remove("overlay-hidden");let i=e.el,o=n(i,r);(!e.animated||!He.getBoolean("animated",!0))&&o.duration(0),e.keyboardClose&&o.beforeAddWrite(()=>{let a=t.ownerDocument.activeElement;a?.matches("input,ion-input, ion-textarea")&&a.blur()});let s=gc.get(e)||[];return gc.set(e,[...s,o]),yield o.play(),!0}),M$=(e,n)=>{let t,r=new Promise(i=>t=i);return sN(e,n,i=>{t(i.detail)}),r},sN=(e,n,t)=>{let r=i=>{Up(e,n,r),t(i)};Bp(e,n,r)};var h0="backdrop",aN="gesture",_$=39,S$=e=>{let n=!1,t,r=l0(),i=(a=!1)=>{if(t&&!a)return{delegate:t,inline:n};let{el:c,hasController:l,delegate:d}=e;return n=c.parentNode!==null&&!l,t=n?d||r:d,{inline:n,delegate:t}};return{attachViewToDom:a=>ve(void 0,null,function*(){let{delegate:c}=i(!0);if(c)return yield c.attachViewToDom(e.el,a);let{hasController:l}=e;if(l&&a!==void 0)throw new Error("framework delegate is missing");return null}),removeViewFromDom:()=>{let{delegate:a}=i();a&&e.el!==void 0&&a.removeViewFromDom(e.el.parentElement,e.el)}}},T$=()=>{let e,n=()=>{e&&(e(),e=void 0)};return{addClickListener:(r,i)=>{n();let o=i!==void 0?document.getElementById(i):null;if(!o){Co(`[${r.tagName.toLowerCase()}] - A trigger element with the ID "${i}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,r);return}e=((a,c)=>{let l=()=>{c.present()};return a.addEventListener("click",l),()=>{a.removeEventListener("click",l)}})(o,r)},removeClickListener:n}},y0=e=>{nn!==void 0&&di("android")&&e.setAttribute("aria-hidden","true")},cN=e=>{var n;if(nn===void 0)return;let t=mc(nn);for(let r=t.length-1;r>=0;r--){let i=t[r],o=(n=t[r+1])!==null&&n!==void 0?n:e;(o.hasAttribute("aria-hidden")||o.tagName!=="ION-TOAST")&&i.setAttribute("aria-hidden","true")}},lN=()=>{if(nn===void 0)return;let e=mc(nn);for(let n=e.length-1;n>=0;n--){let t=e[n];if(t.removeAttribute("aria-hidden"),t.tagName!=="ION-TOAST")break}},uN="ion-disable-focus-trap";var dN=["tabsInner"];var b0=(()=>{class e{doc;_readyPromise;win;backButton=new ae;keyboardDidShow=new ae;keyboardDidHide=new ae;pause=new ae;resume=new ae;resize=new ae;constructor(t,r){this.doc=t,r.run(()=>{this.win=t.defaultView,this.backButton.subscribeWithPriority=function(o,s){return this.subscribe(a=>a.register(o,c=>r.run(()=>s(c))))},fi(this.pause,t,"pause",r),fi(this.resume,t,"resume",r),fi(this.backButton,t,"ionBackButton",r),fi(this.resize,this.win,"resize",r),fi(this.keyboardDidShow,this.win,"ionKeyboardDidShow",r),fi(this.keyboardDidHide,this.win,"ionKeyboardDidHide",r);let i;this._readyPromise=new Promise(o=>{i=o}),this.win?.cordova?t.addEventListener("deviceready",()=>{i("cordova")},{once:!0}):i("dom")})}is(t){return di(this.win,t)}platforms(){return hc(this.win)}ready(){return this._readyPromise}get isRTL(){return this.doc.dir==="rtl"}getQueryParam(t){return fN(this.win.location.href,t)}isLandscape(){return!this.isPortrait()}isPortrait(){return this.win.matchMedia?.("(orientation: portrait)").matches}testUserAgent(t){let r=this.win.navigator;return!!(r?.userAgent&&r.userAgent.indexOf(t)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}static \u0275fac=function(r){return new(r||e)(N(Ie),N(g))};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),fN=(e,n)=>{n=n.replace(/[[\]\\]/g,"\\$&");let r=new RegExp("[\\?&]"+n+"=([^&#]*)").exec(e);return r?decodeURIComponent(r[1].replace(/\+/g," ")):null},fi=(e,n,t,r)=>{n&&n.addEventListener(t,i=>{r.run(()=>{let o=i?.detail;e.next(o)})})},kn=(()=>{class e{location;serializer;router;topOutlet;direction=D0;animated=I0;animationBuilder;guessDirection="forward";guessAnimation;lastNavId=-1;constructor(t,r,i,o){this.location=r,this.serializer=i,this.router=o,o&&o.events.subscribe(s=>{if(s instanceof Tn){let a=s.restoredState?s.restoredState.navigationId:s.id;this.guessDirection=this.guessAnimation=a<this.lastNavId?"back":"forward",this.lastNavId=this.guessDirection==="forward"?s.id:a}}),t.backButton.subscribeWithPriority(0,s=>{this.pop(),s()})}navigateForward(t,r={}){return this.setDirection("forward",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateBack(t,r={}){return this.setDirection("back",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateRoot(t,r={}){return this.setDirection("root",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}back(t={animated:!0,animationDirection:"back"}){return this.setDirection("back",t.animated,t.animationDirection,t.animation),this.location.back()}pop(){return ve(this,null,function*(){let t=this.topOutlet;for(;t;){if(yield t.pop())return!0;t=t.parentOutlet}return!1})}setDirection(t,r,i,o){this.direction=t,this.animated=hN(t,r,i),this.animationBuilder=o}setTopOutlet(t){this.topOutlet=t}consumeTransition(){let t="root",r,i=this.animationBuilder;return this.direction==="auto"?(t=this.guessDirection,r=this.guessAnimation):(r=this.animated,t=this.direction),this.direction=D0,this.animated=I0,this.animationBuilder=void 0,{direction:t,animation:r,animationBuilder:i}}navigate(t,r){if(Array.isArray(t))return this.router.navigate(t,r);{let i=this.serializer.parse(t.toString());return r.queryParams!==void 0&&(i.queryParams=b({},r.queryParams)),r.fragment!==void 0&&(i.fragment=r.fragment),this.router.navigateByUrl(i,r)}}static \u0275fac=function(r){return new(r||e)(N(b0),N(Dt),N(cr),N(_e,8))};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),hN=(e,n,t)=>{if(n!==!1){if(t!==void 0)return t;if(e==="forward"||e==="back")return e;if(e==="root"&&n===!0)return"forward"}},D0="auto",I0=void 0,Do=(()=>{class e{get(t,r){let i=$f();return i?i.get(t,r):null}getBoolean(t,r){let i=$f();return i?i.getBoolean(t,r):!1}getNumber(t,r){let i=$f();return i?i.getNumber(t,r):0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),vc=new A("USERCONFIG"),$f=()=>{if(typeof window<"u"){let e=window.Ionic;if(e?.config)return e.config}return null},yo=class{data;constructor(n={}){this.data=n,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(n){return this.data[n]}},Pn=(()=>{class e{zone=v(g);applicationRef=v(At);config=v(vc);create(t,r,i){return new zf(t,r,this.applicationRef,this.zone,i,this.config.useSetInputAPI??!1)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})(),zf=class{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(n,t,r,i,o,s){this.environmentInjector=n,this.injector=t,this.applicationRef=r,this.zone=i,this.elementReferenceKey=o,this.enableSignalsSupport=s}attachViewToDom(n,t,r,i){return this.zone.run(()=>new Promise(o=>{let s=b({},r);this.elementReferenceKey!==void 0&&(s[this.elementReferenceKey]=n);let a=pN(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,n,t,s,i,this.elementReferenceKey,this.enableSignalsSupport);o(a)}))}removeViewFromDom(n,t){return this.zone.run(()=>new Promise(r=>{let i=this.elRefMap.get(t);if(i){i.destroy(),this.elRefMap.delete(t);let o=this.elEventsMap.get(t);o&&(o(),this.elEventsMap.delete(t))}r()}))}},pN=(e,n,t,r,i,o,s,a,c,l,d,f)=>{let p=le.create({providers:mN(c),parent:t}),h=sD(a,{environmentInjector:n,elementInjector:p}),y=h.instance,M=h.location.nativeElement;if(c)if(d&&y[d]!==void 0&&console.error(`[Ionic Error]: ${d} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${d}" property from ${a.name}.`),f===!0&&h.setInput!==void 0){let F=c,{modal:re,popover:Z}=F,me=Cc(F,["modal","popover"]);for(let Te in me)h.setInput(Te,me[Te]);re!==void 0&&Object.assign(y,{modal:re}),Z!==void 0&&Object.assign(y,{popover:Z})}else Object.assign(y,c);if(l)for(let re of l)M.classList.add(re);let T=w0(e,y,M);return s.appendChild(M),r.attachView(h.hostView),i.set(M,h),o.set(M,T),M},gN=[wc,Ec,Mc,_c,Sc],w0=(e,n,t)=>e.run(()=>{let r=gN.filter(i=>typeof n[i]=="function").map(i=>{let o=s=>n[i](s.detail);return t.addEventListener(i,o),()=>t.removeEventListener(i,o)});return()=>r.forEach(i=>i())}),C0=new A("NavParamsToken"),mN=e=>[{provide:C0,useValue:e},{provide:yo,useFactory:vN,deps:[C0]}],vN=e=>new yo(e),yN=(e,n)=>{let t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(i){this.z.runOutsideAngular(()=>this.el[r]=i)}})})},DN=(e,n)=>{let t=e.prototype;n.forEach(r=>{t[r]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,i))}})},Jf=(e,n,t)=>{t.forEach(r=>e[r]=Un(n,r))};function yc(e){return function(t){let{defineCustomElementFn:r,inputs:i,methods:o}=e;return r!==void 0&&r(),i&&yN(t,i),o&&DN(t,o),t}}var IN=["alignment","animated","arrow","keepContentsMounted","backdropDismiss","cssClass","dismissOnSelect","enterAnimation","event","focusTrap","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","translucent","trigger","triggerAction","reference","size","side"],CN=["present","dismiss","onDidDismiss","onWillDismiss"],E0=(()=>{let e=class Gf{z;template;isCmpOpen=!1;el;constructor(t,r,i){this.z=i,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),Jf(this,this.el,["ionPopoverDidPresent","ionPopoverWillPresent","ionPopoverWillDismiss","ionPopoverDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Gf)(u(D),u(m),u(g))};static \u0275dir=H({type:Gf,selectors:[["ion-popover"]],contentQueries:function(r,i,o){if(r&1&&bn(o,St,5),r&2){let s;mt(s=vt())&&(i.template=s.first)}},inputs:{alignment:"alignment",animated:"animated",arrow:"arrow",keepContentsMounted:"keepContentsMounted",backdropDismiss:"backdropDismiss",cssClass:"cssClass",dismissOnSelect:"dismissOnSelect",enterAnimation:"enterAnimation",event:"event",focusTrap:"focusTrap",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger",triggerAction:"triggerAction",reference:"reference",size:"size",side:"side"}})};return e=w([yc({inputs:IN,methods:CN})],e),e})(),bN=["animated","keepContentsMounted","backdropBreakpoint","backdropDismiss","breakpoints","canDismiss","cssClass","enterAnimation","expandToScroll","event","focusTrap","handle","handleBehavior","initialBreakpoint","isOpen","keyboardClose","leaveAnimation","mode","presentingElement","showBackdrop","translucent","trigger"],wN=["present","dismiss","onDidDismiss","onWillDismiss","setCurrentBreakpoint","getCurrentBreakpoint"],M0=(()=>{let e=class Wf{z;template;isCmpOpen=!1;el;constructor(t,r,i){this.z=i,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),Jf(this,this.el,["ionModalDidPresent","ionModalWillPresent","ionModalWillDismiss","ionModalDidDismiss","ionBreakpointDidChange","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Wf)(u(D),u(m),u(g))};static \u0275dir=H({type:Wf,selectors:[["ion-modal"]],contentQueries:function(r,i,o){if(r&1&&bn(o,St,5),r&2){let s;mt(s=vt())&&(i.template=s.first)}},inputs:{animated:"animated",keepContentsMounted:"keepContentsMounted",backdropBreakpoint:"backdropBreakpoint",backdropDismiss:"backdropDismiss",breakpoints:"breakpoints",canDismiss:"canDismiss",cssClass:"cssClass",enterAnimation:"enterAnimation",expandToScroll:"expandToScroll",event:"event",focusTrap:"focusTrap",handle:"handle",handleBehavior:"handleBehavior",initialBreakpoint:"initialBreakpoint",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",presentingElement:"presentingElement",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger"}})};return e=w([yc({inputs:bN,methods:wN})],e),e})(),EN=(e,n,t)=>t==="root"?_0(e,n):t==="forward"?MN(e,n):_N(e,n),_0=(e,n)=>(e=e.filter(t=>t.stackId!==n.stackId),e.push(n),e),MN=(e,n)=>(e.indexOf(n)>=0?e=e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):e.push(n),e),_N=(e,n)=>e.indexOf(n)>=0?e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):_0(e,n),qf=(e,n)=>{let t=e.createUrlTree(["."],{relativeTo:n});return e.serializeUrl(t)},S0=(e,n)=>n?e.stackId!==n.stackId:!0,SN=(e,n)=>{if(!e)return;let t=T0(n);for(let r=0;r<t.length;r++){if(r>=e.length)return t[r];if(t[r]!==e[r])return}},T0=e=>e.split("/").map(n=>n.trim()).filter(n=>n!==""),x0=e=>{e&&(e.ref.destroy(),e.unlistenEvents())},Zf=class{containerEl;router;navCtrl;zone;location;views=[];runningTask;skipTransition=!1;tabsPrefix;activeView;nextId=0;constructor(n,t,r,i,o,s){this.containerEl=t,this.router=r,this.navCtrl=i,this.zone=o,this.location=s,this.tabsPrefix=n!==void 0?T0(n):void 0}createView(n,t){let r=qf(this.router,t),i=n?.location?.nativeElement,o=w0(this.zone,n.instance,i);return{id:this.nextId++,stackId:SN(this.tabsPrefix,r),unlistenEvents:o,element:i,ref:n,url:r}}getExistingView(n){let t=qf(this.router,n),r=this.views.find(i=>i.url===t);return r&&r.ref.changeDetectorRef.reattach(),r}setActive(n){let t=this.navCtrl.consumeTransition(),{direction:r,animation:i,animationBuilder:o}=t,s=this.activeView,a=S0(n,s);a&&(r="back",i=void 0);let c=this.views.slice(),l,d=this.router;d.getCurrentNavigation?l=d.getCurrentNavigation():d.navigations?.value&&(l=d.navigations.value),l?.extras?.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);let f=this.views.includes(n),p=this.insertView(n,r);f||n.ref.changeDetectorRef.detectChanges();let h=n.animationBuilder;return o===void 0&&r==="back"&&!a&&h!==void 0&&(o=h),s&&(s.animationBuilder=o),this.zone.runOutsideAngular(()=>this.wait(()=>(s&&s.ref.changeDetectorRef.detach(),n.ref.changeDetectorRef.reattach(),this.transition(n,s,i,this.canGoBack(1),!1,o).then(()=>TN(n,p,c,this.location,this.zone)).then(()=>({enteringView:n,direction:r,animation:i,tabSwitch:a})))))}canGoBack(n,t=this.getActiveStackId()){return this.getStack(t).length>n}pop(n,t=this.getActiveStackId()){return this.zone.run(()=>{let r=this.getStack(t);if(r.length<=n)return Promise.resolve(!1);let i=r[r.length-n-1],o=i.url,s=i.savedData;if(s){let c=s.get("primary");c?.route?._routerState?.snapshot.url&&(o=c.route._routerState.snapshot.url)}let{animationBuilder:a}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(o,V(b({},i.savedExtras),{animation:a})).then(()=>!0)})}startBackTransition(){let n=this.activeView;if(n){let t=this.getStack(n.stackId),r=t[t.length-2],i=r.animationBuilder;return this.wait(()=>this.transition(r,n,"back",this.canGoBack(2),!0,i))}return Promise.resolve()}endBackTransition(n){n?(this.skipTransition=!0,this.pop(1)):this.activeView&&A0(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(n){let t=this.getStack(n);return t.length>0?t[t.length-1]:void 0}getRootUrl(n){let t=this.getStack(n);return t.length>0?t[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return this.runningTask!==void 0}destroy(){this.containerEl=void 0,this.views.forEach(x0),this.activeView=void 0,this.views=[]}getStack(n){return this.views.filter(t=>t.stackId===n)}insertView(n,t){return this.activeView=n,this.views=EN(this.views,n,t),this.views.slice()}transition(n,t,r,i,o,s){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(t===n)return Promise.resolve(!1);let a=n?n.element:void 0,c=t?t.element:void 0,l=this.containerEl;return a&&a!==c&&(a.classList.add("ion-page"),a.classList.add("ion-page-invisible"),l.commit)?l.commit(a,c,{duration:r===void 0?0:void 0,direction:r,showGoBack:i,progressAnimation:o,animationBuilder:s}):Promise.resolve(!1)}wait(n){return ve(this,null,function*(){this.runningTask!==void 0&&(yield this.runningTask,this.runningTask=void 0);let t=this.runningTask=n();return t.finally(()=>this.runningTask=void 0),t})}},TN=(e,n,t,r,i)=>typeof requestAnimationFrame=="function"?new Promise(o=>{requestAnimationFrame(()=>{A0(e,n,t,r,i),o()})}):Promise.resolve(),A0=(e,n,t,r,i)=>{i.run(()=>t.filter(o=>!n.includes(o)).forEach(x0)),n.forEach(o=>{let a=r.path().split("?")[0].split("#")[0];if(o!==e&&o.url!==a){let c=o.element;c.setAttribute("aria-hidden","true"),c.classList.add("ion-page-hidden"),o.ref.changeDetectorRef.detach()}})},eh=(()=>{class e{parentOutlet;nativeEl;activatedView=null;tabsPrefix;_swipeGesture;stackCtrl;proxyMap=new WeakMap;currentActivatedRoute$=new ye(null);activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=j;stackWillChange=new ee;stackDidChange=new ee;activateEvents=new ee;deactivateEvents=new ee;parentContexts=v(Rn);location=v(qe);environmentInjector=v(ue);inputBinder=v(R0,{optional:!0});supportsBindingToComponentInputs=!0;config=v(Do);navCtrl=v(kn);set animation(t){this.nativeEl.animation=t}set animated(t){this.nativeEl.animated=t}set swipeGesture(t){this._swipeGesture=t,this.nativeEl.swipeHandler=t?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:r=>this.stackCtrl.endBackTransition(r)}:void 0}constructor(t,r,i,o,s,a,c,l){this.parentOutlet=l,this.nativeEl=o.nativeElement,this.name=t||j,this.tabsPrefix=r==="true"?qf(s,c):void 0,this.stackCtrl=new Zf(this.tabsPrefix,this.nativeEl,s,this.navCtrl,a,i),this.parentContexts.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.stackCtrl.destroy(),this.inputBinder?.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){let t=this.getContext();t?.route&&this.activateWith(t.route,t.injector)}new Promise(t=>rn(this.nativeEl,t)).then(()=>{this._swipeGesture===void 0&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled",this.nativeEl.mode==="ios"))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(t,r){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){let r=this.getContext();this.activatedView.savedData=new Map(r.children.contexts);let i=this.activatedView.savedData.get("primary");if(i&&r.route&&(i.route=b({},r.route)),this.activatedView.savedExtras={},r.route){let o=r.route.snapshot;this.activatedView.savedExtras.queryParams=o.queryParams,this.activatedView.savedExtras.fragment=o.fragment}}let t=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=t;let i,o=this.stackCtrl.getExistingView(t);if(o){i=this.activated=o.ref;let a=o.savedData;if(a){let c=this.getContext();c.children.contexts=a}this.updateActivatedRouteProxy(i.instance,t)}else{let a=t._futureSnapshot,c=this.parentContexts.getOrCreateContext(this.name).children,l=new ye(null),d=this.createActivatedRouteProxy(l,t),f=new Qf(d,c,this.location.injector),p=a.routeConfig.component??a.component;i=this.activated=this.outletContent.createComponent(p,{index:this.outletContent.length,injector:f,environmentInjector:r??this.environmentInjector}),l.next(i.instance),o=this.stackCtrl.createView(this.activated,t),this.proxyMap.set(i.instance,d),this.currentActivatedRoute$.next({component:i.instance,activatedRoute:t})}this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activatedView=o,this.navCtrl.setTopOutlet(this);let s=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:o,tabSwitch:S0(o,s)}),this.stackCtrl.setActive(o).then(a=>{this.activateEvents.emit(i.instance),this.stackDidChange.emit(a)})}canGoBack(t=1,r){return this.stackCtrl.canGoBack(t,r)}pop(t=1,r){return this.stackCtrl.pop(t,r)}getLastUrl(t){let r=this.stackCtrl.getLastUrl(t);return r?r.url:void 0}getLastRouteView(t){return this.stackCtrl.getLastUrl(t)}getRootView(t){return this.stackCtrl.getRootUrl(t)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(t,r){let i=new Le;return i._futureSnapshot=r._futureSnapshot,i._routerState=r._routerState,i.snapshot=r.snapshot,i.outlet=r.outlet,i.component=r.component,i._paramMap=this.proxyObservable(t,"paramMap"),i._queryParamMap=this.proxyObservable(t,"queryParamMap"),i.url=this.proxyObservable(t,"url"),i.params=this.proxyObservable(t,"params"),i.queryParams=this.proxyObservable(t,"queryParams"),i.fragment=this.proxyObservable(t,"fragment"),i.data=this.proxyObservable(t,"data"),i}proxyObservable(t,r){return t.pipe(Ae(i=>!!i),we(i=>this.currentActivatedRoute$.pipe(Ae(o=>o!==null&&o.component===i),we(o=>o&&o.activatedRoute[r]),Qc())))}updateActivatedRouteProxy(t,r){let i=this.proxyMap.get(t);if(!i)throw new Error("Could not find activated route proxy for view");i._futureSnapshot=r._futureSnapshot,i._routerState=r._routerState,i.snapshot=r.snapshot,i.outlet=r.outlet,i.component=r.component,this.currentActivatedRoute$.next({component:t,activatedRoute:r})}static \u0275fac=function(r){return new(r||e)(Gt("name"),Gt("tabs"),u(Dt),u(m),u(_e),u(g),u(Le),u(e,12))};static \u0275dir=H({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"]})}return e})(),Qf=class{route;childContexts;parent;constructor(n,t,r){this.route=n,this.childContexts=t,this.parent=r}get(n,t){return n===Le?this.route:n===Rn?this.childContexts:this.parent.get(n,t)}},R0=new A(""),xN=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,i=Bn([r.queryParams,r.params,r.data]).pipe(we(([o,s,a],c)=>(a=b(b(b({},o),s),a),c===0?O(a):Promise.resolve(a)))).subscribe(o=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=ya(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,o[a])});this.outletDataSubscriptions.set(t,i)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})(),N0=()=>({provide:R0,useFactory:AN,deps:[_e]});function AN(e){return e?.componentInputBindingEnabled?new xN:null}var RN=["color","defaultHref","disabled","icon","mode","routerAnimation","text","type"],O0=(()=>{let e=class Yf{routerOutlet;navCtrl;config;r;z;el;constructor(t,r,i,o,s,a){this.routerOutlet=t,this.navCtrl=r,this.config=i,this.r=o,this.z=s,a.detach(),this.el=this.r.nativeElement}onClick(t){let r=this.defaultHref||this.config.get("backButtonDefaultHref");this.routerOutlet?.canGoBack()?(this.navCtrl.setDirection("back",void 0,void 0,this.routerAnimation),this.routerOutlet.pop(),t.preventDefault()):r!=null&&(this.navCtrl.navigateBack(r,{animation:this.routerAnimation}),t.preventDefault())}static \u0275fac=function(r){return new(r||Yf)(u(eh,8),u(kn),u(Do),u(m),u(g),u(D))};static \u0275dir=H({type:Yf,hostBindings:function(r,i){r&1&&Me("click",function(s){return i.onClick(s)})},inputs:{color:"color",defaultHref:"defaultHref",disabled:"disabled",icon:"icon",mode:"mode",routerAnimation:"routerAnimation",text:"text",type:"type"}})};return e=w([yc({inputs:RN})],e),e})(),k0=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,i,o,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=i,this.router=o,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref(),this.updateTabindex()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTabindex(){let t=["ION-BACK-BUTTON","ION-BREADCRUMB","ION-BUTTON","ION-CARD","ION-FAB-BUTTON","ION-ITEM","ION-ITEM-OPTION","ION-MENU-BUTTON","ION-SEGMENT-BUTTON","ION-TAB-BUTTON"],r=this.elementRef.nativeElement;t.includes(r.tagName)&&r.getAttribute("tabindex")==="0"&&r.removeAttribute("tabindex")}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(t){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation),t.preventDefault()}static \u0275fac=function(r){return new(r||e)(u(Qe),u(kn),u(m),u(_e),u(Mf,8))};static \u0275dir=H({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(r,i){r&1&&Me("click",function(s){return i.onClick(s)})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},features:[nt]})}return e})(),P0=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,i,o,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=i,this.router=o,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation)}static \u0275fac=function(r){return new(r||e)(u(Qe),u(kn),u(m),u(_e),u(Mf,8))};static \u0275dir=H({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],hostBindings:function(r,i){r&1&&Me("click",function(){return i.onClick()})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},features:[nt]})}return e})(),NN=["animated","animation","root","rootParams","swipeGesture"],ON=["push","insert","insertPages","pop","popTo","popToRoot","removeIndex","setRoot","setPages","getActive","getByIndex","canGoBack","getPrevious"],F0=(()=>{let e=class Kf{z;el;constructor(t,r,i,o,s,a){this.z=s,a.detach(),this.el=t.nativeElement,t.nativeElement.delegate=o.create(r,i),Jf(this,this.el,["ionNavDidChange","ionNavWillChange"])}static \u0275fac=function(r){return new(r||Kf)(u(m),u(ue),u(le),u(Pn),u(g),u(D))};static \u0275dir=H({type:Kf,inputs:{animated:"animated",animation:"animation",root:"root",rootParams:"rootParams",swipeGesture:"swipeGesture"}})};return e=w([yc({inputs:NN,methods:ON})],e),e})(),j0=(()=>{class e{navCtrl;tabsInner;ionTabsWillChange=new ee;ionTabsDidChange=new ee;tabBarSlot="bottom";hasTab=!1;selectedTab;leavingTab;constructor(t){this.navCtrl=t}ngAfterViewInit(){let t=this.tabs.length>0?this.tabs.first:void 0;t&&(this.hasTab=!0,this.setActiveTab(t.tab),this.tabSwitch())}ngAfterContentInit(){this.detectSlotChanges()}ngAfterContentChecked(){this.detectSlotChanges()}onStackWillChange({enteringView:t,tabSwitch:r}){let i=t.stackId;r&&i!==void 0&&this.ionTabsWillChange.emit({tab:i})}onStackDidChange({enteringView:t,tabSwitch:r}){let i=t.stackId;r&&i!==void 0&&(this.tabBar&&(this.tabBar.selectedTab=i),this.ionTabsDidChange.emit({tab:i}))}select(t){let r=typeof t=="string",i=r?t:t.detail.tab;if(this.hasTab){this.setActiveTab(i),this.tabSwitch();return}let o=this.outlet.getActiveStackId()===i,s=`${this.outlet.tabsPrefix}/${i}`;if(r||t.stopPropagation(),o){let a=this.outlet.getActiveStackId();if(this.outlet.getLastRouteView(a)?.url===s)return;let l=this.outlet.getRootView(i),d=l&&s===l.url&&l.savedExtras;return this.navCtrl.navigateRoot(s,V(b({},d),{animated:!0,animationDirection:"back"}))}else{let a=this.outlet.getLastRouteView(i),c=a?.url||s,l=a?.savedExtras;return this.navCtrl.navigateRoot(c,V(b({},l),{animated:!0,animationDirection:"back"}))}}setActiveTab(t){let i=this.tabs.find(o=>o.tab===t);if(!i){console.error(`[Ionic Error]: Tab with id: "${t}" does not exist`);return}this.leavingTab=this.selectedTab,this.selectedTab=i,this.ionTabsWillChange.emit({tab:t}),i.el.active=!0}tabSwitch(){let{selectedTab:t,leavingTab:r}=this;this.tabBar&&t&&(this.tabBar.selectedTab=t.tab),r?.tab!==t?.tab&&r?.el&&(r.el.active=!1),t&&this.ionTabsDidChange.emit({tab:t.tab})}getSelected(){return this.hasTab?this.selectedTab?.tab:this.outlet.getActiveStackId()}detectSlotChanges(){this.tabBars.forEach(t=>{let r=t.el.getAttribute("slot");r!==this.tabBarSlot&&(this.tabBarSlot=r,this.relocateTabBar())})}relocateTabBar(){let t=this.tabBar.el;this.tabBarSlot==="top"?this.tabsInner.nativeElement.before(t):this.tabsInner.nativeElement.after(t)}static \u0275fac=function(r){return new(r||e)(u(kn))};static \u0275dir=H({type:e,selectors:[["ion-tabs"]],viewQuery:function(r,i){if(r&1&&Pi(dN,7,m),r&2){let o;mt(o=vt())&&(i.tabsInner=o.first)}},hostBindings:function(r,i){r&1&&Me("ionTabButtonClick",function(s){return i.select(s)})},outputs:{ionTabsWillChange:"ionTabsWillChange",ionTabsDidChange:"ionTabsDidChange"}})}return e})(),th=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),Io=(()=>{class e{injector;elementRef;onChange=()=>{};onTouched=()=>{};lastValue;statusChanges;constructor(t,r){this.injector=t,this.elementRef=r}writeValue(t){this.elementRef.nativeElement.value=this.lastValue=t,fr(this.elementRef)}handleValueChange(t,r){t===this.elementRef.nativeElement&&(r!==this.lastValue&&(this.lastValue=r,this.onChange(r)),fr(this.elementRef))}_handleBlurEvent(t){t===this.elementRef.nativeElement?(this.onTouched(),fr(this.elementRef)):t.closest("ion-radio-group")===this.elementRef.nativeElement&&this.onTouched()}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.elementRef.nativeElement.disabled=t}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let t;try{t=this.injector.get(lr)}catch{}if(!t)return;t.statusChanges&&(this.statusChanges=t.statusChanges.subscribe(()=>fr(this.elementRef)));let r=t.control;r&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(o=>{if(typeof r[o]<"u"){let s=r[o].bind(r);r[o]=(...a)=>{s(...a),fr(this.elementRef)}}})}static \u0275fac=function(r){return new(r||e)(u(le),u(m))};static \u0275dir=H({type:e,hostBindings:function(r,i){r&1&&Me("ionBlur",function(s){return i._handleBlurEvent(s.target)})}})}return e})(),fr=e=>{th(()=>{let n=e.nativeElement,t=n.value!=null&&n.value.toString().length>0,r=kN(n);Hf(n,r);let i=n.closest("ion-item");i&&(t?Hf(i,[...r,"item-has-value"]):Hf(i,r))})},kN=e=>{let n=e.classList,t=[];for(let r=0;r<n.length;r++){let i=n.item(r);i!==null&&PN(i,"ng-")&&t.push(`ion-${i.substring(3)}`)}return t},Hf=(e,n)=>{let t=e.classList;t.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),t.add(...n)},PN=(e,n)=>e.substring(0,n.length)===n,Xf=class{shouldDetach(n){return!1}shouldAttach(n){return!1}store(n,t){}retrieve(n){return null}shouldReuseRoute(n,t){if(n.routeConfig!==t.routeConfig)return!1;let r=n.params,i=t.params,o=Object.keys(r),s=Object.keys(i);if(o.length!==s.length)return!1;for(let a of o)if(i[a]!==r[a])return!1;return!0}},On=class{ctrl;constructor(n){this.ctrl=n}create(n){return this.ctrl.create(n||{})}dismiss(n,t,r){return this.ctrl.dismiss(n,t,r)}getTop(){return this.ctrl.getTop()}};function V0(){var e=[];if(typeof window<"u"){var n=window;(!n.customElements||n.Element&&(!n.Element.prototype.closest||!n.Element.prototype.matches||!n.Element.prototype.remove||!n.Element.prototype.getRootNode))&&e.push(import("./chunk-5X4HMWFG.js"));var t=function(){try{var r=new URL("b","http://a");return r.pathname="c%20d",r.href==="http://a/c%20d"&&r.searchParams}catch{return!1}};(typeof Object.assign!="function"||!Object.entries||!Array.prototype.find||!Array.prototype.includes||!String.prototype.startsWith||!String.prototype.endsWith||n.NodeList&&!n.NodeList.prototype.forEach||!n.fetch||!t()||typeof WeakMap>"u")&&e.push(import("./chunk-LQX7KJ2R.js"))}return Promise.all(e)}var B0=xc;var U0=(e,n)=>ve(void 0,null,function*(){if(!(typeof window>"u"))return yield B0(),Gp(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-input-password-toggle",[[33,"ion-input-password-toggle",{"color":[513],"showIcon":[1,"show-icon"],"hideIcon":[1,"hide-icon"],"type":[1025]},null,{"type":["onTypeChange"]}]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"contentId":[513,"content-id"],"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[16,"ionSegmentViewScroll","handleSegmentViewScroll"],[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-input",[[38,"ion-input",{"color":[513],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearInputIcon":[1,"clear-input-icon"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16],"debounce":[2],"disabled":[516],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[516],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"type":["onTypeChange"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64],"getLength":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16],"beforeLeave":[16],"beforeEnter":[16]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"step":["stepChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-segment-content",[[1,"ion-segment-content"]]],["ion-segment-view",[[33,"ion-segment-view",{"disabled":[4],"isManualScroll":[32],"setContent":[64]},[[1,"scroll","handleScroll"],[1,"touchstart","handleScrollStart"],[1,"touchend","handleTouchEnd"]]]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32],"isVisible":[64]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-select-modal",[[34,"ion-select-modal",{"header":[1],"multiple":[4],"options":[16]}]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16],"readonly":[4],"isDateEnabled":[16],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16],"multiple":[4],"highlightedDates":[16],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker-legacy",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-legacy-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"breakpoints":[16],"expandToScroll":[4,"expand-to-scroll"],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"focusTrap":[4,"focus-trap"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-picker",[[33,"ion-picker",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-picker-column",[[1,"ion-picker-column",{"disabled":[4],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"ariaLabel":[32],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64],"setFocus":[64]},null,{"aria-label":["ariaLabelChanged"],"value":["valueChange"]}]]],["ion-picker-column-option",[[33,"ion-picker-column-option",{"disabled":[4],"value":[8],"color":[513],"ariaLabel":[32]},null,{"aria-label":["onAriaLabelChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16],"leaveAnimation":[16],"component":[1],"componentProps":[16],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"focusTrap":[4,"focus-trap"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"setFocus":[64]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[33,"ion-note",{"color":[513]}],[1,"ion-skeleton-text",{"animated":[4]}],[38,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[33,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[516],"download":[1],"href":[1],"rel":[1],"lines":[1],"routerAnimation":[16],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"multipleInputs":[32],"focusable":[32]},[[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"fixedSlotPlacement":[1,"fixed-slot-placement"],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[38,"ion-buttons",{"collapse":[4]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"required":[4],"isExpanded":[32],"hasFocus":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"]}],[36,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032],"helperText":[1,"helper-text"],"errorText":[1,"error-text"],"setFocus":[64]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1],"isCircle":[32]},null,{"disabled":["disabledChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]]]'),n)});var _=["*"],qN=["outletContent"],ZN=["outlet"],QN=[[["","slot","top"]],"*",[["ion-tab"]]],YN=["[slot=top]","*","ion-tab"];function KN(e,n){if(e&1){let t=Qy();Gr(0,"ion-router-outlet",5,1),Me("stackWillChange",function(i){Ru(t);let o=ki();return Nu(o.onStackWillChange(i))})("stackDidChange",function(i){Ru(t);let o=ki();return Nu(o.onStackDidChange(i))}),Wr()}}function XN(e,n){e&1&&C(0,2,["*ngIf","tabs.length > 0"])}function JN(e,n){if(e&1&&(Gr(0,"div",1),pa(1,2),Wr()),e&2){let t=ki();aa(),Cn("ngTemplateOutlet",t.template)}}function eO(e,n){if(e&1&&pa(0,1),e&2){let t=ki();Cn("ngTemplateOutlet",t.template)}}var tO=(()=>{class e extends Io{constructor(t,r){super(t,r)}writeValue(t){this.elementRef.nativeElement.checked=this.lastValue=t,fr(this.elementRef)}_handleIonChange(t){this.handleValueChange(t,t.checked)}static \u0275fac=function(r){return new(r||e)(u(le),u(m))};static \u0275dir=H({type:e,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(r,i){r&1&&Me("ionChange",function(s){return i._handleIonChange(s.target)})},features:[$e([{provide:ur,useExisting:e,multi:!0}]),oe]})}return e})(),nO=(()=>{class e extends Io{el;constructor(t,r){super(t,r),this.el=r}handleInputEvent(t){this.handleValueChange(t,t.value)}registerOnChange(t){this.el.nativeElement.tagName==="ION-INPUT"?super.registerOnChange(r=>{t(r===""?null:parseFloat(r))}):super.registerOnChange(t)}static \u0275fac=function(r){return new(r||e)(u(le),u(m))};static \u0275dir=H({type:e,selectors:[["ion-input","type","number"],["ion-range"]],hostBindings:function(r,i){r&1&&Me("ionInput",function(s){return i.handleInputEvent(s.target)})},features:[$e([{provide:ur,useExisting:e,multi:!0}]),oe]})}return e})(),rO=(()=>{class e extends Io{constructor(t,r){super(t,r)}_handleChangeEvent(t){this.handleValueChange(t,t.value)}static \u0275fac=function(r){return new(r||e)(u(le),u(m))};static \u0275dir=H({type:e,selectors:[["ion-select"],["ion-radio-group"],["ion-segment"],["ion-datetime"]],hostBindings:function(r,i){r&1&&Me("ionChange",function(s){return i._handleChangeEvent(s.target)})},features:[$e([{provide:ur,useExisting:e,multi:!0}]),oe]})}return e})(),iO=(()=>{class e extends Io{constructor(t,r){super(t,r)}_handleInputEvent(t){this.handleValueChange(t,t.value)}static \u0275fac=function(r){return new(r||e)(u(le),u(m))};static \u0275dir=H({type:e,selectors:[["ion-input",3,"type","number"],["ion-textarea"],["ion-searchbar"]],hostBindings:function(r,i){r&1&&Me("ionInput",function(s){return i._handleInputEvent(s.target)})},features:[$e([{provide:ur,useExisting:e,multi:!0}]),oe]})}return e})(),oO=(e,n)=>{let t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(i){this.z.runOutsideAngular(()=>this.el[r]=i)},configurable:!0})})},sO=(e,n)=>{let t=e.prototype;n.forEach(r=>{t[r]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,i))}})},Y=(e,n,t)=>{t.forEach(r=>e[r]=Un(n,r))};function S(e){return function(t){let{defineCustomElementFn:r,inputs:i,methods:o}=e;return r!==void 0&&r(),i&&oO(t,i),o&&sO(t,o),t}}var aO=(()=>{let e=class nh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||nh)(u(D),u(m),u(g))};static \u0275cmp=I({type:nh,selectors:[["ion-accordion"]],inputs:{disabled:"disabled",mode:"mode",readonly:"readonly",toggleIcon:"toggleIcon",toggleIconSlot:"toggleIconSlot",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["disabled","mode","readonly","toggleIcon","toggleIconSlot","value"]})],e),e})(),cO=(()=>{let e=class rh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||rh)(u(D),u(m),u(g))};static \u0275cmp=I({type:rh,selectors:[["ion-accordion-group"]],inputs:{animated:"animated",disabled:"disabled",expand:"expand",mode:"mode",multiple:"multiple",readonly:"readonly",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["animated","disabled","expand","mode","multiple","readonly","value"]})],e),e})(),lO=(()=>{let e=class ih{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionActionSheetDidPresent","ionActionSheetWillPresent","ionActionSheetWillDismiss","ionActionSheetDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||ih)(u(D),u(m),u(g))};static \u0275cmp=I({type:ih,selectors:[["ion-action-sheet"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),uO=(()=>{let e=class oh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionAlertDidPresent","ionAlertWillPresent","ionAlertWillDismiss","ionAlertDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||oh)(u(D),u(m),u(g))};static \u0275cmp=I({type:oh,selectors:[["ion-alert"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",inputs:"inputs",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","inputs","isOpen","keyboardClose","leaveAnimation","message","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),dO=(()=>{let e=class sh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||sh)(u(D),u(m),u(g))};static \u0275cmp=I({type:sh,selectors:[["ion-app"]],ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({methods:["setFocus"]})],e),e})(),fO=(()=>{let e=class ah{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ah)(u(D),u(m),u(g))};static \u0275cmp=I({type:ah,selectors:[["ion-avatar"]],ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({})],e),e})(),hO=(()=>{let e=class ch{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionBackdropTap"])}static \u0275fac=function(r){return new(r||ch)(u(D),u(m),u(g))};static \u0275cmp=I({type:ch,selectors:[["ion-backdrop"]],inputs:{stopPropagation:"stopPropagation",tappable:"tappable",visible:"visible"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["stopPropagation","tappable","visible"]})],e),e})(),pO=(()=>{let e=class lh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||lh)(u(D),u(m),u(g))};static \u0275cmp=I({type:lh,selectors:[["ion-badge"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","mode"]})],e),e})(),gO=(()=>{let e=class uh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||uh)(u(D),u(m),u(g))};static \u0275cmp=I({type:uh,selectors:[["ion-breadcrumb"]],inputs:{active:"active",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",separator:"separator",target:"target"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["active","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","separator","target"]})],e),e})(),mO=(()=>{let e=class dh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionCollapsedClick"])}static \u0275fac=function(r){return new(r||dh)(u(D),u(m),u(g))};static \u0275cmp=I({type:dh,selectors:[["ion-breadcrumbs"]],inputs:{color:"color",itemsAfterCollapse:"itemsAfterCollapse",itemsBeforeCollapse:"itemsBeforeCollapse",maxItems:"maxItems",mode:"mode"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","itemsAfterCollapse","itemsBeforeCollapse","maxItems","mode"]})],e),e})(),vO=(()=>{let e=class fh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||fh)(u(D),u(m),u(g))};static \u0275cmp=I({type:fh,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],e),e})(),yO=(()=>{let e=class hh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||hh)(u(D),u(m),u(g))};static \u0275cmp=I({type:hh,selectors:[["ion-buttons"]],inputs:{collapse:"collapse"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["collapse"]})],e),e})(),DO=(()=>{let e=class ph{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ph)(u(D),u(m),u(g))};static \u0275cmp=I({type:ph,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),IO=(()=>{let e=class gh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||gh)(u(D),u(m),u(g))};static \u0275cmp=I({type:gh,selectors:[["ion-card-content"]],inputs:{mode:"mode"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["mode"]})],e),e})(),CO=(()=>{let e=class mh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||mh)(u(D),u(m),u(g))};static \u0275cmp=I({type:mh,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","mode","translucent"]})],e),e})(),bO=(()=>{let e=class vh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||vh)(u(D),u(m),u(g))};static \u0275cmp=I({type:vh,selectors:[["ion-card-subtitle"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","mode"]})],e),e})(),wO=(()=>{let e=class yh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||yh)(u(D),u(m),u(g))};static \u0275cmp=I({type:yh,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","mode"]})],e),e})(),EO=(()=>{let e=class Dh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||Dh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Dh,selectors:[["ion-checkbox"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",errorText:"errorText",helperText:"helperText",indeterminate:"indeterminate",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["alignment","checked","color","disabled","errorText","helperText","indeterminate","justify","labelPlacement","mode","name","required","value"]})],e),e})(),MO=(()=>{let e=class Ih{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ih)(u(D),u(m),u(g))};static \u0275cmp=I({type:Ih,selectors:[["ion-chip"]],inputs:{color:"color",disabled:"disabled",mode:"mode",outline:"outline"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","disabled","mode","outline"]})],e),e})(),_O=(()=>{let e=class Ch{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ch)(u(D),u(m),u(g))};static \u0275cmp=I({type:Ch,selectors:[["ion-col"]],inputs:{offset:"offset",offsetLg:"offsetLg",offsetMd:"offsetMd",offsetSm:"offsetSm",offsetXl:"offsetXl",offsetXs:"offsetXs",pull:"pull",pullLg:"pullLg",pullMd:"pullMd",pullSm:"pullSm",pullXl:"pullXl",pullXs:"pullXs",push:"push",pushLg:"pushLg",pushMd:"pushMd",pushSm:"pushSm",pushXl:"pushXl",pushXs:"pushXs",size:"size",sizeLg:"sizeLg",sizeMd:"sizeMd",sizeSm:"sizeSm",sizeXl:"sizeXl",sizeXs:"sizeXs"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["offset","offsetLg","offsetMd","offsetSm","offsetXl","offsetXs","pull","pullLg","pullMd","pullSm","pullXl","pullXs","push","pushLg","pushMd","pushSm","pushXl","pushXs","size","sizeLg","sizeMd","sizeSm","sizeXl","sizeXs"]})],e),e})(),SO=(()=>{let e=class bh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}static \u0275fac=function(r){return new(r||bh)(u(D),u(m),u(g))};static \u0275cmp=I({type:bh,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],e),e})(),TO=(()=>{let e=class wh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionCancel","ionChange","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||wh)(u(D),u(m),u(g))};static \u0275cmp=I({type:wh,selectors:[["ion-datetime"]],inputs:{cancelText:"cancelText",clearText:"clearText",color:"color",dayValues:"dayValues",disabled:"disabled",doneText:"doneText",firstDayOfWeek:"firstDayOfWeek",formatOptions:"formatOptions",highlightedDates:"highlightedDates",hourCycle:"hourCycle",hourValues:"hourValues",isDateEnabled:"isDateEnabled",locale:"locale",max:"max",min:"min",minuteValues:"minuteValues",mode:"mode",monthValues:"monthValues",multiple:"multiple",name:"name",preferWheel:"preferWheel",presentation:"presentation",readonly:"readonly",showClearButton:"showClearButton",showDefaultButtons:"showDefaultButtons",showDefaultTimeLabel:"showDefaultTimeLabel",showDefaultTitle:"showDefaultTitle",size:"size",titleSelectedDatesFormatter:"titleSelectedDatesFormatter",value:"value",yearValues:"yearValues"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["cancelText","clearText","color","dayValues","disabled","doneText","firstDayOfWeek","formatOptions","highlightedDates","hourCycle","hourValues","isDateEnabled","locale","max","min","minuteValues","mode","monthValues","multiple","name","preferWheel","presentation","readonly","showClearButton","showDefaultButtons","showDefaultTimeLabel","showDefaultTitle","size","titleSelectedDatesFormatter","value","yearValues"],methods:["confirm","reset","cancel"]})],e),e})(),xO=(()=>{let e=class Eh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Eh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Eh,selectors:[["ion-datetime-button"]],inputs:{color:"color",datetime:"datetime",disabled:"disabled",mode:"mode"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","datetime","disabled","mode"]})],e),e})(),AO=(()=>{let e=class Mh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Mh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Mh,selectors:[["ion-fab"]],inputs:{activated:"activated",edge:"edge",horizontal:"horizontal",vertical:"vertical"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["activated","edge","horizontal","vertical"],methods:["close"]})],e),e})(),RO=(()=>{let e=class _h{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||_h)(u(D),u(m),u(g))};static \u0275cmp=I({type:_h,selectors:[["ion-fab-button"]],inputs:{activated:"activated",closeIcon:"closeIcon",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",show:"show",size:"size",target:"target",translucent:"translucent",type:"type"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["activated","closeIcon","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","show","size","target","translucent","type"]})],e),e})(),NO=(()=>{let e=class Sh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Sh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Sh,selectors:[["ion-fab-list"]],inputs:{activated:"activated",side:"side"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["activated","side"]})],e),e})(),OO=(()=>{let e=class Th{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Th)(u(D),u(m),u(g))};static \u0275cmp=I({type:Th,selectors:[["ion-footer"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["collapse","mode","translucent"]})],e),e})(),kO=(()=>{let e=class xh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||xh)(u(D),u(m),u(g))};static \u0275cmp=I({type:xh,selectors:[["ion-grid"]],inputs:{fixed:"fixed"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["fixed"]})],e),e})(),PO=(()=>{let e=class Ah{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ah)(u(D),u(m),u(g))};static \u0275cmp=I({type:Ah,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["collapse","mode","translucent"]})],e),e})(),FO=(()=>{let e=class Rh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Rh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Rh,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],e),e})(),jO=(()=>{let e=class Nh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionImgWillLoad","ionImgDidLoad","ionError"])}static \u0275fac=function(r){return new(r||Nh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Nh,selectors:[["ion-img"]],inputs:{alt:"alt",src:"src"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["alt","src"]})],e),e})(),LO=(()=>{let e=class Oh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionInfinite"])}static \u0275fac=function(r){return new(r||Oh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Oh,selectors:[["ion-infinite-scroll"]],inputs:{disabled:"disabled",position:"position",threshold:"threshold"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["disabled","position","threshold"],methods:["complete"]})],e),e})(),VO=(()=>{let e=class kh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||kh)(u(D),u(m),u(g))};static \u0275cmp=I({type:kh,selectors:[["ion-infinite-scroll-content"]],inputs:{loadingSpinner:"loadingSpinner",loadingText:"loadingText"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["loadingSpinner","loadingText"]})],e),e})(),BO=(()=>{let e=class Ph{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}static \u0275fac=function(r){return new(r||Ph)(u(D),u(m),u(g))};static \u0275cmp=I({type:Ph,selectors:[["ion-input"]],inputs:{autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearInputIcon:"clearInputIcon",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearInputIcon","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","spellcheck","step","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),UO=(()=>{let e=class Fh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Fh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Fh,selectors:[["ion-input-password-toggle"]],inputs:{color:"color",hideIcon:"hideIcon",mode:"mode",showIcon:"showIcon"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","hideIcon","mode","showIcon"]})],e),e})(),$O=(()=>{let e=class jh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||jh)(u(D),u(m),u(g))};static \u0275cmp=I({type:jh,selectors:[["ion-item"]],inputs:{button:"button",color:"color",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["button","color","detail","detailIcon","disabled","download","href","lines","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),HO=(()=>{let e=class Lh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Lh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Lh,selectors:[["ion-item-divider"]],inputs:{color:"color",mode:"mode",sticky:"sticky"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","mode","sticky"]})],e),e})(),zO=(()=>{let e=class Vh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Vh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Vh,selectors:[["ion-item-group"]],ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({})],e),e})(),GO=(()=>{let e=class Bh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Bh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Bh,selectors:[["ion-item-option"]],inputs:{color:"color",disabled:"disabled",download:"download",expandable:"expandable",href:"href",mode:"mode",rel:"rel",target:"target",type:"type"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","disabled","download","expandable","href","mode","rel","target","type"]})],e),e})(),WO=(()=>{let e=class Uh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionSwipe"])}static \u0275fac=function(r){return new(r||Uh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Uh,selectors:[["ion-item-options"]],inputs:{side:"side"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["side"]})],e),e})(),qO=(()=>{let e=class $h{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionDrag"])}static \u0275fac=function(r){return new(r||$h)(u(D),u(m),u(g))};static \u0275cmp=I({type:$h,selectors:[["ion-item-sliding"]],inputs:{disabled:"disabled"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["disabled"],methods:["getOpenAmount","getSlidingRatio","open","close","closeOpened"]})],e),e})(),ZO=(()=>{let e=class Hh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Hh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Hh,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","mode","position"]})],e),e})(),QO=(()=>{let e=class zh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||zh)(u(D),u(m),u(g))};static \u0275cmp=I({type:zh,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],e),e})(),YO=(()=>{let e=class Gh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Gh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Gh,selectors:[["ion-list-header"]],inputs:{color:"color",lines:"lines",mode:"mode"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","lines","mode"]})],e),e})(),KO=(()=>{let e=class Wh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionLoadingDidPresent","ionLoadingWillPresent","ionLoadingWillDismiss","ionLoadingDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Wh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Wh,selectors:[["ion-loading"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",showBackdrop:"showBackdrop",spinner:"spinner",translucent:"translucent",trigger:"trigger"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["animated","backdropDismiss","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","message","mode","showBackdrop","spinner","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),XO=(()=>{let e=class qh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionWillOpen","ionWillClose","ionDidOpen","ionDidClose"])}static \u0275fac=function(r){return new(r||qh)(u(D),u(m),u(g))};static \u0275cmp=I({type:qh,selectors:[["ion-menu"]],inputs:{contentId:"contentId",disabled:"disabled",maxEdgeStart:"maxEdgeStart",menuId:"menuId",side:"side",swipeGesture:"swipeGesture",type:"type"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["contentId","disabled","maxEdgeStart","menuId","side","swipeGesture","type"],methods:["isOpen","isActive","open","close","toggle","setOpen"]})],e),e})(),JO=(()=>{let e=class Zh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Zh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Zh,selectors:[["ion-menu-button"]],inputs:{autoHide:"autoHide",color:"color",disabled:"disabled",menu:"menu",mode:"mode",type:"type"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["autoHide","color","disabled","menu","mode","type"]})],e),e})(),e1=(()=>{let e=class Qh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Qh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Qh,selectors:[["ion-menu-toggle"]],inputs:{autoHide:"autoHide",menu:"menu"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["autoHide","menu"]})],e),e})(),t1=(()=>{let e=class Yh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Yh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Yh,selectors:[["ion-nav-link"]],inputs:{component:"component",componentProps:"componentProps",routerAnimation:"routerAnimation",routerDirection:"routerDirection"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["component","componentProps","routerAnimation","routerDirection"]})],e),e})(),n1=(()=>{let e=class Kh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Kh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Kh,selectors:[["ion-note"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","mode"]})],e),e})(),r1=(()=>{let e=class Xh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Xh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Xh,selectors:[["ion-picker"]],inputs:{mode:"mode"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["mode"]})],e),e})(),i1=(()=>{let e=class Jh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||Jh)(u(D),u(m),u(g))};static \u0275cmp=I({type:Jh,selectors:[["ion-picker-column"]],inputs:{color:"color",disabled:"disabled",mode:"mode",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","disabled","mode","value"],methods:["setFocus"]})],e),e})(),o1=(()=>{let e=class ep{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ep)(u(D),u(m),u(g))};static \u0275cmp=I({type:ep,selectors:[["ion-picker-column-option"]],inputs:{color:"color",disabled:"disabled",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","disabled","value"]})],e),e})(),s1=(()=>{let e=class tp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionPickerDidPresent","ionPickerWillPresent","ionPickerWillDismiss","ionPickerDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||tp)(u(D),u(m),u(g))};static \u0275cmp=I({type:tp,selectors:[["ion-picker-legacy"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",columns:"columns",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",trigger:"trigger"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["animated","backdropDismiss","buttons","columns","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss","getColumn"]})],e),e})(),a1=(()=>{let e=class np{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||np)(u(D),u(m),u(g))};static \u0275cmp=I({type:np,selectors:[["ion-progress-bar"]],inputs:{buffer:"buffer",color:"color",mode:"mode",reversed:"reversed",type:"type",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["buffer","color","mode","reversed","type","value"]})],e),e})(),c1=(()=>{let e=class rp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||rp)(u(D),u(m),u(g))};static \u0275cmp=I({type:rp,selectors:[["ion-radio"]],inputs:{alignment:"alignment",color:"color",disabled:"disabled",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["alignment","color","disabled","justify","labelPlacement","mode","name","value"]})],e),e})(),l1=(()=>{let e=class ip{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||ip)(u(D),u(m),u(g))};static \u0275cmp=I({type:ip,selectors:[["ion-radio-group"]],inputs:{allowEmptySelection:"allowEmptySelection",compareWith:"compareWith",errorText:"errorText",helperText:"helperText",name:"name",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["allowEmptySelection","compareWith","errorText","helperText","name","value"]})],e),e})(),u1=(()=>{let e=class op{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionInput","ionFocus","ionBlur","ionKnobMoveStart","ionKnobMoveEnd"])}static \u0275fac=function(r){return new(r||op)(u(D),u(m),u(g))};static \u0275cmp=I({type:op,selectors:[["ion-range"]],inputs:{activeBarStart:"activeBarStart",color:"color",debounce:"debounce",disabled:"disabled",dualKnobs:"dualKnobs",label:"label",labelPlacement:"labelPlacement",max:"max",min:"min",mode:"mode",name:"name",pin:"pin",pinFormatter:"pinFormatter",snaps:"snaps",step:"step",ticks:"ticks",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["activeBarStart","color","debounce","disabled","dualKnobs","label","labelPlacement","max","min","mode","name","pin","pinFormatter","snaps","step","ticks","value"]})],e),e})(),d1=(()=>{let e=class sp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionRefresh","ionPull","ionStart"])}static \u0275fac=function(r){return new(r||sp)(u(D),u(m),u(g))};static \u0275cmp=I({type:sp,selectors:[["ion-refresher"]],inputs:{closeDuration:"closeDuration",disabled:"disabled",mode:"mode",pullFactor:"pullFactor",pullMax:"pullMax",pullMin:"pullMin",snapbackDuration:"snapbackDuration"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["closeDuration","disabled","mode","pullFactor","pullMax","pullMin","snapbackDuration"],methods:["complete","cancel","getProgress"]})],e),e})(),f1=(()=>{let e=class ap{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ap)(u(D),u(m),u(g))};static \u0275cmp=I({type:ap,selectors:[["ion-refresher-content"]],inputs:{pullingIcon:"pullingIcon",pullingText:"pullingText",refreshingSpinner:"refreshingSpinner",refreshingText:"refreshingText"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["pullingIcon","pullingText","refreshingSpinner","refreshingText"]})],e),e})(),h1=(()=>{let e=class cp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||cp)(u(D),u(m),u(g))};static \u0275cmp=I({type:cp,selectors:[["ion-reorder"]],ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({})],e),e})(),p1=(()=>{let e=class lp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionItemReorder"])}static \u0275fac=function(r){return new(r||lp)(u(D),u(m),u(g))};static \u0275cmp=I({type:lp,selectors:[["ion-reorder-group"]],inputs:{disabled:"disabled"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["disabled"],methods:["complete"]})],e),e})(),g1=(()=>{let e=class up{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||up)(u(D),u(m),u(g))};static \u0275cmp=I({type:up,selectors:[["ion-ripple-effect"]],inputs:{type:"type"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["type"],methods:["addRipple"]})],e),e})(),m1=(()=>{let e=class dp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||dp)(u(D),u(m),u(g))};static \u0275cmp=I({type:dp,selectors:[["ion-row"]],ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({})],e),e})(),v1=(()=>{let e=class fp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionInput","ionChange","ionCancel","ionClear","ionBlur","ionFocus"])}static \u0275fac=function(r){return new(r||fp)(u(D),u(m),u(g))};static \u0275cmp=I({type:fp,selectors:[["ion-searchbar"]],inputs:{animated:"animated",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",cancelButtonIcon:"cancelButtonIcon",cancelButtonText:"cancelButtonText",clearIcon:"clearIcon",color:"color",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",inputmode:"inputmode",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",searchIcon:"searchIcon",showCancelButton:"showCancelButton",showClearButton:"showClearButton",spellcheck:"spellcheck",type:"type",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["animated","autocapitalize","autocomplete","autocorrect","cancelButtonIcon","cancelButtonText","clearIcon","color","debounce","disabled","enterkeyhint","inputmode","maxlength","minlength","mode","name","placeholder","searchIcon","showCancelButton","showClearButton","spellcheck","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),y1=(()=>{let e=class hp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||hp)(u(D),u(m),u(g))};static \u0275cmp=I({type:hp,selectors:[["ion-segment"]],inputs:{color:"color",disabled:"disabled",mode:"mode",scrollable:"scrollable",selectOnFocus:"selectOnFocus",swipeGesture:"swipeGesture",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","disabled","mode","scrollable","selectOnFocus","swipeGesture","value"]})],e),e})(),D1=(()=>{let e=class pp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||pp)(u(D),u(m),u(g))};static \u0275cmp=I({type:pp,selectors:[["ion-segment-button"]],inputs:{contentId:"contentId",disabled:"disabled",layout:"layout",mode:"mode",type:"type",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["contentId","disabled","layout","mode","type","value"]})],e),e})(),I1=(()=>{let e=class gp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||gp)(u(D),u(m),u(g))};static \u0275cmp=I({type:gp,selectors:[["ion-segment-content"]],ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({})],e),e})(),C1=(()=>{let e=class mp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionSegmentViewScroll"])}static \u0275fac=function(r){return new(r||mp)(u(D),u(m),u(g))};static \u0275cmp=I({type:mp,selectors:[["ion-segment-view"]],inputs:{disabled:"disabled"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["disabled"]})],e),e})(),b1=(()=>{let e=class vp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionCancel","ionDismiss","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||vp)(u(D),u(m),u(g))};static \u0275cmp=I({type:vp,selectors:[["ion-select"]],inputs:{cancelText:"cancelText",color:"color",compareWith:"compareWith",disabled:"disabled",errorText:"errorText",expandedIcon:"expandedIcon",fill:"fill",helperText:"helperText",interface:"interface",interfaceOptions:"interfaceOptions",justify:"justify",label:"label",labelPlacement:"labelPlacement",mode:"mode",multiple:"multiple",name:"name",okText:"okText",placeholder:"placeholder",required:"required",selectedText:"selectedText",shape:"shape",toggleIcon:"toggleIcon",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["cancelText","color","compareWith","disabled","errorText","expandedIcon","fill","helperText","interface","interfaceOptions","justify","label","labelPlacement","mode","multiple","name","okText","placeholder","required","selectedText","shape","toggleIcon","value"],methods:["open"]})],e),e})(),w1=(()=>{let e=class yp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||yp)(u(D),u(m),u(g))};static \u0275cmp=I({type:yp,selectors:[["ion-select-modal"]],inputs:{header:"header",multiple:"multiple",options:"options"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["header","multiple","options"]})],e),e})(),E1=(()=>{let e=class Dp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Dp)(u(D),u(m),u(g))};static \u0275cmp=I({type:Dp,selectors:[["ion-select-option"]],inputs:{disabled:"disabled",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["disabled","value"]})],e),e})(),M1=(()=>{let e=class Ip{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ip)(u(D),u(m),u(g))};static \u0275cmp=I({type:Ip,selectors:[["ion-skeleton-text"]],inputs:{animated:"animated"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["animated"]})],e),e})(),_1=(()=>{let e=class Cp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Cp)(u(D),u(m),u(g))};static \u0275cmp=I({type:Cp,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","duration","name","paused"]})],e),e})(),S1=(()=>{let e=class bp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionSplitPaneVisible"])}static \u0275fac=function(r){return new(r||bp)(u(D),u(m),u(g))};static \u0275cmp=I({type:bp,selectors:[["ion-split-pane"]],inputs:{contentId:"contentId",disabled:"disabled",when:"when"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["contentId","disabled","when"]})],e),e})(),$0=(()=>{let e=class wp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||wp)(u(D),u(m),u(g))};static \u0275cmp=I({type:wp,selectors:[["ion-tab"]],inputs:{component:"component",tab:"tab"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["component","tab"],methods:["setActive"]})],e),e})(),Ep=(()=>{let e=class Mp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Mp)(u(D),u(m),u(g))};static \u0275cmp=I({type:Mp,selectors:[["ion-tab-bar"]],inputs:{color:"color",mode:"mode",selectedTab:"selectedTab",translucent:"translucent"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","mode","selectedTab","translucent"]})],e),e})(),T1=(()=>{let e=class _p{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||_p)(u(D),u(m),u(g))};static \u0275cmp=I({type:_p,selectors:[["ion-tab-button"]],inputs:{disabled:"disabled",download:"download",href:"href",layout:"layout",mode:"mode",rel:"rel",selected:"selected",tab:"tab",target:"target"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["disabled","download","href","layout","mode","rel","selected","tab","target"]})],e),e})(),x1=(()=>{let e=class Sp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Sp)(u(D),u(m),u(g))};static \u0275cmp=I({type:Sp,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","mode"]})],e),e})(),A1=(()=>{let e=class Tp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}static \u0275fac=function(r){return new(r||Tp)(u(D),u(m),u(g))};static \u0275cmp=I({type:Tp,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],e),e})(),R1=(()=>{let e=class xp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||xp)(u(D),u(m),u(g))};static \u0275cmp=I({type:xp,selectors:[["ion-thumbnail"]],ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({})],e),e})(),N1=(()=>{let e=class Ap{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ap)(u(D),u(m),u(g))};static \u0275cmp=I({type:Ap,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","size"]})],e),e})(),O1=(()=>{let e=class Rp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionToastDidPresent","ionToastWillPresent","ionToastWillDismiss","ionToastDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Rp)(u(D),u(m),u(g))};static \u0275cmp=I({type:Rp,selectors:[["ion-toast"]],inputs:{animated:"animated",buttons:"buttons",color:"color",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",icon:"icon",isOpen:"isOpen",keyboardClose:"keyboardClose",layout:"layout",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",position:"position",positionAnchor:"positionAnchor",swipeGesture:"swipeGesture",translucent:"translucent",trigger:"trigger"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["animated","buttons","color","cssClass","duration","enterAnimation","header","htmlAttributes","icon","isOpen","keyboardClose","layout","leaveAnimation","message","mode","position","positionAnchor","swipeGesture","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),k1=(()=>{let e=class Np{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||Np)(u(D),u(m),u(g))};static \u0275cmp=I({type:Np,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",errorText:"errorText",helperText:"helperText",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["alignment","checked","color","disabled","enableOnOffLabels","errorText","helperText","justify","labelPlacement","mode","name","required","value"]})],e),e})(),P1=(()=>{let e=class Op{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Op)(u(D),u(m),u(g))};static \u0275cmp=I({type:Op,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([S({inputs:["color","mode"]})],e),e})(),Dc=(()=>{class e extends eh{parentOutlet;outletContent;constructor(t,r,i,o,s,a,c,l){super(t,r,i,o,s,a,c,l),this.parentOutlet=l}static \u0275fac=function(r){return new(r||e)(Gt("name"),Gt("tabs"),u(Dt),u(m),u(_e),u(g),u(Le),u(e,12))};static \u0275cmp=I({type:e,selectors:[["ion-router-outlet"]],viewQuery:function(r,i){if(r&1&&Pi(qN,7,qe),r&2){let o;mt(o=vt())&&(i.outletContent=o.first)}},features:[oe],ngContentSelectors:_,decls:3,vars:0,consts:[["outletContent",""]],template:function(r,i){r&1&&(E(),fa(0,null,0),C(2),ha())},encapsulation:2})}return e})(),F1=(()=>{class e extends j0{outlet;tabBar;tabBars;tabs;static \u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})();static \u0275cmp=I({type:e,selectors:[["ion-tabs"]],contentQueries:function(r,i,o){if(r&1&&(bn(o,Ep,5),bn(o,Ep,4),bn(o,$0,4)),r&2){let s;mt(s=vt())&&(i.tabBar=s.first),mt(s=vt())&&(i.tabBars=s),mt(s=vt())&&(i.tabs=s)}},viewQuery:function(r,i){if(r&1&&Pi(ZN,5,Dc),r&2){let o;mt(o=vt())&&(i.outlet=o.first)}},features:[oe],ngContentSelectors:YN,decls:6,vars:2,consts:[["tabsInner",""],["outlet",""],[1,"tabs-inner"],["tabs","true",3,"stackWillChange","stackDidChange",4,"ngIf"],[4,"ngIf"],["tabs","true",3,"stackWillChange","stackDidChange"]],template:function(r,i){r&1&&(E(QN),C(0),Gr(1,"div",2,0),Oi(3,KN,2,0,"ion-router-outlet",3)(4,XN,1,0,"ng-content",4),Wr(),C(5,1)),r&2&&(aa(3),Cn("ngIf",i.tabs.length===0),aa(),Cn("ngIf",i.tabs.length>0))},dependencies:[Ra,Dc],styles:["[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}"]})}return e})(),j1=(()=>{class e extends O0{constructor(t,r,i,o,s,a){super(t,r,i,o,s,a)}static \u0275fac=function(r){return new(r||e)(u(Dc,8),u(kn),u(Do),u(m),u(g),u(D))};static \u0275cmp=I({type:e,selectors:[["ion-back-button"]],features:[oe],ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})}return e})(),L1=(()=>{class e extends F0{constructor(t,r,i,o,s,a){super(t,r,i,o,s,a)}static \u0275fac=function(r){return new(r||e)(u(m),u(ue),u(le),u(Pn),u(g),u(D))};static \u0275cmp=I({type:e,selectors:[["ion-nav"]],features:[oe],ngContentSelectors:_,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})}return e})(),V1=(()=>{class e extends k0{static \u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})();static \u0275dir=H({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],features:[oe]})}return e})(),B1=(()=>{class e extends P0{static \u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})();static \u0275dir=H({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],features:[oe]})}return e})(),U1=(()=>{class e extends M0{static \u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})();static \u0275cmp=I({type:e,selectors:[["ion-modal"]],features:[oe],decls:1,vars:1,consts:[["class","ion-delegate-host ion-page",4,"ngIf"],[1,"ion-delegate-host","ion-page"],[3,"ngTemplateOutlet"]],template:function(r,i){r&1&&Oi(0,JN,2,1,"div",0),r&2&&Cn("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[Ra,Sd],encapsulation:2,changeDetection:0})}return e})(),$1=(()=>{class e extends E0{static \u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})();static \u0275cmp=I({type:e,selectors:[["ion-popover"]],features:[oe],decls:1,vars:1,consts:[[3,"ngTemplateOutlet",4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(r,i){r&1&&Oi(0,eO,1,1,"ng-container",0),r&2&&Cn("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[Ra,Sd],encapsulation:2,changeDetection:0})}return e})(),H1={provide:Nn,useExisting:et(()=>H0),multi:!0},H0=(()=>{class e extends Of{static \u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})();static \u0275dir=H({type:e,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,i){r&2&&xt("max",i._enabled?i.max:null)},features:[$e([H1]),oe]})}return e})(),z1={provide:Nn,useExisting:et(()=>z0),multi:!0},z0=(()=>{class e extends kf{static \u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})();static \u0275dir=H({type:e,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,i){r&2&&xt("min",i._enabled?i.min:null)},features:[$e([z1]),oe]})}return e})(),vz=(()=>{class e extends On{constructor(){super(Ac)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),yz=(()=>{class e{create(t){return wo(t)}easingTime(t,r,i,o,s){return Eo(t,r,i,o,s)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Dz=(()=>{class e extends On{constructor(){super(Rc)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var G1=(()=>{class e extends On{angularDelegate=v(Pn);injector=v(le);environmentInjector=v(ue);constructor(){super(Nc)}create(t){return super.create(V(b({},t),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})();var kp=class extends On{angularDelegate=v(Pn);injector=v(le);environmentInjector=v(ue);constructor(){super(Oc)}create(n){return super.create(V(b({},n),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},Iz=(()=>{class e extends On{constructor(){super(kc)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),W1=(e,n,t)=>()=>{let r=n.defaultView;if(r&&typeof window<"u"){Pc(V(b({},e),{_zoneGate:o=>t.run(o)}));let i="__zone_symbol__addEventListener"in n.body?"__zone_symbol__addEventListener":"addEventListener";return V0().then(()=>U0(r,{exclude:["ion-tabs"],syncQueue:!0,raf:th,jmp:o=>t.runOutsideAngular(o),ael(o,s,a,c){o[i](s,a,c)},rel(o,s,a,c){o.removeEventListener(s,a,c)}}))}},q1=[aO,cO,lO,uO,dO,fO,hO,pO,gO,mO,vO,yO,DO,IO,CO,bO,wO,EO,MO,_O,SO,TO,xO,AO,RO,NO,OO,kO,PO,FO,jO,LO,VO,BO,UO,$O,HO,zO,GO,WO,qO,ZO,QO,YO,KO,XO,JO,e1,t1,n1,r1,i1,o1,s1,a1,c1,l1,u1,d1,f1,h1,p1,g1,m1,v1,y1,D1,I1,C1,b1,w1,E1,M1,_1,S1,$0,Ep,T1,x1,A1,R1,N1,O1,k1,P1],Cz=[...q1,U1,$1,tO,nO,rO,iO,F1,Dc,j1,L1,V1,B1,z0,H0],bz=(()=>{class e{static forRoot(t={}){return{ngModule:e,providers:[{provide:vc,useValue:t},{provide:qr,useFactory:W1,multi:!0,deps:[vc,Ie,g]},Pn,N0()]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=gt({type:e});static \u0275inj=pt({providers:[G1,kp],imports:[Td]})}return e})();export{ye as a,w as b,Ir as c,Q0 as d,aC as e,X0 as f,kt as g,MC as h,x as i,pt as j,N as k,v as l,I as m,gt as n,xi as o,Db as p,ue as q,Ru as r,Nu as s,Gt as t,le as u,ee as v,g as w,Ut as x,m as y,BB as z,nE as A,aa as B,u as C,qe as D,oe as E,Oi as F,Cn as G,Hy as H,da as I,zB as J,Gr as K,Wr as L,fd as M,fa as N,ha as O,Qy as P,Me as Q,ki as R,E as S,C as T,W_ as U,Pi as V,mt as W,vt as X,GB as Y,Q_ as Z,Xy as _,Y_ as $,K_ as aa,WB as ba,X_ as ca,eD as da,qB as ea,ZB as fa,QB as ga,qr as ha,D as ia,Ie as ja,Dt as ka,v2 as la,y2 as ma,Ra as na,D2 as oa,Td as pa,Jt as qa,uT as ra,vT as sa,N2 as ta,O2 as ua,iU as va,Le as wa,EA as xa,_e as ya,bU as za,wU as Aa,FA as Ba,EU as Ca,MU as Da,YU as Ea,di as Fa,Ff as Ga,zR as Ha,n$ as Ia,r$ as Ja,o$ as Ka,s$ as La,u$ as Ma,d$ as Na,l0 as Oa,u0 as Pa,YR as Qa,KR as Ra,XR as Sa,C$ as Ta,b$ as Ua,w$ as Va,E$ as Wa,M$ as Xa,h0 as Ya,aN as Za,_$ as _a,S$ as $a,T$ as ab,uN as bb,HU as cb,zU as db,IR as eb,wR as fb,WU as gb,SR as hb,qU as ib,b0 as jb,vc as kb,Pn as lb,yc as mb,eh as nb,N0 as ob,Xf as pb,On as qb,tO as rb,rO as sb,iO as tb,pO as ub,vO as vb,yO as wb,DO as xb,IO as yb,CO as zb,wO as Ab,EO as Bb,MO as Cb,SO as Db,AO as Eb,RO as Fb,OO as Gb,PO as Hb,FO as Ib,BO as Jb,$O as Kb,ZO as Lb,QO as Mb,n1 as Nb,d1 as Ob,f1 as Pb,v1 as Qb,y1 as Rb,D1 as Sb,_1 as Tb,Ep as Ub,T1 as Vb,x1 as Wb,N1 as Xb,P1 as Yb,F1 as Zb,j1 as _b,U1 as $b,vz as ac,yz as bc,Dz as cc,G1 as dc,Iz as ec,bz as fc};
