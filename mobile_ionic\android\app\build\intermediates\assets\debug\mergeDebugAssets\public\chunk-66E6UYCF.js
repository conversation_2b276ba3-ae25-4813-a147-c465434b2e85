import{a as q}from"./chunk-W4VSVZEO.js";import{a as $}from"./chunk-5WVTKRNS.js";import"./chunk-EXGJLNXY.js";import{a as r}from"./chunk-WPPT3EJF.js";import"./chunk-2GT6F2KJ.js";import"./chunk-2LL5MXLB.js";import{B as b,C as l,Db as U,Jb as B,K as c,L as g,M as P,Q as C,Y as h,aa as w,ac as R,ba as M,ca as y,cb as O,d as p,da as x,db as S,e as k,eb as I,ec as z,fb as F,fc as W,gb as E,hb as L,i as v,ib as A,jb as f,k as m,m as _,ra as u,tb as j,vb as N,ya as T}from"./chunk-E442IOFQ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{g as o}from"./chunk-2R6CW7ES.js";var D=(()=>{class s{constructor(e,t,n){this.http=e,this.platform=t,this.toastCtrl=n}checkBackendConnectivity(){return o(this,null,function*(){try{console.log("Checking backend connectivity to:",r.apiUrl);let e=yield p(this.http.get(`${r.apiUrl.replace("/api","")}/api/test`).pipe(k(1e4)));console.log("Backend test endpoint successful:",e);let t=yield p(this.http.get(`${r.apiUrl}/evacuation-centers`).pipe(k(1e4)));return console.log("Backend connectivity check successful"),!0}catch(e){if(console.error("Backend connectivity check failed:",e),console.error("Error details:",{status:e.status,statusText:e.statusText,url:e.url,message:e.message}),e.status===0)console.log("Backend server not reachable - offline mode available");else return console.log("Backend is reachable but returned an error:",e.status),!0;return!1}})}checkMapboxConnectivity(){return o(this,null,function*(){try{console.log("Checking Mapbox connectivity...");let e=`https://api.mapbox.com/directions/v5/mapbox/walking/121.7740,12.8797;121.7750,12.8807?access_token=${r.mapboxAccessToken}&overview=simplified`,t=yield p(this.http.get(e));return console.log("Mapbox connectivity check successful"),!0}catch(e){return console.error("Mapbox connectivity check failed:",e),e.status===0?this.showConnectivityError("Mapbox","Cannot connect to Mapbox API. Please check your internet connection."):e.status===401?this.showConnectivityError("Mapbox","Invalid Mapbox access token. Please check your token."):e.status===403?this.showConnectivityError("Mapbox","Mapbox access denied. Please check your token permissions."):e.status===429&&this.showConnectivityError("Mapbox","Too many requests to Mapbox. Please wait and try again."),!1}})}checkNetworkConnectivity(){return o(this,null,function*(){console.log("Starting comprehensive network connectivity check...");let e={backend:!1,routing:!1};return e.backend=yield this.checkBackendConnectivity(),e.routing=yield this.checkMapboxConnectivity(),console.log("Network connectivity check results:",e),e})}showConnectivityError(e,t){return o(this,null,function*(){yield(yield this.toastCtrl.create({header:`${e} Connection Error`,message:t,duration:5e3,color:"danger",buttons:[{text:"Dismiss",role:"cancel"}]})).present()})}pingTest(){return o(this,null,function*(){try{let e=yield p(this.http.get("https://httpbin.org/get"));return!0}catch(e){return console.error("Ping test failed:",e),!1}})}getNetworkInfo(){return this.platform.is("capacitor")?{platform:"mobile",userAgent:navigator.userAgent}:{platform:"web",online:navigator.onLine,userAgent:navigator.userAgent}}static{this.\u0275fac=function(t){return new(t||s)(m(u),m(f),m(z))}}static{this.\u0275prov=v({token:s,factory:s.\u0275fac,providedIn:"root"})}}return s})();var ae=(()=>{class s{goToRegister(){this.router.navigate(["/register"])}constructor(e,t,n,i,a,d,V){this.router=e,this.authService=t,this.http=n,this.alertController=i,this.platform=a,this.networkService=d,this.fcmService=V,this.credentials={email:"",password:""},this.errorMessage="",this.fcmToken="",this.fcmTokenReady=!1}ngOnInit(){return o(this,null,function*(){console.log("\u{1F525} Login page initializing..."),yield this.initializeFCMToken()})}registerTokenWithEndpoint(e,t,n,i){if(localStorage.getItem("fcm_token")===this.fcmToken){console.log("Token already registered, skipping registration"),n&&n();return}if(localStorage.getItem("fcm_token_registering")==="true"){console.log("Token registration already in progress, skipping"),n&&n();return}localStorage.setItem("fcm_token_registering","true"),this.http.post(e,t).subscribe({next:d=>{console.log(`FCM token registered with ${e}:`,d),localStorage.setItem("fcm_token",this.fcmToken),localStorage.removeItem("fcm_token_registering"),n&&n()},error:d=>{console.error(`Error registering token with ${e}:`,d),localStorage.removeItem("fcm_token_registering"),i&&i()}})}onLogin(){return o(this,null,function*(){if(!this.credentials.email||!this.credentials.password){yield this.presentAlert("Login Failed","Please enter both email and password.");return}if(console.log("\u{1F510} Login attempt started"),console.log("\u{1F4E7} Email:",this.credentials.email),console.log("\u{1F310} API URL:",r.apiUrl),console.log("\u{1F4F1} Platform:",this.platform.is("android")?"Android":this.platform.is("ios")?"iOS":"Browser"),console.log("\u{1F30D} Network status:",navigator.onLine?"Online":"Offline"),console.log("\u{1F525} FCM Token ready:",this.fcmTokenReady,"Token:",this.fcmToken?this.fcmToken.substring(0,20)+"...":"None"),console.log("\u{1F525} FCM functionality temporarily disabled"),console.log("\u{1F9EA} Testing API connectivity..."),!(yield this.networkService.checkBackendConnectivity())){yield this.presentConnectionErrorAlert();return}this.authService.login(this.credentials).subscribe({next:t=>o(this,null,function*(){console.log("\u2705 Login successful:",t),yield this.presentSuccessAlert("Login Successful","Welcome, "+(t.user.first_name||t.user.email)),this.authService.setToken(t.token),localStorage.setItem("user",JSON.stringify(t.user));let n=!1;t.onboarding_complete!==void 0&&(n=t.onboarding_complete,localStorage.setItem("onboardingComplete",n?"true":"false"),console.log("\u{1F3AF} Server onboarding status:",n)),n?(console.log("\u2705 User has completed onboarding \u2192 navigating to tabs/home"),this.router.navigate(["/tabs/home"])):(console.log("\u2705 User needs onboarding \u2192 navigating to welcome"),this.router.navigate(["/welcome"])),this.registerFCMTokenInBackground(t.user.id)}),error:t=>{if(console.error("\u274C Login error:",t),console.error("\u{1F4CA} Error details:",{status:t.status,statusText:t.statusText,message:t.message,url:t.url,error:t.error}),this.errorMessage=t.error?.message||"Login failed",t.status===0)this.presentAlert("Network Error",`Cannot connect to the server. Please check:
\u2022 Your internet connection
\u2022 If you're on the same WiFi network as the server
\u2022 If the backend server is running

Server URL: ${r.apiUrl}`);else if(t.status===401){let n=t.error?.message||"The email and password didn't match";this.presentAlert("Login Failed",n)}else t.status===404?this.presentAlert("Server Error","Login endpoint not found. Please check server configuration."):t.status>=500?this.presentAlert("Server Error","The server encountered an error. Please try again later."):this.presentAlert("Login Error",`An error occurred during login (${t.status}).

Please check the console for more details or try again later.`)}})})}presentAlert(e,t){return o(this,null,function*(){yield(yield this.alertController.create({header:e,message:t,buttons:["OK"],cssClass:"login-alert"})).present()})}presentSuccessAlert(e,t){return o(this,null,function*(){yield(yield this.alertController.create({header:e,message:t,buttons:["OK"],cssClass:"login-success-alert"})).present()})}presentConnectionErrorAlert(){return o(this,null,function*(){yield(yield this.alertController.create({header:"Connection Error",message:`Cannot connect to the server at ${r.apiUrl}.

Please check:
\u2022 Your internet connection
\u2022 If the backend server is running
\u2022 If you're on the same network as the server`,buttons:[{text:"Retry",handler:()=>{this.onLogin()}}],cssClass:"connection-error-alert"})).present()})}initializeFCMToken(){return o(this,null,function*(){try{this.platform.is("capacitor")?(console.log("Getting FCM token..."),this.fcmToken=yield this.fcmService.getFCMToken(),this.fcmToken?(this.fcmTokenReady=!0,console.log("FCM token ready:",this.fcmToken)):console.log("No FCM token available")):console.log("FCM not available on this platform")}catch(e){console.error("Error initializing FCM token:",e)}})}registerFCMTokenInBackground(e){return o(this,null,function*(){try{console.log("\u{1F525} Starting background FCM token registration..."),this.fcmTokenReady&&this.fcmToken?(yield this.registerTokenWithEndpoints({token:this.fcmToken,device_type:"android",project_id:r.firebase.projectId,user_id:e}),console.log("\u2705 FCM token registered successfully in background")):(console.log("\u26A0\uFE0F FCM token not ready, attempting to get token in background..."),yield this.initializeFCMToken(),this.fcmTokenReady&&this.fcmToken?(yield this.registerTokenWithEndpoints({token:this.fcmToken,device_type:"android",project_id:r.firebase.projectId,user_id:e}),console.log("\u2705 FCM token registered successfully in background after retry")):console.log("\u274C Failed to get FCM token in background"))}catch(t){console.error("\u274C Error registering FCM token in background:",t)}})}registerTokenWithEndpoints(e){return o(this,null,function*(){e.project_id||(e.project_id=r.firebase.projectId);let t=[`${r.apiUrl}/device-token`,`${r.apiUrl}/device-token/register`];for(let n of t)try{let i=yield this.http.post(n,e).toPromise();console.log(`FCM token registered with ${n}:`,i),localStorage.setItem("fcm_token",this.fcmToken);break}catch(i){console.error(`Error registering token with ${n}:`,i)}})}static{this.\u0275fac=function(t){return new(t||s)(l(T),l(q),l(u),l(R),l(f),l(D),l($))}}static{this.\u0275cmp=_({type:s,selectors:[["app-login"]],standalone:!0,features:[x],decls:17,vars:2,consts:[[1,"login-bg"],[1,"login-wrapper"],[1,"logo-container"],["src","assets/ALERTO.png"],[1,"login-form",3,"ngSubmit"],[1,"input-group"],["type","email","name","email","placeholder","Username","required","",1,"modern-input",3,"ngModelChange","ngModel"],["type","password","name","password","placeholder","Password","required","",1,"modern-input",3,"ngModelChange","ngModel"],["expand","block","type","submit",1,"modern-btn"],[1,"register-link"],[3,"click"]],template:function(t,n){t&1&&(c(0,"ion-content",0)(1,"div",1)(2,"div",2),P(3,"img",3),g(),c(4,"form",4),C("ngSubmit",function(){return n.onLogin()}),c(5,"div",5)(6,"ion-input",6),y("ngModelChange",function(a){return M(n.credentials.email,a)||(n.credentials.email=a),a}),g()(),c(7,"div",5)(8,"ion-input",7),y("ngModelChange",function(a){return M(n.credentials.password,a)||(n.credentials.password=a),a}),g()(),c(9,"ion-button",8),h(10," Sign In "),g()(),c(11,"div",9),h(12," Don't have an account? "),c(13,"a",10),C("click",function(){return n.goToRegister()}),c(14,"strong")(15,"u"),h(16,"Sign Up"),g()()()()()()),t&2&&(b(6),w("ngModel",n.credentials.email),b(2),w("ngModel",n.credentials.password))},dependencies:[W,N,U,B,j,A,E,O,S,L,F,I],styles:[".login-bg[_ngcontent-%COMP%]{--background: white;display:flex;align-items:center;justify-content:center;min-height:100vh}.login-wrapper[_ngcontent-%COMP%]{width:100%;max-width:350px;padding:2rem;margin:0 auto;display:flex;flex-direction:column;align-items:flex-start;text-align:left}.logo-container[_ngcontent-%COMP%]{margin-bottom:3rem;align-self:center}.app-logo[_ngcontent-%COMP%]{width:120px;height:120px;border-radius:50%;background:#fff3;padding:20px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);box-shadow:0 8px 32px #0000001a}.login-form[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column;gap:1.5rem}.input-group[_ngcontent-%COMP%]{width:100%}.modern-input[_ngcontent-%COMP%]{--background: transparent;--color: black;--placeholder-color: rgba(0, 0, 0, .7);--padding-start: 0;--padding-end: 0;border:none;border-bottom:1px solid rgba(0,0,0,.5);border-radius:0;font-size:1rem;padding:12px 0}.modern-input[_ngcontent-%COMP%]:focus-within{border-bottom-color:#000}.modern-btn[_ngcontent-%COMP%]{--background: #007bff;--color: white;--border-radius: 25px;--box-shadow: 0 4px 12px rgba(0, 0, 0, .15);font-weight:600;font-size:1.1rem;height:50px;width:100%;margin-top:1rem}.register-link[_ngcontent-%COMP%]{margin-top:1.5rem;color:#000;font-size:1rem}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#007bff;text-decoration:underline;font-weight:600}.troubleshooting-section[_ngcontent-%COMP%]{margin-top:24px;padding-top:16px;border-top:1px solid var(--ion-color-light)}.troubleshooting-section[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-bottom:8px}.login-alert[_ngcontent-%COMP%]{--backdrop-opacity: .8}.login-alert[_ngcontent-%COMP%]   .alert-wrapper[_ngcontent-%COMP%]{border-radius:15px;box-shadow:0 4px 16px #0003}.login-alert[_ngcontent-%COMP%]   .alert-head[_ngcontent-%COMP%]{padding-bottom:10px}.login-alert[_ngcontent-%COMP%]   .alert-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;color:#d9534f}.login-alert[_ngcontent-%COMP%]   .alert-message[_ngcontent-%COMP%]{font-size:1rem;color:#333}.login-alert[_ngcontent-%COMP%]   .alert-button[_ngcontent-%COMP%]{color:#3880ff;font-weight:500}.login-success-alert[_ngcontent-%COMP%]{--backdrop-opacity: .8}.login-success-alert[_ngcontent-%COMP%]   .alert-wrapper[_ngcontent-%COMP%]{border-radius:15px;box-shadow:0 4px 16px #0003}.login-success-alert[_ngcontent-%COMP%]   .alert-head[_ngcontent-%COMP%]{padding-bottom:10px}.login-success-alert[_ngcontent-%COMP%]   .alert-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;color:#5cb85c}.login-success-alert[_ngcontent-%COMP%]   .alert-message[_ngcontent-%COMP%]{font-size:1rem;color:#333}.login-success-alert[_ngcontent-%COMP%]   .alert-button[_ngcontent-%COMP%]{color:#3880ff;font-weight:500}"]})}}return s})();export{ae as LoginPage};
